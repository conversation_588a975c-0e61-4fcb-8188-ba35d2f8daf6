{"name": "sendMessage", "fromClz": "com.aliyun.gts.yunqiao.SampleController", "fromClzAnnotations": [{"type": "org.springframework.web.bind.annotation.RestController"}, {"type": "lombok.extern.slf4j.Slf4j"}], "annotations": [{"type": "org.springframework.web.bind.annotation.GetMapping", "properties": {"value": ["/sample/send/message"]}}], "returnType": {"type": "com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse", "name": "com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse", "parameterizedTyps": ["java.lang.String"], "typeParameters": ["T"], "parameteredArguments": [{"type": "java.lang.String"}], "fileds": [{"name": "SUCCESS_MSG", "type": "java.lang.String"}, {"name": "requestId", "type": "java.lang.String", "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "微服务中心相关的请求id", "example": "my-center-xxxxxxx"}}]}, {"name": "code", "type": "java.lang.String", "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "业务错误码，如101，-95", "example": "101"}}]}, {"name": "message", "type": "java.lang.String", "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "额外消息"}}]}, {"name": "success", "type": "java.lang.Bo<PERSON>an", "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "是否成功"}}]}, {"name": "meta", "type": "java.util.Map<java.lang.String, java.lang.Object>", "typeRef": {"type": "java.util.Map", "name": "java.util.Map", "parameterizedTyps": ["java.lang.String", "java.lang.Object"], "typeParameters": ["K", "V"], "parameteredArguments": [{"type": "java.lang.String"}, {"type": "java.lang.Object"}]}, "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "一些meta信息，例如需要crsf的token"}}], "parameteredArguments": [{"type": "java.lang.String"}, {"type": "java.lang.Object"}], "typeParameters": ["K", "V"]}, {"name": "Data", "type": "T", "isTypeVariable": true, "typeVariableIndex": 0, "typeRef": {"type": "java.lang.String", "name": "java.lang.String", "superType": "java.lang.Object"}, "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "实际数据，一般来说是DTO"}}]}], "annotations": [{"type": "io.swagger.annotations.ApiModel", "properties": {"value": "标准返回"}}], "superType": "java.lang.Object"}, "signatureMd5": "bcd8b0c2eb1fce714eab6cef0d771acc", "requestMapping": {"paths": ["/sample/send/message"], "methods": ["GET"], "mthPaths": ["/sample/send/message"]}, "public": false}