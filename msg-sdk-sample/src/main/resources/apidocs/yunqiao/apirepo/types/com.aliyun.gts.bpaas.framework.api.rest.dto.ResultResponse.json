{"type": "com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse", "name": "com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse", "typeParameters": ["T"], "fileds": [{"name": "SUCCESS_MSG", "type": "java.lang.String"}, {"name": "requestId", "type": "java.lang.String", "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "微服务中心相关的请求id", "example": "my-center-xxxxxxx"}}]}, {"name": "code", "type": "java.lang.String", "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "业务错误码，如101，-95", "example": "101"}}]}, {"name": "message", "type": "java.lang.String", "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "额外消息"}}]}, {"name": "success", "type": "java.lang.Bo<PERSON>an", "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "是否成功"}}]}, {"name": "meta", "type": "java.util.Map<java.lang.String, java.lang.Object>", "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "一些meta信息，例如需要crsf的token"}}], "parameteredArguments": [{"type": "java.lang.String"}, {"type": "java.lang.Object"}], "typeParameters": ["K", "V"]}, {"name": "Data", "type": "T", "isTypeVariable": true, "typeVariableIndex": 0, "annotations": [{"type": "io.swagger.annotations.ApiModelProperty", "properties": {"value": "实际数据，一般来说是DTO"}}]}], "annotations": [{"type": "io.swagger.annotations.ApiModel", "properties": {"value": "标准返回"}}], "superType": "java.lang.Object"}