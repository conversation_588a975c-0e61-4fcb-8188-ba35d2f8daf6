package com.aliyun.gts.yunqiao;

import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.aliyun.gts.utc.api.facade.feign.message.MessageSendApi;
import com.aliyun.gts.utc.api.facade.request.CommonMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.message.MessageSendRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: yaohao.yh
 * @date: 2023/9/4
 */
@RestController
@Slf4j
public class SampleController {
    @Autowired
    private MessageSendApi messageSendApi;

    @GetMapping("/sample/send/message")
    public ResultResponse<String> sendMessage() {
//        CommonMessageSendRequest request = new CommonMessageSendRequest();
//        request.setMessageType("email");
//        request.setTo("<EMAIL>");
//        request.setSubject("Hello");
//        request.setContent("world!!");
//        try {
//            messageSendApi.sendMessage(request);
//        } catch (Exception e) {
//            log.error("请求失败");
//            return ResultResponse.errorResult(e.getMessage());
//        }
        return ResultResponse.succResult(null);
    }
}
