package com.aliyun.gts.yunqiao.message.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.gts.yunqiao.message.boot.MessageProperties;
import com.aliyun.gts.yunqiao.message.request.MailMessageRequest;
import com.aliyun.gts.yunqiao.message.request.SMSMessageRequest;
import com.aliyun.gts.yunqiao.message.util.MailSenderServiceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;

import javax.mail.internet.MimeMessage;

/**
 * Created by 鸿度 on 2023-01-31 11:05.
 */
@Slf4j
public class MessageService {

    private final MessageProperties messageProperties;
    private final TemplateService templateService;
    private final JavaMailSender javaMailSender;

    private final Client smsClient;

    public MessageService(MessageProperties messageProperties, TemplateService templateService, JavaMailSender javaMailSender, Client smsClient) {
        this.messageProperties = messageProperties;
        this.templateService = templateService;
        this.javaMailSender = javaMailSender;
        this.smsClient = smsClient;
    }


    public boolean sendMailMessage(MailMessageRequest req) {
        try {
            templateService.renderTplContent(req);
            if (req.getFrom() == null || req.getFrom().isEmpty()) {
                req.setFrom(messageProperties.getMail().getUsername());
            }
            MimeMessage mimeMessage = MailSenderServiceUtils.buildMimeMessage(req, javaMailSender);
            javaMailSender.send(mimeMessage);
            return true;
        } catch (Exception e) {
            log.error("fail to send mail message", e);
        }
        return false;
    }

    public boolean sendSMSMessage(SMSMessageRequest req) {
        SendSmsRequest sendSmsRequest = new SendSmsRequest();
        sendSmsRequest.setPhoneNumbers(req.getPhoneNum());
        sendSmsRequest.setTemplateCode(req.getTemplateCode());
        sendSmsRequest.setTemplateParam(JSON.toJSONString(req.getExtParams()));
        sendSmsRequest.setSignName(messageProperties.getSms().getSign());
        log.info("send sms to " + JSON.toJSONString(sendSmsRequest));
        SendSmsResponse response;
        try {
            response = smsClient.sendSms(sendSmsRequest);
        } catch (Exception e) {
            log.error("fail to send sms message", e);
            return false;
        }
        String code = response.getBody().getCode();
        if (!"OK".equalsIgnoreCase(code)) {
            return true;
        } else {
            log.error("fail to send sms message, response: {}", JSON.toJSONString(response));
            return false;
        }
    }

}