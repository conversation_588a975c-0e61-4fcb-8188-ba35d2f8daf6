package com.aliyun.gts.yunqiao.message.boot;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.gts.yunqiao.message.service.MessageService;
import com.aliyun.gts.yunqiao.message.service.TemplateService;
import com.aliyun.gts.yunqiao.message.util.MailSenderServiceUtils;
import com.aliyun.teaopenapi.models.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

@SuppressWarnings("SpringJavaAutowiringInspection")
@Configuration
@EnableConfigurationProperties(MessageProperties.class)
@Slf4j
public class MessageAutoConfiguration {

    private final MessageProperties messageProperties;

    MessageAutoConfiguration(MessageProperties messageProperties) {
        this.messageProperties = messageProperties;
    }

    @Bean
    @ConditionalOnMissingBean(MessageService.class)
    @ConditionalOnProperty(name = "yunqiao.message.enabled", havingValue = "true")
    public MessageService messageSenderService(TemplateService templateService,
                                               JavaMailSender javaMailSender,
                                               Client smsClient) {
        return new MessageService(messageProperties, templateService, javaMailSender, smsClient);
    }

    @Bean
    @ConditionalOnMissingBean(TemplateService.class)
    public TemplateService templateService() {
        return new TemplateService();
    }

    @Bean
    @ConditionalOnMissingBean
    public JavaMailSenderImpl mailSender() {
        JavaMailSenderImpl sender = new JavaMailSenderImpl();
        MailSenderServiceUtils.applyProperties(sender, messageProperties.getMail());
        return sender;
    }


    @Bean
    @ConditionalOnMissingBean
    public Client buildSMSClient() throws Exception {
        SMSProperties smsProperties = messageProperties.getSms();
        Config config = new Config()
                // 您的AccessKey ID
                .setAccessKeyId(smsProperties.getAccessKey())
                // 您的AccessKey Secret
                .setAccessKeySecret(smsProperties.getAccessSecret());
        // 访问的域名
        config.endpoint = smsProperties.getEndpoint();
        return new Client(config);
    }
}