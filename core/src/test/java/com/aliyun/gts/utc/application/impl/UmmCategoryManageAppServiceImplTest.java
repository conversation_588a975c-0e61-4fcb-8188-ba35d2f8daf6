package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmCategoryDTO;
import com.aliyun.gts.utc.api.facade.request.ummcategory.CreateUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.request.ummcategory.PageQueryUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.request.ummcategory.UpdateUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.response.ummcategory.*;
import com.aliyun.gts.utc.core.domain.UmmCategory;
import com.aliyun.gts.utc.core.repository.UmmCategoryRepository;
import com.aliyun.gts.utc.core.repository.UmmMessageRepository;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:14:03
 */
public class UmmCategoryManageAppServiceImplTest {
    @Mock
    UmmCategoryRepository ummCategoryRepository;
    @Mock
    UmmTemplateRepository ummTemplateRepository;
    @Mock
    UmmMessageRepository ummMessageRepository;
    @InjectMocks
    UmmCategoryManageAppServiceImpl ummCategoryManageAppServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmCategory() {
        when(ummCategoryRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());
        when(ummCategoryRepository.add(any())).thenReturn(new UmmCategory());

        CreateUmmCategoryRequest request = new CreateUmmCategoryRequest();
        UmmCategoryDTO ummCategoryDTO = new UmmCategoryDTO();
        ummCategoryDTO.setCategoryName("<categoryName>");
        ummCategoryDTO.setCategoryCode("<categoryCode>");
        request.setUmmCategoryDTO(ummCategoryDTO);
        CreateUmmCategoryResponse result = ummCategoryManageAppServiceImpl.createUmmCategory(request);
        Assert.assertNotNull(result.getUmmCategoryDTO());
    }

    @Test
    public void testQueryUmmCategory() {
        when(ummCategoryRepository.getById(anyLong())).thenReturn(new UmmCategory());

        QueryUmmCategoryResponse result = ummCategoryManageAppServiceImpl.queryUmmCategory(Long.valueOf(1));
        Assert.assertNotNull(result.getUmmCategoryDTO());
    }

    @Test
    public void testPageQueryUmmCategory() {
        when(ummCategoryRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());

        PageQueryUmmCategoryResponse result = ummCategoryManageAppServiceImpl.pageQueryUmmCategory(new PageQueryUmmCategoryRequest(new UmmCategoryDTO()));
        Assert.assertEquals(0,result.getList().size());
    }

    @Test
    public void testDeleteUmmCategory() {
        when(ummCategoryRepository.deleteById(anyLong())).thenReturn(true);
        when(ummCategoryRepository.getByParentId(anyLong())).thenReturn(null);
        when(ummTemplateRepository.getByRange(anyInt(), anyInt(), any())).thenReturn(new Page<>());
        when(ummMessageRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());

        DeleteUmmCategoryResponse result = ummCategoryManageAppServiceImpl.deleteUmmCategory(1L);
        Assert.assertTrue(result.getIsDelete());
    }

    @Test
    public void testUpdateUmmCategory() {
        when(ummCategoryRepository.update(any())).thenReturn(true);
        UpdateUmmCategoryRequest request = new UpdateUmmCategoryRequest();
        request.setUmmCategoryDTO(new UmmCategoryDTO());
        UpdateUmmCategoryResponse result = ummCategoryManageAppServiceImpl.updateUmmCategory(request);
        Assert.assertTrue(result.getIsUpdate());
    }

    @Test
    public void testListAll() {
        when(ummCategoryRepository.getByParentId(anyLong())).thenReturn(Collections.emptyList());

        AllUmmCategoryResponse result = ummCategoryManageAppServiceImpl.listAll();
        Assert.assertNotNull(result.getAllUmmCategoryDTO());
    }
}