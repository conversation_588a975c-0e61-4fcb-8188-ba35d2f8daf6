package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.request.parameter.PageQueryMessageParameterRequest;
import com.aliyun.gts.utc.api.facade.response.parameter.PageQueryMessageParameterResponse;
import com.aliyun.gts.utc.core.repository.UmmMsgParameterRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:18:40
 */
public class MessageParameterManageServiceImplTest {
    @Mock
    UmmMsgParameterRepository ummMsgParameterRepository;
    @InjectMocks
    MessageParameterManageServiceImpl messageParameterManageServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPageQuery() {
        when(ummMsgParameterRepository.getPageQuery(anyInt(), anyInt(), any())).thenReturn(new Page<>());

        Page<PageQueryMessageParameterResponse> result = messageParameterManageServiceImpl.pageQuery(new PageQueryMessageParameterRequest());
        Assert.assertNotNull(result);
    }
}