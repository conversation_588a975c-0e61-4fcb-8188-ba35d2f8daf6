package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgChannelDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.CreateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.PageQueryUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.UpdateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgchannel.*;
import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import com.aliyun.gts.utc.core.repository.UmmMsgChannelRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:19:46
 */
public class UmmMsgChannelManageCoreServiceImplTest {
    @Mock
    UmmMsgChannelRepository ummMsgChannelRepository;
    @InjectMocks
    UmmMsgChannelManageCoreServiceImpl ummMsgChannelManageCoreServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmMsgChannel() {
        when(ummMsgChannelRepository.add(any())).thenReturn(new UmmMsgChannel());
        CreateUmmMsgChannelRequest request = new CreateUmmMsgChannelRequest();
        request.setUmmMsgChannelDTO(new UmmMsgChannelDTO());
        CreateUmmMsgChannelResponse result = ummMsgChannelManageCoreServiceImpl.createUmmMsgChannel(request);
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgChannel() {
        when(ummMsgChannelRepository.getById(anyLong())).thenReturn(new UmmMsgChannel());

        QueryUmmMsgChannelResponse result = ummMsgChannelManageCoreServiceImpl.queryUmmMsgChannel(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgChannelByMsgId() {
        when(ummMsgChannelRepository.getByMsgId(anyLong())).thenReturn(Arrays.<UmmMsgChannel>asList(new UmmMsgChannel()));

        List<UmmMsgChannel> result = ummMsgChannelManageCoreServiceImpl.queryUmmMsgChannelByMsgId(Long.valueOf(1));
        Assert.assertEquals(Arrays.asList(new UmmMsgChannel()), result);
    }

    @Test
    public void testPageQueryUmmMsgChannel() {
        when(ummMsgChannelRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());

        PageQueryUmmMsgChannelResponse result = ummMsgChannelManageCoreServiceImpl.pageQueryUmmMsgChannel(new PageQueryUmmMsgChannelRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteUmmMsgChannel() {
        when(ummMsgChannelRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMsgChannelResponse result = ummMsgChannelManageCoreServiceImpl.deleteUmmMsgChannel(Long.valueOf(1));
        Assert.assertTrue(result.getIsDelete());
    }

    @Test
    public void testUpdateUmmMsgChannel() {
        when(ummMsgChannelRepository.update(any())).thenReturn(true);

        UpdateUmmMsgChannelResponse result = ummMsgChannelManageCoreServiceImpl.updateUmmMsgChannel(new UpdateUmmMsgChannelRequest());
        Assert.assertNotNull(result);
    }
}