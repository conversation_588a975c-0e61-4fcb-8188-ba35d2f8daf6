package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgParameterDTO;
import com.aliyun.gts.utc.api.facade.request.ummmsgparameter.CreateUmmMsgParameterRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgparameter.PageQueryUmmMsgParameterRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgparameter.UpdateUmmMsgParameterRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgparameter.*;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import com.aliyun.gts.utc.core.repository.UmmMsgParameterRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:14:42
 */
public class UmmMsgParameterManageAppServiceImplTest {
    @Mock
    UmmMsgParameterRepository ummMsgParameterRepository;
    @InjectMocks
    UmmMsgParameterManageAppServiceImpl ummMsgParameterManageAppServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmMsgParameter() {
        when(ummMsgParameterRepository.add(any())).thenReturn(new UmmMsgParameter());
        CreateUmmMsgParameterRequest request = new CreateUmmMsgParameterRequest();
        request.setUmmMsgParameterDTO(new UmmMsgParameterDTO());
        CreateUmmMsgParameterResponse result = ummMsgParameterManageAppServiceImpl.createUmmMsgParameter(request);
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgParameter() {
        when(ummMsgParameterRepository.getById(anyLong())).thenReturn(new UmmMsgParameter());

        QueryUmmMsgParameterResponse result = ummMsgParameterManageAppServiceImpl.queryUmmMsgParameter(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testPageQueryUmmMsgParameter() {
        when(ummMsgParameterRepository.getByRange(anyInt(), anyInt(), any())).thenReturn(new Page<>());

        PageQueryUmmMsgParameterResponse result = ummMsgParameterManageAppServiceImpl.pageQueryUmmMsgParameter(new PageQueryUmmMsgParameterRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteUmmMsgParameter() {
        when(ummMsgParameterRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMsgParameterResponse result = ummMsgParameterManageAppServiceImpl.deleteUmmMsgParameter(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testUpdateUmmMsgParameter() {
        when(ummMsgParameterRepository.update(any())).thenReturn(true);

        UpdateUmmMsgParameterResponse result = ummMsgParameterManageAppServiceImpl.updateUmmMsgParameter(new UpdateUmmMsgParameterRequest());
        Assert.assertNotNull(result);
    }
}