package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.core.repository.UmmMsgParameterRepository;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgParameterDO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:19:58
 */
public class UmmMsgParameterServiceImplTest {
    @Mock
    UmmMsgParameterRepository ummMsgParameterRepository;
    @InjectMocks
    UmmMsgParameterServiceImpl ummMsgParameterServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQuery() throws Exception {
        when(ummMsgParameterRepository.query()).thenReturn(Arrays.<UmmMsgParameterDO>asList(new UmmMsgParameterDO()));

        List<UmmMsgParameterDO> result = ummMsgParameterServiceImpl.query();
        Assert.assertEquals(Arrays.<UmmMsgParameterDO>asList(new UmmMsgParameterDO()), result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme