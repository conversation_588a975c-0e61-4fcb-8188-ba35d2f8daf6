package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendLogDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsendlog.PageQueryUmmMsgSendLogRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsendlog.UpdateUmmMsgSendLogRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsendlog.*;
import com.aliyun.gts.utc.core.domain.UmmMsgSendLog;
import com.aliyun.gts.utc.core.repository.UmmMsgSendLogRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:20:12
 */
public class UmmMsgSendLogManageCoreServiceImplTest {
    @Mock
    UmmMsgSendLogRepository ummMsgSendLogRepository;
    @InjectMocks
    UmmMsgSendLogManageCoreServiceImpl ummMsgSendLogManageCoreServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmMsgSendLog() {
        when(ummMsgSendLogRepository.add(any())).thenReturn(new UmmMsgSendLog());

        CreateUmmMsgSendLogResponse result = ummMsgSendLogManageCoreServiceImpl.createUmmMsgSendLog(new UmmMsgSendLogDTO());
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgSendLog() {
        when(ummMsgSendLogRepository.getById(anyLong())).thenReturn(new UmmMsgSendLog());

        QueryUmmMsgSendLogResponse result = ummMsgSendLogManageCoreServiceImpl.queryUmmMsgSendLog(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testBatchCreateUmmMsgSendLog() {
        when(ummMsgSendLogRepository.batchCreateUmmMsgSendLog(any())).thenReturn(Boolean.TRUE);

        Boolean result = ummMsgSendLogManageCoreServiceImpl.batchCreateUmmMsgSendLog(Arrays.<UmmMsgSendLogDTO>asList(new UmmMsgSendLogDTO()));
        Assert.assertEquals(Boolean.TRUE, result);
    }

    @Test
    public void testPageQueryUmmMsgSendLog() {
        when(ummMsgSendLogRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());

        PageQueryUmmMsgSendLogResponse result = ummMsgSendLogManageCoreServiceImpl.pageQueryUmmMsgSendLog(new PageQueryUmmMsgSendLogRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteUmmMsgSendLog() {
        when(ummMsgSendLogRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMsgSendLogResponse result = ummMsgSendLogManageCoreServiceImpl.deleteUmmMsgSendLog(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testUpdateUmmMsgSendLog() {
        when(ummMsgSendLogRepository.update(any())).thenReturn(true);

        UpdateUmmMsgSendLogResponse result = ummMsgSendLogManageCoreServiceImpl.updateUmmMsgSendLog(new UpdateUmmMsgSendLogRequest());
        Assert.assertNotNull(result);
    }
}
