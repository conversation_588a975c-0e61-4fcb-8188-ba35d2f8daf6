package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.request.MailMessageSendRequest;
import com.aliyun.gts.utc.integration.client.mail.MailClient;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * Created by 鸿度 on 2022-12-01 16:18:22
 */
public class MailServiceImplTest {
    @Mock
    MailClient mailClient;
    @InjectMocks
    MailServiceImpl mailServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

}