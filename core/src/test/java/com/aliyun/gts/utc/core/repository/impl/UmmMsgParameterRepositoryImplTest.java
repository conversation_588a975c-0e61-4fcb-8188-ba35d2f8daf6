package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.api.facade.request.parameter.PageQueryMessageParameterRequest;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import com.aliyun.gts.utc.dal.dao.UmmMsgParameterMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgParameterDO;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;


public class UmmMsgParameterRepositoryImplTest {
    @Mock
    UmmMsgParameterMapper ummMsgParameterMapper;
    @InjectMocks
    UmmMsgParameterRepositoryImpl ummMsgParameterRepositoryImpl;

    @Before
    public void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), UmmMsgParameterDO.class);
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetById() {
        when(ummMsgParameterMapper.selectById(anyLong())).thenReturn(new UmmMsgParameterDO());
        UmmMsgParameter result = ummMsgParameterRepositoryImpl.getById(1L);
        Assert.assertEquals(new UmmMsgParameter(), result);
    }

    @Test
    public void testGetByRange() {
        Page<UmmMsgParameterDO> parameterDOPage = new Page<>();
        parameterDOPage.setTotal(0L);
        parameterDOPage.setRecords(Collections.emptyList());
        when(ummMsgParameterMapper.selectPage(any(), any())).thenReturn(parameterDOPage);
        IPage<UmmMsgParameter> result =
                ummMsgParameterRepositoryImpl.getByRange(1, 1,
                        new UmmMsgParameter());
        Assert.assertEquals(0, result.getTotal());
    }

    @Test
    public void testAdd() {
        UmmMsgParameter result = ummMsgParameterRepositoryImpl.add(new UmmMsgParameter());
        Assert.assertEquals(new UmmMsgParameter(), result);
    }

    @Test
    public void testUpdate() {
        when(ummMsgParameterMapper.selectById(any())).thenReturn(new UmmMsgParameterDO());
        when(ummMsgParameterMapper.updateById(any())).thenReturn(1);
        boolean result = ummMsgParameterRepositoryImpl.update(new UmmMsgParameter());
        Assert.assertTrue(result);
    }

    @Test
    public void testDeleteById() {
        when(ummMsgParameterMapper.deleteById(anyLong())).thenReturn(1);
        boolean result = ummMsgParameterRepositoryImpl.deleteById(1L);
        Assert.assertTrue(result);
    }

    @Test
    public void testGetAll() {
        List<UmmMsgParameter> result = ummMsgParameterRepositoryImpl.getAll();
        Assert.assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetPageQuery() {
        Page<UmmMsgParameterDO> parameterDOPage = new Page<>();
        parameterDOPage.setTotal(0L);
        parameterDOPage.setRecords(Collections.emptyList());
        when(ummMsgParameterMapper.selectPage(any(),any())).thenReturn(parameterDOPage);
        Page<UmmMsgParameter> result =
                ummMsgParameterRepositoryImpl.getPageQuery(1, 1,
                        new PageQueryMessageParameterRequest());
        Assert.assertEquals(0, result.getTotal());
    }
}