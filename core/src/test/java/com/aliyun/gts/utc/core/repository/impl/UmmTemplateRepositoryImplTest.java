package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.dal.dao.UmmTemplateMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmTemplateDO;
import com.aliyun.gts.utc.dal.dto.MessageTemplateQueryDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageTemplateDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.mockito.Mockito.*;

public class UmmTemplateRepositoryImplTest {
    @Mock
    UmmTemplateMapper ummTemplateMapper;
    @InjectMocks
    UmmTemplateRepositoryImpl ummTemplateRepositoryImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetById() throws Exception {
        UmmTemplateDO ummTemplateDO = new UmmTemplateDO();
        ummTemplateDO.setId(1L);
        when(ummTemplateMapper.selectOne(any())).thenReturn(ummTemplateDO);
        UmmTemplate result = ummTemplateRepositoryImpl.getById(1L);
        Assert.assertEquals(Long.valueOf(1), result.getId());
    }

    @Test
    public void testGetByRange() throws Exception {
        Page<UmmTemplateDO> page = new Page<>();
        page.setRecords(Collections.emptyList());
        page.setTotal(0);
        when(ummTemplateMapper.selectPage(any(), any())).thenReturn(page);
        IPage<UmmTemplate> result = ummTemplateRepositoryImpl.getByRange(1, 1
                , new UmmTemplate());
        Assert.assertEquals(0, result.getTotal());
    }

    @Test
    public void testAdd() throws Exception {
        UmmTemplate result = ummTemplateRepositoryImpl.add(new UmmTemplate());
        Assert.assertEquals(new UmmTemplate(), result);
    }

    @Test
    public void testUpdate() throws Exception {
        when(ummTemplateMapper.selectOne(any())).thenReturn(new UmmTemplateDO());
        when(ummTemplateMapper.updateById(any())).thenReturn(0);
        boolean result = ummTemplateRepositoryImpl.update(new UmmTemplate());
        Assert.assertTrue(result);
    }

    @Test
    public void testDeleteById() throws Exception {
        UmmTemplateDO ummTemplateDO = new UmmTemplateDO();
        ummTemplateDO.setId(1L);
        when(ummTemplateMapper.selectOne(any())).thenReturn(ummTemplateDO);
        when(ummTemplateMapper.deleteById(anyLong())).thenReturn(1);
        boolean result = ummTemplateRepositoryImpl.deleteById(1L);
        Assert.assertTrue(result);
    }

    @Test
    public void testBatchDelete() throws Exception {
        when(ummTemplateMapper.deleteBatchIds(any())).thenReturn(1);
        boolean result = ummTemplateRepositoryImpl.batchDelete(Collections.<Long>singletonList(Long.valueOf(1)));
        Assert.assertTrue(result);
    }

    @Test
    public void testPageQuery() throws Exception {
        Page<PageMessageTemplateDTO> page = new Page<>();
        page.setRecords(Collections.emptyList());
        page.setTotal(0);
        when(ummTemplateMapper.pageQuery(any(), any())).thenReturn(page);

        Page<PageMessageTemplateDTO> result = ummTemplateRepositoryImpl.pageQuery(null, new MessageTemplateQueryDTO());
        Assert.assertEquals(0, result.getTotal());
    }

    @Test
    public void testCountTitle() throws Exception {
        int result = ummTemplateRepositoryImpl.countTitle("msgTitle", 1L);
        Assert.assertEquals(0, result);
    }
}