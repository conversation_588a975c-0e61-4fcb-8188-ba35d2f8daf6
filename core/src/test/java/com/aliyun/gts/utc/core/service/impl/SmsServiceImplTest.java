package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.request.SmsMessageSendRequest;
import com.aliyun.gts.utc.integration.client.alisms.AliSmsClientImpl;
import com.aliyun.gts.utc.integration.client.alisms.SmsClient;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:19:22
 */
public class SmsServiceImplTest {
    @Mock
    SmsClient smsClient;
    @InjectMocks
    SmsServiceImpl smsServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSendSms() {
        smsServiceImpl.sendSms(new SmsMessageSendRequest("templateCode", new HashMap<String, String>() {{
            put("String", "String");
        }}, "phoneNum"));
    }

    public static void main(String[] args) throws Exception{
        SmsClient smsClient = new AliSmsClientImpl("ak", "sk", "阿里云短信测试");
        Map<String, String> extParams = new LinkedHashMap<>();
        extParams.put("code","888888");
        smsClient.sendSms("SMS_154950909", extParams, "xxxxxx");
    }
}