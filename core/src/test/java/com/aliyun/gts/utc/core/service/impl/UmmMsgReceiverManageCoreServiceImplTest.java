package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgReceiverDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgreceiver.CreateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgreceiver.PageQueryUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgreceiver.UpdateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgreceiver.*;
import com.aliyun.gts.utc.core.domain.UmmMsgReceiver;
import com.aliyun.gts.utc.core.repository.UmmMsgReceiverRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:20:05
 */
public class UmmMsgReceiverManageCoreServiceImplTest {
    @Mock
    UmmMsgReceiverRepository ummMsgReceiverRepository;
    @InjectMocks
    UmmMsgReceiverManageCoreServiceImpl ummMsgReceiverManageCoreServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmMsgReceiver() {
        when(ummMsgReceiverRepository.add(any())).thenReturn(new UmmMsgReceiver());
        CreateUmmMsgReceiverRequest request = new CreateUmmMsgReceiverRequest();
        UmmMsgReceiverDTO ummMsgReceiverDTO = new UmmMsgReceiverDTO();
        request.setUmmMsgReceiverDTO(ummMsgReceiverDTO);
        CreateUmmMsgReceiverResponse result = ummMsgReceiverManageCoreServiceImpl.createUmmMsgReceiver(request);
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgReceiver() {
        when(ummMsgReceiverRepository.getById(anyLong())).thenReturn(new UmmMsgReceiver());

        QueryUmmMsgReceiverResponse result = ummMsgReceiverManageCoreServiceImpl.queryUmmMsgReceiver(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgReceiverByMsgId() {
        when(ummMsgReceiverRepository.getByMsgId(anyLong())).thenReturn(new UmmMsgReceiver());

        QueryUmmMsgReceiverResponse result = ummMsgReceiverManageCoreServiceImpl.queryUmmMsgReceiverByMsgId(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testPageQueryUmmMsgReceiver() {
        when(ummMsgReceiverRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());

        PageQueryUmmMsgReceiverResponse result = ummMsgReceiverManageCoreServiceImpl.pageQueryUmmMsgReceiver(new PageQueryUmmMsgReceiverRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteUmmMsgReceiver() {
        when(ummMsgReceiverRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMsgReceiverResponse result = ummMsgReceiverManageCoreServiceImpl.deleteUmmMsgReceiver(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testUpdateUmmMsgReceiver() {
        when(ummMsgReceiverRepository.update(any())).thenReturn(true);

        UpdateUmmMsgReceiverResponse result = ummMsgReceiverManageCoreServiceImpl.updateUmmMsgReceiver(new UpdateUmmMsgReceiverRequest());
        Assert.assertNotNull(result);
    }
}