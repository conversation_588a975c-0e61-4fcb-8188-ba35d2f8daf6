package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.aliyun.gts.utc.api.facade.request.NoticeRollbackRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:19:15
 */
public class NoticeRollbackServiceImplTest {
    @Mock
    ApplicationContext context;
    @InjectMocks
    NoticeRollbackServiceImpl noticeRollbackServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testNoticeRollback() {
        ResultResponse result = noticeRollbackServiceImpl.noticeRollback(new NoticeRollbackRequest());
        Assert.assertNotNull(result);
    }
}