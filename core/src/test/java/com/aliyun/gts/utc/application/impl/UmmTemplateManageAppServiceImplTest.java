package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmTemplateDTO;
import com.aliyun.gts.utc.api.facade.request.ummtemplate.CreateUmmTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.ummtemplate.PageQueryUmmTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.ummtemplate.UpdateUmmTemplateRequest;
import com.aliyun.gts.utc.api.facade.response.ummtemplate.*;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.repository.UmmCategoryRepository;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:15:12
 */
public class UmmTemplateManageAppServiceImplTest {
    @Mock
    UmmTemplateRepository ummTemplateRepository;
    @Mock
    UmmCategoryRepository ummCategoryRepository;
    @InjectMocks
    UmmTemplateManageAppServiceImpl ummTemplateManageAppServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmTemplate() {
        when(ummTemplateRepository.add(any())).thenReturn(new UmmTemplate());
        doNothing().when(ummCategoryRepository).checkCategoryExistInCurrentTenantCode(any());
        CreateUmmTemplateRequest request = new CreateUmmTemplateRequest();
        request.setUmmTemplateDTO(new UmmTemplateDTO());
        CreateUmmTemplateResponse result = ummTemplateManageAppServiceImpl.createUmmTemplate(request);
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmTemplate() {
        when(ummTemplateRepository.getById(anyLong())).thenReturn(new UmmTemplate());

        QueryUmmTemplateResponse result = ummTemplateManageAppServiceImpl.queryUmmTemplate(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmTemplateByCode() {
        when(ummTemplateRepository.getByCode(anyString())).thenReturn(new UmmTemplate());

        QueryUmmTemplateResponse result = ummTemplateManageAppServiceImpl.queryUmmTemplateByCode("code");
        Assert.assertNotNull(result);
    }

    @Test
    public void testPageQueryUmmTemplate() {
        when(ummTemplateRepository.getByRange(anyInt(), anyInt(), any())).thenReturn(new Page<>());

        PageQueryUmmTemplateResponse result = ummTemplateManageAppServiceImpl.pageQueryUmmTemplate(new PageQueryUmmTemplateRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteUmmTemplate() {
        when(ummTemplateRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmTemplateResponse result = ummTemplateManageAppServiceImpl.deleteUmmTemplate(Long.valueOf(1));
        Assert.assertTrue(result.getIsDelete());
    }

    @Test
    public void testUpdateUmmTemplate() {
        when(ummTemplateRepository.update(any())).thenReturn(true);

        UpdateUmmTemplateResponse result = ummTemplateManageAppServiceImpl.updateUmmTemplate(new UpdateUmmTemplateRequest());
        Assert.assertTrue(result.getIsUpdate());
    }

    @Test
    public void testQueryAllUmmTemplate() {
        when(ummTemplateRepository.getByRange(anyInt(), anyInt(), any())).thenReturn(new Page<>());
        PageQueryUmmTemplateResponse result = ummTemplateManageAppServiceImpl.queryAllUmmTemplate();
        Assert.assertNotNull(result);
    }
}