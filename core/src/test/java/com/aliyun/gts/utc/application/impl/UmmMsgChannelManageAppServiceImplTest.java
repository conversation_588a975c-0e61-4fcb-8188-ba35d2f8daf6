package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgChannelDTO;
import com.aliyun.gts.utc.api.facade.request.ummmsgchannel.CreateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgchannel.PageQueryUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgchannel.UpdateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgchannel.*;
import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import com.aliyun.gts.utc.core.repository.UmmMsgChannelRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:14:31
 */
public class UmmMsgChannelManageAppServiceImplTest {
    @Mock
    UmmMsgChannelRepository ummMsgChannelRepository;
    @InjectMocks
    UmmMsgChannelManageAppServiceImpl ummMsgChannelManageAppServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmMsgChannel() {
        when(ummMsgChannelRepository.add(any())).thenReturn(new UmmMsgChannel());
        CreateUmmMsgChannelRequest request = new CreateUmmMsgChannelRequest();
        request.setUmmMsgChannelDTO(new UmmMsgChannelDTO());
        CreateUmmMsgChannelResponse result = ummMsgChannelManageAppServiceImpl.createUmmMsgChannel(request);
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgChannel() {
        when(ummMsgChannelRepository.getById(anyLong())).thenReturn(new UmmMsgChannel());

        QueryUmmMsgChannelResponse result = ummMsgChannelManageAppServiceImpl.queryUmmMsgChannel(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testPageQueryUmmMsgChannel() {
        when(ummMsgChannelRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());
        PageQueryUmmMsgChannelResponse result = ummMsgChannelManageAppServiceImpl.pageQueryUmmMsgChannel(new PageQueryUmmMsgChannelRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteUmmMsgChannel() {
        when(ummMsgChannelRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMsgChannelResponse result = ummMsgChannelManageAppServiceImpl.deleteUmmMsgChannel(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testUpdateUmmMsgChannel() {
        when(ummMsgChannelRepository.update(any())).thenReturn(true);

        UpdateUmmMsgChannelResponse result = ummMsgChannelManageAppServiceImpl.updateUmmMsgChannel(new UpdateUmmMsgChannelRequest());
        Assert.assertNotNull(result);
    }
}