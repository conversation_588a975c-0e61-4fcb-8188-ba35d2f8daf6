package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMessageDTO;
import com.aliyun.gts.utc.api.auto.dto.UmmMsgReceiverDTO;
import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.QueryUmmMessageResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.UpdateUmmMessageResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgreceiver.QueryUmmMsgReceiverResponse;
import com.aliyun.gts.utc.api.facade.request.SmsMessageSendRequest;
import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import com.aliyun.gts.utc.core.factory.ListenerChannelFactory;
import com.aliyun.gts.utc.core.service.*;
import com.aliyun.gts.utc.core.service.impl.DingWorkNoticeMsgListenerServiceImpl;
import com.aliyun.gts.utc.spi.UserServiceSpi;
import com.aliyun.gts.utc.spi.dto.User;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 14:13:52
 */
public class MessageSendAppServiceImplTest {
    @Mock
    UmmMsgSendManageCoreService ummMsgSendManageCoreService;
    @Mock
    UmmMessageManageCoreService ummMessageManageCoreService;
    @Mock
    UserServiceSpi userServiceSpi;
    @Mock
    ListenerChannelFactory channelFactory;
    @Mock
    UmmMsgReceiverManageCoreService ummMsgReceiverManageCoreService;
    @Mock
    UmmMsgChannelManageCoreService channelManageAppService;
    @Mock
    UmmMsgSendLogManageCoreService ummMsgSendLogManageCoreService;

    @Mock
    MailService mailService;

    @Mock
    SmsService smsService;

    @InjectMocks
    MessageSendAppServiceImpl messageSendAppServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testMessageSystemParse() {
        String result = MessageSendAppServiceImpl.messageSystemParse("msgContent", new User(), new HashMap<String, Object>() {{
            put("String", "rootMap");
        }});
        Assert.assertEquals("msgContent", result);
    }

    @Test
    public void testSendSms() {
        String result = messageSendAppServiceImpl.sendSms(new SmsMessageSendRequest("templateCode", new HashMap<String, String>() {{
            put("String", "String");
        }}, "phoneNum"));
        Assert.assertNull(result);
    }

    @Test
    public void testSendMessage() {
        long mockMsgId = System.currentTimeMillis();

        QueryUmmMessageResponse messageResponse = new QueryUmmMessageResponse();
        UmmMessageDTO ummMessageDTO = new UmmMessageDTO();
        messageResponse.setUmmMessageDTO(ummMessageDTO);
        when(ummMessageManageCoreService.queryUmmMessage(anyLong())).thenReturn(messageResponse);

        QueryUmmMsgReceiverResponse queryUmmMsgReceiverResponse = new QueryUmmMsgReceiverResponse();
        UmmMsgReceiverDTO ummMsgReceiverDTO = new UmmMsgReceiverDTO();
        ummMsgReceiverDTO.setMsgId(mockMsgId);
        queryUmmMsgReceiverResponse.setUmmMsgReceiverDTO(ummMsgReceiverDTO);
        when(ummMsgReceiverManageCoreService.queryUmmMsgReceiverByMsgId(anyLong())).thenReturn(queryUmmMsgReceiverResponse);


        UmmMsgChannel ummMsgChannel = new UmmMsgChannel();
        ummMsgChannel.setChannelId(2L);
        when(channelManageAppService.queryUmmMsgChannelByMsgId(anyLong())).thenReturn(Arrays.asList(ummMsgChannel));

        when(ummMsgSendManageCoreService.batchCreateUmmMsgSend(any())).thenReturn(Boolean.TRUE);
        when(ummMsgSendManageCoreService.batchUpdateUmmMsgSend(any())).thenReturn(Boolean.TRUE);
        when(ummMsgSendManageCoreService.queryUmmMsgSendByMsgId(anyString())).thenReturn(Arrays.<UmmMsgSendDTO>asList(new UmmMsgSendDTO()));
        when(ummMessageManageCoreService.updateStatus(anyLong(), anyInt())).thenReturn(true);
        when(ummMessageManageCoreService.updateUmmMessage(any())).thenReturn(new UpdateUmmMessageResponse());
        when(userServiceSpi.queryUser(any(String.class))).thenReturn(null);
        when(userServiceSpi.queryUser(any(String.class))).thenReturn(new User());
        when(channelFactory.getChannel(anyLong())).thenReturn(new DingWorkNoticeMsgListenerServiceImpl(null));
        when(ummMsgSendLogManageCoreService.batchCreateUmmMsgSendLog(any())).thenReturn(Boolean.TRUE);

        messageSendAppServiceImpl.sendMessage(mockMsgId, 1L, null,true);
    }
}
