package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgReceiverDTO;
import com.aliyun.gts.utc.api.facade.request.ummmsgreceiver.CreateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgreceiver.PageQueryUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgreceiver.UpdateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgreceiver.*;
import com.aliyun.gts.utc.core.domain.UmmMsgReceiver;
import com.aliyun.gts.utc.core.repository.UmmMsgReceiverRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:14:52
 */
public class UmmMsgReceiverManageAppServiceImplTest {
    @Mock
    UmmMsgReceiverRepository ummMsgReceiverRepository;
    @InjectMocks
    UmmMsgReceiverManageAppServiceImpl ummMsgReceiverManageAppServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmMsgReceiver() {
        when(ummMsgReceiverRepository.add(any())).thenReturn(new UmmMsgReceiver());

        CreateUmmMsgReceiverRequest request = new CreateUmmMsgReceiverRequest();
        request.setUmmMsgReceiverDTO(new UmmMsgReceiverDTO());
        CreateUmmMsgReceiverResponse result = ummMsgReceiverManageAppServiceImpl.createUmmMsgReceiver(request);
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgReceiver() {
        when(ummMsgReceiverRepository.getById(anyLong())).thenReturn(new UmmMsgReceiver());

        QueryUmmMsgReceiverResponse result = ummMsgReceiverManageAppServiceImpl.queryUmmMsgReceiver(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testPageQueryUmmMsgReceiver() {
        when(ummMsgReceiverRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());

        PageQueryUmmMsgReceiverResponse result = ummMsgReceiverManageAppServiceImpl.pageQueryUmmMsgReceiver(new PageQueryUmmMsgReceiverRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteUmmMsgReceiver() {
        when(ummMsgReceiverRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMsgReceiverResponse result = ummMsgReceiverManageAppServiceImpl.deleteUmmMsgReceiver(Long.valueOf(1));
        Assert.assertTrue(result.getIsDelete());
    }

    @Test
    public void testUpdateUmmMsgReceiver() {
        when(ummMsgReceiverRepository.update(any())).thenReturn(true);

        UpdateUmmMsgReceiverResponse result = ummMsgReceiverManageAppServiceImpl.updateUmmMsgReceiver(new UpdateUmmMsgReceiverRequest());
        Assert.assertNotNull(result);
    }
}