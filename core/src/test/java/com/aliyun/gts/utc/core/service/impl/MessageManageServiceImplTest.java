package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.request.message.MessageReplayRequest;
import com.aliyun.gts.utc.api.facade.request.message.PageQueryMessageSendResultRequest;
import com.aliyun.gts.utc.api.facade.response.message.PageQueryMessageSendResultResponse;
import com.aliyun.gts.utc.api.facade.response.message.QueryMessageResponse;
import com.aliyun.gts.utc.core.domain.*;
import com.aliyun.gts.utc.core.repository.*;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:18:31
 */
public class MessageManageServiceImplTest {
    @Mock
    UmmMessageRepository ummMessageRepository;
    @Mock
    UmmMsgSendRepository ummMsgSendRepository;
    @Mock
    UmmMsgChannelRepository ummMsgChannelRepository;
    @Mock
    UmmCategoryRepository ummCategoryRepository;
    @Mock
    UmmMsgReceiverRepository ummMsgReceiverRepository;
    @Mock
    UmmTemplateRepository ummTemplateRepository;
    @InjectMocks
    MessageManageServiceImpl messageManageServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        RuntimeContextUtil.setTenantCode("t1");
    }

    @Test
    public void testSendResultPage() {
        when(ummMsgSendRepository.resultPageQuery(any())).thenReturn(new Page<>());

        Page<PageQueryMessageSendResultResponse> result = messageManageServiceImpl.sendResultPage(new PageQueryMessageSendResultRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetDetail() {
        UmmMessage ummMessage = new UmmMessage();
        ummMessage.setId(1L);
        ummMessage.setSendMethod(1);
        ummMessage.setTenantCode(RuntimeContextUtil.getTenantCode());
        when(ummMessageRepository.getById(anyLong())).thenReturn(ummMessage);

        UmmMsgChannel channel = new UmmMsgChannel();
        channel.setChannelId(2L);
        when(ummMsgChannelRepository.getByMessage(anyLong())).thenReturn(channel);

        when(ummMsgSendRepository.getSendSuccessCount(anyLong())).thenReturn(0);
        when(ummMsgSendRepository.getSendCount(anyLong())).thenReturn(0);
        when(ummCategoryRepository.getById(anyLong())).thenReturn(new UmmCategory());
        when(ummMsgReceiverRepository.getByMsgId(anyLong())).thenReturn(new UmmMsgReceiver());
        when(ummTemplateRepository.getById(anyLong())).thenReturn(new UmmTemplate());

        QueryMessageResponse result = messageManageServiceImpl.getDetail(1L);
        Assert.assertTrue(1L==result.getId());
    }

    @Test
    public void testReplay() {
        when(ummMsgSendRepository.getById(anyLong())).thenReturn(new UmmMsgSend());
        MessageReplayRequest request = new MessageReplayRequest();
        List<Long> idList = new ArrayList<>();
        idList.add(1L);
        request.setIdList(idList);
    }
}