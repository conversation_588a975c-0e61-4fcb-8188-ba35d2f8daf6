package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.MockDataUtilTest;
import com.aliyun.gts.utc.api.dto.UmmMessageCreateDTO;
import com.aliyun.gts.utc.api.dto.UmmMessageUpdateDTO;
import com.aliyun.gts.utc.api.facade.request.message.MessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.CreateUmmMessageRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.DelUmmMessageRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.PageQueryUmmMessageRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.UpdateUmmMessageRequest;
import com.aliyun.gts.utc.api.facade.response.ummmessage.*;
import com.aliyun.gts.utc.application.MessageSendAppService;
import com.aliyun.gts.utc.core.domain.*;
import com.aliyun.gts.utc.core.factory.ListenerChannelFactory;
import com.aliyun.gts.utc.core.repository.*;
import com.aliyun.gts.utc.core.service.*;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.core.xxljob.ScheduleRegister;
import com.aliyun.gts.utc.integration.client.MessageClientTypeEnum;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.model.user.BaseUserInfoDTO;
import com.yunqiao.foura.service.sdk.service.UserContext;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:14:19
 */
public class UmmMessageManageAppServiceImplTest {
    @Mock
    UmmMessageRepository ummMessageRepository;
    @Mock
    UmmMsgChannelRepository ummMsgChannelRepository;
    @Mock
    UmmMsgReceiverRepository ummMsgReceiverRepository;
    @Mock
    UmmCategoryRepository ummCategoryRepository;
    @Mock
    UmmTemplateRepository ummTemplateRepository;

    @Mock
    MessageSendAppService messageSendAppService;

    UserContext userContext;

    @InjectMocks
    UmmMessageManageAppServiceImpl ummMessageManageAppServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        RuntimeContextUtil.setTenantCode("t1");
        userContext = new UserContext() {
            @Override
            public UserInfoDTO currentUserInfo(boolean force) {
                return MockDataUtilTest.buildUserInfoDTO();
            }
        };
    }

    @Test
    public void testCreateUmmMessage() {
        ReflectionTestUtils.setField(ummMessageManageAppServiceImpl, "userContext", userContext);
        when(ummMessageRepository.add(any())).thenReturn(new UmmMessage());
        when(ummMsgChannelRepository.add(any())).thenReturn(new UmmMsgChannel());
        when(ummMsgReceiverRepository.getByMsgId(anyLong())).thenReturn(new UmmMsgReceiver());
        when(ummMsgReceiverRepository.add(any())).thenReturn(new UmmMsgReceiver());
        when(ummMsgReceiverRepository.update(any())).thenReturn(true);
        when(ummTemplateRepository.getByCode(any())).thenReturn(MockDataUtilTest.buildMockTemplate());
        when(ummCategoryRepository.getByCode(any())).thenReturn(MockDataUtilTest.buildMockCategory());
        CreateUmmMessageRequest createUmmMessageRequest = new CreateUmmMessageRequest();
        createUmmMessageRequest.setUmmMessageDTO(new UmmMessageCreateDTO());
        CreateUmmMessageResponse result = ummMessageManageAppServiceImpl.createUmmMessage(createUmmMessageRequest);
        Assert.assertNotNull(result.getUmmMessageDTO());
    }

    @Test
    public void testMessageSend() {
        when(ummMessageRepository.add(any())).thenReturn(new UmmMessage());
        when(ummMsgChannelRepository.batchAdd(any())).thenReturn(Boolean.TRUE);
        when(ummMsgReceiverRepository.getByMsgId(anyLong())).thenReturn(new UmmMsgReceiver());
        when(ummMsgReceiverRepository.add(any())).thenReturn(new UmmMsgReceiver());
        when(ummMsgReceiverRepository.update(any())).thenReturn(true);
        when(ummCategoryRepository.getByCode(any())).thenReturn(MockDataUtilTest.buildMockCategory());
        when(ummTemplateRepository.getByCode(any())).thenReturn(MockDataUtilTest.buildMockTemplate());

        MessageSendRequest sendRequest = new MessageSendRequest();
        sendRequest.setChannelCode(MessageClientTypeEnum.MAIL.getClientType());
        sendRequest.setReceiverType("1");
        sendRequest.setReceiverGuidList(Arrays.asList("1"));
        String result = ummMessageManageAppServiceImpl.messageSend(sendRequest);
        Assert.assertNull(result);
    }

    @Test
    public void testMsgSend() {
        when(ummMessageRepository.getById(any())).thenReturn(MockDataUtilTest.buildMockMessage());
        when(ummMessageRepository.update(any())).thenReturn(true);

        Boolean result = ummMessageManageAppServiceImpl.msgSend(1L);
        Assert.assertEquals(Boolean.TRUE, result);
    }

    @Test
    public void testQueryUmmMessage() {
        ReflectionTestUtils.setField(ummMessageManageAppServiceImpl, "userContext", userContext);
        when(ummMessageRepository.getById(anyLong())).thenReturn(new UmmMessage());

        QueryUmmMessageResponse result = ummMessageManageAppServiceImpl.queryUmmMessage(Long.valueOf(1));
        Assert.assertNotNull(result.getUmmMessageDTO());
    }

    @Test
    public void testPageQueryUmmMessage() {
        ReflectionTestUtils.setField(ummMessageManageAppServiceImpl, "userContext", userContext);
        when(ummMessageRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(null);

        PageQueryUmmMessageResponse result = ummMessageManageAppServiceImpl.pageQueryUmmMessage(new PageQueryUmmMessageRequest());
        Assert.assertEquals(new PageQueryUmmMessageResponse(), result);
    }

    @Test
    public void testDeleteUmmMessage() {
        when(ummMessageRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMessageResponse result = ummMessageManageAppServiceImpl.deleteUmmMessage(Long.valueOf(1));
        Assert.assertEquals(new DeleteUmmMessageResponse(Boolean.TRUE), result);
    }

    @Test
    public void testDeleteUmmMessageOnBatch() {
        when(ummMessageRepository.getByIds(any())).thenReturn(Collections.emptyList());
        when(ummMessageRepository.deleteByIdOnBatch(any())).thenReturn(true);

        DeleteUmmMessageResponse result = ummMessageManageAppServiceImpl.deleteUmmMessageOnBatch(new DelUmmMessageRequest());
        Assert.assertFalse(result.getIsDelete());
    }

    @Test
    public void testUpdateUmmMessage() {
        when(ummMessageRepository.getById(any())).thenReturn(MockDataUtilTest.buildMockMessage());
        when(ummMessageRepository.update(any())).thenReturn(true);
        when(ummMsgChannelRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(null);
        when(ummMsgChannelRepository.add(any())).thenReturn(new UmmMsgChannel());
        when(ummMsgChannelRepository.update(any())).thenReturn(true);
        when(ummMsgReceiverRepository.getByMsgId(anyLong())).thenReturn(new UmmMsgReceiver());
        when(ummMsgReceiverRepository.add(any())).thenReturn(new UmmMsgReceiver());
        when(ummMsgReceiverRepository.update(any())).thenReturn(true);
        ReflectionTestUtils.setField(ummMessageManageAppServiceImpl, "userContext", userContext);

        UpdateUmmMessageRequest request = new UpdateUmmMessageRequest();
        UmmMessageUpdateDTO ummMessageUpdateDTO = new UmmMessageUpdateDTO();
        ummMessageUpdateDTO.setId(1L);
        request.setUmmMessageUpdateDTO(ummMessageUpdateDTO);
        UpdateUmmMessageResponse result = ummMessageManageAppServiceImpl.updateUmmMessage(request);
        Assert.assertEquals(new UpdateUmmMessageResponse(Boolean.TRUE), result);
    }
}