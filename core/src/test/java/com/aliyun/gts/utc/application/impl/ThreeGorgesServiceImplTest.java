package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.SmsMessageSendRequest;
import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.core.service.ThreeGorgesService;
import com.aliyun.gts.utc.core.service.UmmMsgSendManageCoreService;
import com.aliyun.gts.utc.core.service.impl.ThreeGorgesServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * @author: guoshengLi
 * @create: 2025-02-26 18:12
 * @Description:
 */
public class ThreeGorgesServiceImplTest {
  @Mock
  ThreeGorgesService threeGorgesService;

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
  }
  @Test
  public void sendSms() throws Exception {
    UmmSource ummSource = new UmmSource();
    UmmMsgSendDTO ummMsgSendDTO = new UmmMsgSendDTO();
    threeGorgesService.sendSms(ummSource,ummMsgSendDTO);
  }

}
