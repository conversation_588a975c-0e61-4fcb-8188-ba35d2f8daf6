package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMessageDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.CreateUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.PageQueryUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.UpdateUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.*;
import com.aliyun.gts.utc.core.domain.UmmMessage;
import com.aliyun.gts.utc.core.repository.UmmMessageRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.UUID;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 14:20:53
 */
public class UmmMessageManageCoreServiceImplTest {
    @Mock
    UmmMessageRepository ummMessageRepository;
    @InjectMocks
    UmmMessageManageCoreServiceImpl ummMessageManageCoreServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmMessage() {
        Long id = System.currentTimeMillis();
        UmmMessage ummMessage = new UmmMessage();
        ummMessage.setId(id);
        when(ummMessageRepository.add(any())).thenReturn(ummMessage);
        CreateUmmMessageRequest req = new CreateUmmMessageRequest();
        UmmMessageDTO ummMessageDTO = new UmmMessageDTO();
        ummMessageDTO.setId(id);
        req.setUmmMessageDTO(ummMessageDTO);
        CreateUmmMessageResponse result = ummMessageManageCoreServiceImpl.createUmmMessage(req);
        Assert.assertEquals(id, result.getUmmMessageDTO().getId());
    }

    @Test
    public void testQueryUmmMessage(){
        Long id = System.currentTimeMillis();
        UmmMessage ummMessage = new UmmMessage();
        ummMessage.setId(id);
        when(ummMessageRepository.getById(id)).thenReturn(ummMessage);

        QueryUmmMessageResponse result = ummMessageManageCoreServiceImpl.queryUmmMessage(id);
        Assert.assertEquals(id, result.getUmmMessageDTO().getId());
    }

    @Test
    public void testQueryUmmMessageByGuid(){
        String guid = UUID.randomUUID().toString();
        UmmMessage message = new UmmMessage();
        message.setGuid(guid);
        when(ummMessageRepository.getByGuid(guid)).thenReturn(message);

        QueryUmmMessageResponse result = ummMessageManageCoreServiceImpl.queryUmmMessageByGuid(guid);
        Assert.assertEquals(guid, result.getUmmMessageDTO().getGuid());
    }

    @Test
    public void testPageQueryUmmMessage(){
        IPage<UmmMessage> page = new Page<>();
        page.setTotal(1000);
        when(ummMessageRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(page);

        PageQueryUmmMessageResponse result = ummMessageManageCoreServiceImpl.pageQueryUmmMessage(new PageQueryUmmMessageRequest());
        Assert.assertEquals(1000, (long)result.getTotalCount());
    }

    @Test
    public void testDeleteUmmMessage(){
        when(ummMessageRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMessageResponse result = ummMessageManageCoreServiceImpl.deleteUmmMessage(Long.valueOf(1));
        Assert.assertEquals(true, result.getIsDelete());
    }

    @Test
    public void testUpdateStatus(){
        when(ummMessageRepository.updateStatus(anyLong(), anyInt())).thenReturn(true);

        boolean result = ummMessageManageCoreServiceImpl.updateStatus(1L, 0);
        Assert.assertEquals(true, result);
    }

    @Test
    public void testUpdateUmmMessage(){
        when(ummMessageRepository.update(any())).thenReturn(true);

        UpdateUmmMessageResponse result = ummMessageManageCoreServiceImpl.updateUmmMessage(new UpdateUmmMessageRequest());
        Assert.assertEquals(true, result.getIsUpdate());
    }
}