package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.ummmsgsend.CreateUmmMsgSendRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgsend.PageQueryUmmMsgSendRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgsend.UpdateUmmMsgSendRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgsend.*;
import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import com.aliyun.gts.utc.core.repository.UmmMsgSendRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:15:01
 */
public class UmmMsgSendManageAppServiceImplTest {
    @Mock
    UmmMsgSendRepository ummMsgSendRepository;
    @InjectMocks
    UmmMsgSendManageAppServiceImpl ummMsgSendManageAppServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmMsgSend() {
        when(ummMsgSendRepository.add(any())).thenReturn(new UmmMsgSend());
        CreateUmmMsgSendRequest createUmmMessageRequest = new CreateUmmMsgSendRequest();
        createUmmMessageRequest.setUmmMsgSendDTO(new UmmMsgSendDTO());
        CreateUmmMsgSendResponse result = ummMsgSendManageAppServiceImpl.createUmmMsgSend(createUmmMessageRequest);
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgSend() {
        when(ummMsgSendRepository.getById(anyLong())).thenReturn(new UmmMsgSend());

        QueryUmmMsgSendResponse result = ummMsgSendManageAppServiceImpl.queryUmmMsgSend(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testPageQueryUmmMsgSend()  {
        when(ummMsgSendRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());

        PageQueryUmmMsgSendResponse result = ummMsgSendManageAppServiceImpl.pageQueryUmmMsgSend(new PageQueryUmmMsgSendRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteUmmMsgSend()  {
        when(ummMsgSendRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMsgSendResponse result = ummMsgSendManageAppServiceImpl.deleteUmmMsgSend(Long.valueOf(1));
        Assert.assertTrue(result.getIsDelete());
    }

    @Test
    public void testUpdateUmmMsgSend()  {
        when(ummMsgSendRepository.update(any())).thenReturn(true);

        UpdateUmmMsgSendResponse result = ummMsgSendManageAppServiceImpl.updateUmmMsgSend(new UpdateUmmMsgSendRequest());
        Assert.assertTrue(result.getIsUpdate());
    }
}