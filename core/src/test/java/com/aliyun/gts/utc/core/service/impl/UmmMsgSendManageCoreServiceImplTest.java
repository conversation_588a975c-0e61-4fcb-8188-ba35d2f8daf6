package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsend.PageQueryUmmMsgSendRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsend.*;
import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import com.aliyun.gts.utc.core.repository.UmmMsgSendRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:20:22
 */
public class UmmMsgSendManageCoreServiceImplTest {
    @Mock
    UmmMsgSendRepository ummMsgSendRepository;
    @InjectMocks
    UmmMsgSendManageCoreServiceImpl ummMsgSendManageCoreServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateUmmMsgSend() {
        when(ummMsgSendRepository.add(any())).thenReturn(new UmmMsgSend());

        CreateUmmMsgSendResponse result = ummMsgSendManageCoreServiceImpl.createUmmMsgSend(new UmmMsgSendDTO());
        Assert.assertNotNull(result);
    }

    @Test
    public void testBatchCreateUmmMsgSend() {
        when(ummMsgSendRepository.batchAdd(any())).thenReturn(Boolean.TRUE);

        Boolean result = ummMsgSendManageCoreServiceImpl.batchCreateUmmMsgSend(Arrays.<UmmMsgSendDTO>asList(new UmmMsgSendDTO()));
        Assert.assertNotNull(result);
    }

    @Test
    public void testBatchUpdateUmmMsgSend() {
        when(ummMsgSendRepository.batchUpdate(any())).thenReturn(Boolean.TRUE);

        Boolean result = ummMsgSendManageCoreServiceImpl.batchUpdateUmmMsgSend(Arrays.<UmmMsgSendDTO>asList(new UmmMsgSendDTO()));
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgSend() {
        when(ummMsgSendRepository.getById(anyLong())).thenReturn(new UmmMsgSend());

        QueryUmmMsgSendResponse result = ummMsgSendManageCoreServiceImpl.queryUmmMsgSend(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryUmmMsgSendByMsgId() {
        when(ummMsgSendRepository.queryUmmMsgSendByMsgId(anyString())).thenReturn(Arrays.<UmmMsgSendDTO>asList(new UmmMsgSendDTO()));

        List<UmmMsgSendDTO> result = ummMsgSendManageCoreServiceImpl.queryUmmMsgSendByMsgId("msgId");
        Assert.assertNotNull(result);
    }

    @Test
    public void testPageQueryUmmMsgSend() {
        when(ummMsgSendRepository.getByRange(anyLong(), anyLong(), any())).thenReturn(new Page<>());

        PageQueryUmmMsgSendResponse result = ummMsgSendManageCoreServiceImpl.pageQueryUmmMsgSend(new PageQueryUmmMsgSendRequest());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteUmmMsgSend() {
        when(ummMsgSendRepository.deleteById(anyLong())).thenReturn(true);

        DeleteUmmMsgSendResponse result = ummMsgSendManageCoreServiceImpl.deleteUmmMsgSend(Long.valueOf(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testUpdateUmmMsgSend() {
        when(ummMsgSendRepository.update(any())).thenReturn(true);

        UpdateUmmMsgSendResponse result = ummMsgSendManageCoreServiceImpl.updateUmmMsgSend(new UmmMsgSendDTO());
        Assert.assertNotNull(result);
    }

    @Test
    public void testBatchUpdateUmmMsgSendStatus() {
        when(ummMsgSendRepository.batchUpdateUmmMsgSendStatus(any(), anyInt())).thenReturn(true);

        Boolean result = ummMsgSendManageCoreServiceImpl.batchUpdateUmmMsgSendStatus(Arrays.<Long>asList(Long.valueOf(1)), Integer.valueOf(0));
        Assert.assertNotNull(result);
    }
}