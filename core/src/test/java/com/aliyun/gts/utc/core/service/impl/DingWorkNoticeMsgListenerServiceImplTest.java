package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.NoticeRollbackRequest;
import com.aliyun.gts.utc.core.domain.OapiMoziEmployeeListEmployeeAccountIdsDTO;
import com.aliyun.gts.utc.core.mq.config.DingTalkClient;
import com.aliyun.gts.utc.core.service.UmmMsgSendLogManageCoreService;
import com.aliyun.gts.utc.core.service.UmmMsgSendManageCoreService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static com.aliyun.gts.utc.common.constants.StatusConstants.TENANTID_KEY;
import static org.mockito.Mockito.*;

/**
 * Created by 鸿度 on 2022-12-01 16:18:00
 */
public class DingWorkNoticeMsgListenerServiceImplTest {
    @Mock
    DingTalkClient dingTalkClient;
    @Mock
    UmmMsgSendLogManageCoreService ummMsgSendLogManageCoreService;
    @Mock
    Logger log;
    @Mock
    UmmMsgSendManageCoreService ummMsgSendManageCoreService;
    @InjectMocks
    DingWorkNoticeMsgListenerServiceImpl dingWorkNoticeMsgListenerServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetChannelId() {
        Long result = dingWorkNoticeMsgListenerServiceImpl.getChannelId();
        Assert.assertEquals(Long.valueOf(2), result);
    }

    @Test
    public void testNotice() {
        when(dingTalkClient.sendDingWorkNotice(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(Boolean.TRUE);
        when(dingTalkClient.listEmployeeAccountIds(any(), anyLong())).thenReturn(Arrays.asList(new OapiMoziEmployeeListEmployeeAccountIdsDTO()));

        boolean result = dingWorkNoticeMsgListenerServiceImpl.notice(new UmmMsgSendDTO(), new HashMap<String, Object>() {{
            put(TENANTID_KEY, "1");
        }});
        Assert.assertFalse(result);
    }

    @Test
    public void testNoticeRollback() {
        when(dingTalkClient.noticeRollback(any())).thenReturn(Boolean.TRUE);

        dingWorkNoticeMsgListenerServiceImpl.noticeRollback(new NoticeRollbackRequest(), new HashMap<String, Object>() {{
            put(TENANTID_KEY, "1");
        }});
    }
}