package com.aliyun.gts.utc;

import com.aliyun.gts.utc.common.constants.MessageTemplateConstant;
import com.aliyun.gts.utc.common.enums.UmmMsgStatusEnum;
import com.aliyun.gts.utc.core.domain.UmmCategory;
import com.aliyun.gts.utc.core.domain.UmmMessage;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.model.user.BaseUserInfoDTO;

public class MockDataUtilTest {

    public static UmmCategory buildMockCategory() {
        UmmCategory mockCategory = new UmmCategory();
        mockCategory.setTenantCode(RuntimeContextUtil.getTenantCode());
        return mockCategory;
    }

    public static UmmTemplate buildMockTemplate() {
        UmmTemplate mockTemp = new UmmTemplate();
        mockTemp.setTenantCode(RuntimeContextUtil.getTenantCode());
        mockTemp.setIsDeleted(MessageTemplateConstant.NOT_DELETED);
        return mockTemp;
    }

    public static UmmMessage buildMockMessage() {
        UmmMessage ummMessage = new UmmMessage();
        ummMessage.setTenantCode(RuntimeContextUtil.getTenantCode());
        ummMessage.setStatus(UmmMsgStatusEnum.SEND_ALL_SUCESS.getStatus());
        return ummMessage;
    }

    public static UserInfoDTO buildUserInfoDTO() {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setBaseInfo(new BaseUserInfoDTO());
        return userInfoDTO;
    }
}
