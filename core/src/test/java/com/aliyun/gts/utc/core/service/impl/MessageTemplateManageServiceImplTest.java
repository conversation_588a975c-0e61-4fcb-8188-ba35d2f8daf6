package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.MockDataUtilTest;
import com.aliyun.gts.utc.api.dto.ParamDTO;
import com.aliyun.gts.utc.api.facade.request.template.CreateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.DeleteMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.PageQueryMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.UpdateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.response.template.*;
import com.aliyun.gts.utc.core.domain.UmmCategory;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.repository.UmmCategoryRepository;
import com.aliyun.gts.utc.core.repository.UmmMessageRepository;
import com.aliyun.gts.utc.core.repository.UmmMsgParameterRepository;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dto.PageMessageTemplateDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.model.user.BaseUserInfoDTO;
import com.yunqiao.foura.service.sdk.service.UserContext;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;

import static org.mockito.Mockito.*;

public class MessageTemplateManageServiceImplTest {
    @Mock
    UmmTemplateRepository ummTemplateRepository;
    @Mock
    UmmCategoryRepository ummCategoryRepository;
    @Mock
    UmmMsgParameterRepository ummMsgParameterRepository;
    @Mock
    UmmMessageRepository ummMessageRepository;

    UserContext userContext;

    @InjectMocks
    MessageTemplateManageServiceImpl messageTemplateManageServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        RuntimeContextUtil.setTenantCode("t1");
        userContext = new UserContext() {
            @Override
            public UserInfoDTO currentUserInfo(boolean force) {
                return MockDataUtilTest.buildUserInfoDTO();
            }
        };
    }

    @Test
    public void testAdd(){
        when(ummTemplateRepository.add(any())).thenReturn(new UmmTemplate());
        when(ummTemplateRepository.countTitle(anyString(), anyLong())).thenReturn(0);
        when(ummMsgParameterRepository.getAll()).thenReturn(Collections.<UmmMsgParameter>singletonList(new UmmMsgParameter()));
        ReflectionTestUtils.setField(messageTemplateManageServiceImpl, "userContext", userContext);

        CreateMessageTemplateResponse result = messageTemplateManageServiceImpl.add(new CreateMessageTemplateRequest());
        Assert.assertEquals(new CreateMessageTemplateResponse(), result);
    }

    @Test
    public void testModify(){
        UmmTemplate ummTemplate = new UmmTemplate();
        ummTemplate.setIsDeleted(0L);
        when(ummTemplateRepository.getById(any())).thenReturn(MockDataUtilTest.buildMockTemplate());
        when(ummTemplateRepository.update(any())).thenReturn(true);
        when(ummTemplateRepository.countTitle(any(), any())).thenReturn(0);
        when(ummMsgParameterRepository.getAll()).thenReturn(Collections.singletonList(new UmmMsgParameter()));
        ReflectionTestUtils.setField(messageTemplateManageServiceImpl, "userContext", userContext);

        UpdateMessageTemplateRequest request = new UpdateMessageTemplateRequest();
        request.setParamDTO(new ParamDTO());
        UpdateMessageTemplateResponse result = messageTemplateManageServiceImpl.modify(request);
        Assert.assertEquals(true, result.getResult());
    }

    @Test
    public void testDetail(){
        UmmTemplate ummTemplate = new UmmTemplate();
        ummTemplate.setId(1L);
        when(ummTemplateRepository.getById(any())).thenReturn(MockDataUtilTest.buildMockTemplate());
        when(ummCategoryRepository.getById(any())).thenReturn(MockDataUtilTest.buildMockCategory());
        ReflectionTestUtils.setField(messageTemplateManageServiceImpl, "userContext", userContext);


        QueryMessageTemplateResponse result = messageTemplateManageServiceImpl.detail(1L);
        Assert.assertNull(result.getId());
    }

    @Test
    public void testDelete(){
        when(ummTemplateRepository.batchDelete(any())).thenReturn(true);
        when(ummTemplateRepository.getById(any())).thenReturn(MockDataUtilTest.buildMockTemplate());
        when(ummMessageRepository.getByTplId(any())).thenReturn(Lists.newArrayList(MockDataUtilTest.buildMockMessage()));

        ReflectionTestUtils.setField(messageTemplateManageServiceImpl, "userContext", userContext);
        DeleteMessageTemplateRequest delete = new DeleteMessageTemplateRequest();
        delete.setIdList(Lists.newArrayList(1L));
        DeleteMessageTemplateResponse result = messageTemplateManageServiceImpl.delete(delete);
        Assert.assertEquals(true, result.getResult());
    }

    @Test
    public void testPageQuery(){
        Page<PageMessageTemplateDTO> page = new Page<>();
        page.setRecords(new ArrayList<>());
        page.setTotal(0);
        when(ummTemplateRepository.pageQuery(any(), any())).thenReturn(page);
        when(ummCategoryRepository.getByParentId(anyLong())).thenReturn(Collections.<UmmCategory>singletonList(new UmmCategory()));

        Page<PageQueryMessageTemplateResponse> result = messageTemplateManageServiceImpl.pageQuery(new PageQueryMessageTemplateRequest());
        Assert.assertEquals(0, result.getTotal());
    }
}