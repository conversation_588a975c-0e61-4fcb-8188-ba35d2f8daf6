package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.NoticeRollbackRequest;
import com.aliyun.gts.utc.core.service.MailService;
import com.aliyun.gts.utc.core.service.UmmMsgSendLogManageCoreService;
import com.aliyun.gts.utc.core.service.UmmMsgSendManageCoreService;
import com.aliyun.gts.utc.spi.UserServiceSpi;
import com.aliyun.gts.utc.spi.dto.User;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;

import static com.aliyun.gts.utc.common.constants.StatusConstants.TENANTID_KEY;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

/**
 * Created by 鸿度 on 2022-12-01 16:18:13
 */
public class MailNoticeMsgListenerServiceImplTest {
    @Mock
    UserServiceSpi userServiceSpi;
    @Mock
    MailService mailService;
    @Mock
    UmmMsgSendManageCoreService ummMsgSendManageCoreService;
    @Mock
    UmmMsgSendLogManageCoreService ummMsgSendLogManageCoreService;
    @InjectMocks
    MailNoticeMsgListenerServiceImpl mailNoticeMsgListenerServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetChannelId() {
        Long result = mailNoticeMsgListenerServiceImpl.getChannelId();
        Assert.assertEquals(Long.valueOf(5), result);
    }

    @Test
    public void testNotice() {
        when(userServiceSpi.queryUser(anyString())).thenReturn(new User());
        UmmMsgSendDTO msgSendDTO = new UmmMsgSendDTO();
        msgSendDTO.setUserGuid("1");
        msgSendDTO.setEmail("<EMAIL>");
        boolean result = mailNoticeMsgListenerServiceImpl.notice(msgSendDTO, new HashMap<String, Object>() {{
            put(TENANTID_KEY, "1");
        }});
        Assert.assertEquals(true, result);
    }

    @Test
    public void testNoticeRollback() {
        mailNoticeMsgListenerServiceImpl.noticeRollback(new NoticeRollbackRequest(), new HashMap<String, Object>() {{
            put("String", "extend");
        }});
    }
}