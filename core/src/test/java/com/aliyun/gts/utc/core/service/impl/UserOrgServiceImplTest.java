package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.request.ummuserorg.PageQueryOrgMembersRequest;
import com.aliyun.gts.utc.api.facade.response.OrgTreeResponse;
import com.aliyun.gts.utc.api.facade.response.ummuserorg.PageQueryOrgMembersResponse;
import com.aliyun.gts.utc.spi.OrgServiceSpi;
import com.aliyun.gts.utc.spi.UserServiceSpi;
import com.aliyun.gts.utc.spi.dto.Org;
import com.aliyun.gts.utc.spi.dto.User;
import com.aliyun.gts.utc.spi.request.UserQueryRequest;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * Created by 鸿度 on 2022-12-01 16:20:41
 */
public class UserOrgServiceImplTest {
    @Mock
    OrgServiceSpi orgServiceSpi;
    @Mock
    UserServiceSpi userServiceSpi;
    @InjectMocks
    UserOrgServiceImpl userOrgServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testListOrgTree() {
        Org org = new Org();
        org.setId(1L);
        List<Org> orgs = new ArrayList<>();
        orgs.add(org);
        when(orgServiceSpi.queryAllOrg()).thenReturn(orgs);
        List<OrgTreeResponse> result = userOrgServiceImpl.listOrgTree("type");
        Assert.assertNotNull(result);
    }

    @Test
    public void testListOrgMembersByPage() {
        User mockUser = new User();
        mockUser.setUserId("1");
        UserQueryRequest userQueryRequest = new UserQueryRequest();
        userQueryRequest.setPageNum(1L);
        userQueryRequest.setPageSize(10L);
        userQueryRequest.setOrgGuidList(Collections.singletonList("1"));
        com.aliyun.gts.utc.api.base.Page<List<User>> p = new com.aliyun.gts.utc.api.base.Page<>();
        p.setTotalCount(100L);
        // when(userServiceSpi.queryUser(userQueryRequest)).thenReturn(p);
        p.setList(Arrays.asList(new User()));
        PageQueryOrgMembersRequest request = new PageQueryOrgMembersRequest();
        request.setPageNum(1L);
        request.setPageSize(10L);
        request.setOrgGuidList(Collections.singletonList("1"));
        Page<PageQueryOrgMembersResponse> result = userOrgServiceImpl.listOrgMembersByPage(request);
        Assert.assertNotNull(result);
    }
}