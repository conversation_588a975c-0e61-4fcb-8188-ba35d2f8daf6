/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.CreateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.PageQueryUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.UpdateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgchannel.*;
import com.aliyun.gts.utc.core.domain.UmmMsgChannel;

import java.util.List;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgChannelManageAppService.java
 * source table name: umm_msg_channel
 * initial code lines: 67
 * initial generated date: 2021-06-05 17:03:30
 *
 * @date 2021-06-05 17:03:30
 */
public interface UmmMsgChannelManageCoreService {

    /**
     * 创建UmmMsgChannel
     *
     * @param createUmmMsgChannelRequest 创建$UmmMsgChannel请求
     * @return 创建UmmMsgChannel响应
     */
    CreateUmmMsgChannelResponse createUmmMsgChannel(CreateUmmMsgChannelRequest createUmmMsgChannelRequest);

    /**
     * 查询UmmMsgChannel
     *
     * @param id 查询UmmMsgChannel请求
     * @return 查询UmmMsgChannel响应
     */
    QueryUmmMsgChannelResponse queryUmmMsgChannel(Long id);

    /**
     * 查询UmmMsgChannel
     *
     * @param msgId 查询UmmMsgChannel请求
     * @return 查询UmmMsgChannel响应
     */
    List<UmmMsgChannel> queryUmmMsgChannelByMsgId(Long msgId);

    /**
     * 分页查询UmmMsgChannel
     *
     * @param pageQueryUmmMsgChannelRequest 查询UmmMsgChannel请求
     * @return UmmMsgChannel查询结果
     */
    PageQueryUmmMsgChannelResponse pageQueryUmmMsgChannel(PageQueryUmmMsgChannelRequest pageQueryUmmMsgChannelRequest);

    /**
     * 删除UmmMsgChannel
     *
     * @param id 删除UmmMsgChannel请求
     * @return UmmMsgChannel删除结果
     */
    DeleteUmmMsgChannelResponse deleteUmmMsgChannel(Long id);

    /**
     * 更新UmmMsgChannel
     *
     * @param updateUmmMsgChannelRequest 更新UmmMsgChannel请求
     * @return UmmMsgChannel更新结果
     */
    UpdateUmmMsgChannelResponse updateUmmMsgChannel(UpdateUmmMsgChannelRequest updateUmmMsgChannelRequest);

}
