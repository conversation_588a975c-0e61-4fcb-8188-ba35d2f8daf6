package com.aliyun.gts.utc.common.enums;

/**
 * 推送消息状态定义
 */
public enum UmmMsgStatusEnum {
    //    状态（0草稿；1待发送； 2发送中；3部分成功；4全部成功；5发送失败；6待审批；7审批中；8审批未通过；9已撤销）
    DRAFT( 0,"草稿"),
    SEND_WAITTING(1,"待发送"),
    SEND_PENDING( 2,"发送中"),
    SEND_PART_SUCCESS( 3,"部分成功"),
    SEND_ALL_SUCESS( 4,"发送成功"),
    SEND_FAILD( 5,"发送失败"),
    APPROVAL_PENDING( 6,"待审批"),
    APPROVALING( 7,"审批中"),
    APPROVAL_REJECTED( 8,"审批未通过"),
    CANCELLED( 9,"已撤销");

    UmmMsgStatusEnum(Integer status, String desc){
        this.status = status;
        this.desc = desc;
    }

    private Integer status;
    private String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
