package com.aliyun.gts.utc.integration.client.foura.impl;

import com.aliyun.gts.utc.integration.client.foura.UserCenterClient;
import com.aliyun.gts.utc.spi.OrgServiceSpi;
import com.aliyun.gts.utc.spi.dto.Org;
import com.google.common.base.Strings;
import com.yunqiao.foura.service.model.OrganizationInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: yaohao.yh
 * @date: 2023/9/10
 */
@Service
public class OrgServiceApiImpl implements OrgServiceSpi {
    @Autowired
    private UserCenterClient userCenterClient;

    @Override
    public List<Org> queryAllOrg() {
        List<Org> orgList = listChildOrgList(null);
        orgList.forEach(this::setChildOrgTree);
        return orgList;
    }

    @Override
    public List<Org> listChildOrgList(String parentOrgGuid) {
        List<OrganizationInfoDTO> organizationInfoDTOS;
        if (Strings.isNullOrEmpty(parentOrgGuid)) {
            organizationInfoDTOS = userCenterClient.listRootOrgs();
        } else {
            organizationInfoDTOS = userCenterClient.listOrgsByParentId(parentOrgGuid);
        }
        if (organizationInfoDTOS.isEmpty()) {
            return new ArrayList<>();
        }
        List<Org> childOrgList = new ArrayList<>();
        organizationInfoDTOS.forEach(organizationInfoDTO -> {
            childOrgList.add(convert(organizationInfoDTO));
        });
        childOrgList.forEach(childOrg -> {
            Boolean isLeaf = CollectionUtils.isEmpty(userCenterClient.listOrgsByParentId(childOrg.getGuid()));
            childOrg.setIsLeaf(isLeaf);
        });
        return childOrgList;
    }

    private void setChildOrgTree(Org org) {
        String parentOrgGuid = org.getGuid();
        List<Org> childOrgList = listChildOrgList(parentOrgGuid);
        if (childOrgList.isEmpty()) {
            org.setIsLeaf(true);
            return;
        }
        org.setChildList(childOrgList);
        org.setIsLeaf(false);
        for (Org child : childOrgList) {
            child.setParentId(org.getGuid());
            setChildOrgTree(child);
        }
    }

    private Org convert(OrganizationInfoDTO organizationInfoDTO) {
        Org org = new Org();
        org.setId(organizationInfoDTO.getId());
        org.setGuid(organizationInfoDTO.getGuid());
        org.setOrgCode(organizationInfoDTO.getOrgCode());
        org.setOrgName(organizationInfoDTO.getOrgName());
        org.setParentId("0".equals(organizationInfoDTO.getParentId()) ? null : organizationInfoDTO.getParentId());
        return org;
    }
}
