/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dataobject;

import com.aliyun.gts.utc.core.dataobject.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmTemplateDO.java
 * source table name: umm_template
 * initial code lines: 137
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Data
@TableName("umm_template")
public class UmmTemplateDO extends BaseEntity {

    /**
    * 主键id
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * 消息标题
    */
    @TableField("tpl_title")
    private String tplTitle;

    /**
    * 消息内容
    */
    @TableField("tpl_content")
    private String tplContent;
    /**
    * 模板编码
    */
    @TableField("tpl_code")
    private String tplCode;

    /**
    * 发送参数json存储，存储模板中涉及到的参数，存储数组
    */
    @TableField("param")
    private String param;

    /**
     * 栏目id
     */
    @TableField("category_id")
    private Long categoryId;

    /**
    * 支持的channels, 存储channel id的数组
    */
    @TableField("support_channels")
    private String supportChannels;

    /**
    * 状态 0禁用 1启用
    */
    @TableField("status")
    private Integer status;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    @TableField("features")
    private String features;

    /**
     * 消息源id
     */
    @TableField("source_id")
    private Long sourceId;
}