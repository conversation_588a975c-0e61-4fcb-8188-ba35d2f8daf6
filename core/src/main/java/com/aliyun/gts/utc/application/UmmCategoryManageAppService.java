/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application;

import com.aliyun.gts.utc.api.facade.request.ummcategory.CreateUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.request.ummcategory.PageQueryUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.request.ummcategory.UpdateUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.response.ummcategory.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmCategoryManageAppService.java
 * source table name: umm_category
 * initial code lines: 67
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
public interface UmmCategoryManageAppService {

    /**
     * 创建UmmCategory
     *
     * @param createUmmCategoryRequest 创建$UmmCategory请求
     * @return 创建UmmCategory响应
     */
    CreateUmmCategoryResponse createUmmCategory(CreateUmmCategoryRequest createUmmCategoryRequest);

    /**
     * 查询UmmCategory
     *
     * @param id 查询UmmCategory请求
     * @return 查询UmmCategory响应
     */
    QueryUmmCategoryResponse queryUmmCategory(Long id);

    /**
     * 分页查询UmmCategory
     *
     * @param pageQueryUmmCategoryRequest 查询UmmCategory请求
     * @return UmmCategory查询结果
     */
    PageQueryUmmCategoryResponse pageQueryUmmCategory(PageQueryUmmCategoryRequest pageQueryUmmCategoryRequest);

    /**
     * 删除UmmCategory
     *
     * @param id 删除UmmCategory请求
     * @return UmmCategory删除结果
     */
    DeleteUmmCategoryResponse deleteUmmCategory(Long id);

    /**
     * 更新UmmCategory
     *
     * @param updateUmmCategoryRequest 更新UmmCategory请求
     * @return UmmCategory更新结果
     */
    UpdateUmmCategoryResponse updateUmmCategory(UpdateUmmCategoryRequest updateUmmCategoryRequest);

    /**
     * 查询栏目树
     * @return
     */
    AllUmmCategoryResponse listAll();
}