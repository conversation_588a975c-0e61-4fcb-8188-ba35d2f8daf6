/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.ummmsgsend.CreateUmmMsgSendRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgsend.PageQueryUmmMsgSendRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgsend.UpdateUmmMsgSendRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgsend.*;
import com.aliyun.gts.utc.application.UmmMsgSendManageAppService;
import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import com.aliyun.gts.utc.core.repository.UmmMsgSendRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendManageAppServiceImpl.java
 * source table name: umm_msg_send
 * initial code lines: 113
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Service
public class UmmMsgSendManageAppServiceImpl implements UmmMsgSendManageAppService {
    @Autowired
    private UmmMsgSendRepository ummMsgSendRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmMsgSendResponse createUmmMsgSend(CreateUmmMsgSendRequest createUmmMsgSendRequest){

        UmmMsgSendDTO ummMsgSendDTO = createUmmMsgSendRequest.getUmmMsgSendDTO();

        UmmMsgSend ummMsgSend = UmmMsgSendDTOConverter.INSTANCE.convert(ummMsgSendDTO);
        UmmMsgSend add = ummMsgSendRepository.add(ummMsgSend);
        ummMsgSendDTO.setId(add.getId());

        CreateUmmMsgSendResponse response = new CreateUmmMsgSendResponse();
        response.setUmmMsgSendDTO(ummMsgSendDTO);

        return response;
    }

    @Override
    public QueryUmmMsgSendResponse queryUmmMsgSend(Long id) {

        UmmMsgSend ummMsgSend = ummMsgSendRepository.getById(id);
        UmmMsgSendDTO ummMsgSendDTO = UmmMsgSendDTOConverter.INSTANCE.convert(ummMsgSend);
        QueryUmmMsgSendResponse response = new QueryUmmMsgSendResponse();
        response.setUmmMsgSendDTO(ummMsgSendDTO);
        return response;
    }

    @Override
    public PageQueryUmmMsgSendResponse pageQueryUmmMsgSend(PageQueryUmmMsgSendRequest request) {

        UmmMsgSendDTO ummMsgSendDTO = request.getUmmMsgSendDTO();
        UmmMsgSend ummMsgSend =
                UmmMsgSendDTOConverter.INSTANCE.convert(ummMsgSendDTO);
        IPage<UmmMsgSend> byRange =
                ummMsgSendRepository.getByRange(request.getPageNum(),
                        request.getPageSize(), ummMsgSend);

        PageQueryUmmMsgSendResponse response =
                new PageQueryUmmMsgSendResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
                .map(UmmMsgSendDTOConverter.INSTANCE::convert)
                .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmMsgSendResponse deleteUmmMsgSend(Long id) {
        DeleteUmmMsgSendResponse deleteUmmMsgSendResponse = new DeleteUmmMsgSendResponse();
        deleteUmmMsgSendResponse.setIsDelete(ummMsgSendRepository.deleteById(id));
        return deleteUmmMsgSendResponse;
    }

    @Override
    public UpdateUmmMsgSendResponse updateUmmMsgSend(UpdateUmmMsgSendRequest request) {

        UmmMsgSendDTO ummMsgSendDTO = request.getUmmMsgSendDTO();

        UmmMsgSend ummMsgSend = UmmMsgSendDTOConverter.INSTANCE.convert(ummMsgSendDTO);
        UpdateUmmMsgSendResponse updateUmmMsgSendResponse = new UpdateUmmMsgSendResponse();
        updateUmmMsgSendResponse.setIsUpdate(ummMsgSendRepository.update(ummMsgSend));
        return updateUmmMsgSendResponse;
    }

    @Mapper
    interface UmmMsgSendDTOConverter {
        UmmMsgSendDTOConverter INSTANCE = Mappers.getMapper(UmmMsgSendDTOConverter.class);

        UmmMsgSend convert(UmmMsgSendDTO ummMsgSendDTO);

        UmmMsgSendDTO convert(UmmMsgSend ummMsgSend);
    }

}