package com.aliyun.gts.utc.core.response;

import lombok.Data;

@Data
public class ResultObject<T> {
    private Boolean success;
    private String errCode;
    private String errMsg;
    private T data;

    public ResultObject() {
    }

    public ResultObject<T> setSuccess(Boolean success) {
        this.success = success;
        return this;
    }

    public ResultObject<T> setErrCode(String errCode) {
        this.errCode = errCode;
        return this;
    }

    public ResultObject<T> setErrMsg(String errMsg) {
        this.errMsg = errMsg;
        return this;
    }

    public ResultObject<T> setData(T data) {
        this.data = data;
        return this;
    }
}
