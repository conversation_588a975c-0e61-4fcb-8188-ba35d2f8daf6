package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.enums.ReceiverTypeEnum;
import com.aliyun.gts.utc.api.facade.request.NoticeRollbackRequest;
import com.aliyun.gts.utc.core.domain.OapiMoziEmployeeListEmployeeAccountIdsDTO;
import com.aliyun.gts.utc.core.mq.config.DingTalkClient;
import com.aliyun.gts.utc.integration.client.MessageClientTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.aliyun.gts.utc.common.constants.StatusConstants.*;

/**
 * 钉钉工作通知
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DingWorkNoticeMsgListenerServiceImpl extends AbstractSendMsgListenerService {
    private final DingTalkClient dingTalkClient;

    public DingWorkNoticeMsgListenerServiceImpl(DingTalkClient dingTalkClient) {
        this.dingTalkClient = dingTalkClient;
    }

    @Override
    public Long getChannelId() {
        return MessageClientTypeEnum.DING_WORK_NOTICE.getClientID();
    }

    @Override
    protected boolean notice(UmmMsgSendDTO ummMsgSendDTO, Map<String,
            Object> extend) {
        String tenantId = String.valueOf(extend.get(TENANTID_KEY));
        String msgContent = ummMsgSendDTO.getMsgContent();
        ummMsgSendDTO.setUserGuid(String.format("GE_" +
                "%s", ummMsgSendDTO.getUserGuid()));

        List<OapiMoziEmployeeListEmployeeAccountIdsDTO> oapiMoziEmployeeListEmployeeAccountIdsDTOS =
                dingTalkClient.listEmployeeAccountIds(Collections.singletonList(ummMsgSendDTO.getUserGuid()), Long.valueOf(tenantId));
        List<Long> accountIds =
                oapiMoziEmployeeListEmployeeAccountIdsDTOS.stream().map(OapiMoziEmployeeListEmployeeAccountIdsDTO::getAccountId)
                        .collect(Collectors.toList());
        String guid = UUID.randomUUID().toString().replace("-", "");
        Boolean result =
                dingTalkClient.sendDingWorkNotice(ReceiverTypeEnum.USR.name()
                        , StringUtils.join(accountIds, ","), msgContent,
                        tenantId, guid);
        ummMsgSendDTO.setStatus(result ? SEND_SUCCESS_STATUS :
                SEND_FAIL_STATUS);
        return result;
    }


    @Override
    public void noticeRollback(Object obj, Map<String, Object> extend) {
        NoticeRollbackRequest noticeRollbackRequest =
                (NoticeRollbackRequest) obj;
        dingTalkClient.noticeRollback(noticeRollbackRequest);
    }
}
