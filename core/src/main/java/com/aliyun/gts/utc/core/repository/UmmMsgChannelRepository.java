/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository;

import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgChannelDO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;


public interface UmmMsgChannelRepository {

    /**
     * 通过 id 获取ummMsgChannel
     *
     * @param id ummMsgChannel id
     * @return ummMsgChannel；null 如果账户不存在
     */
    UmmMsgChannel getById(Long id);
    /**
     * 通过 id 获取ummMsgChannel
     *
     * @param msgId ummMsgChannel id
     * @return ummMsgChannel；null 如果账户不存在
     */
    List<UmmMsgChannel> getByMsgId(Long msgId);

    /**
     * 获取一个区间内的ummMsgChannel
     *
     * @param pageNum
     * @param pageSize 期望个数
     * @return 区间内的ummMsgChannel列表
     */
    IPage<UmmMsgChannel> getByRange(Long pageNum, Long pageSize,
                                    UmmMsgChannel ummMsgChannel);

    /**
     * 添加ummMsgChannel
     *
     * @param ummMsgChannel 待添加ummMsgChannel
     */
    UmmMsgChannel add(UmmMsgChannel ummMsgChannel);

    Boolean batchAdd(List<UmmMsgChannelDO> ummMsgChannels);

    /**
     * 更新ummMsgChannel
     *
     * @param ummMsgChannel 待更新ummMsgChannel对象
     * @return true，更新成功；false，ummMsgChannel不存在
     */
    boolean update(UmmMsgChannel ummMsgChannel);

    /**
     * 通过 id 删除ummMsgChannel
     *
     * @param id 账户 id
     * @return true，删除成功；false，ummMsgChannel不存在
     */
    boolean deleteById(Long id);

    /**
     * 根据消息id查询消息渠道
     *
     * @param msgId 消息id
     * @return 消息渠道id
     */
    UmmMsgChannel getByMessage(Long msgId);

    List<UmmMsgChannel> getByMsgId(List<Long> msgIds);

}
