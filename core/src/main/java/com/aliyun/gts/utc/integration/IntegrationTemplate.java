package com.aliyun.gts.utc.integration;

import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.aliyun.gts.utc.api.error.ErrorCodeEnum;
import com.aliyun.gts.utc.common.utils.ParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;

/**
 * 集成服务模板
 */
@Slf4j
@Component
public class IntegrationTemplate {
    public <T> T execute(Callable<ResultResponse<T>> command) {
        try {
            ResultResponse<T> response = command.call();
            ParamUtil.checkNotNull(response, ErrorCodeEnum.THIRD_PARTY_ERROR, "response from third party system is null");

            if (!response.getSuccess()) {
                log.warn("response from third party system is failed, errorCode={}, errorMessage={}", response.getCode(), response.getMessage());
                ParamUtil.fail(ErrorCodeEnum.THIRD_PARTY_ERROR, "response from third party system is failed");
            }
            log.debug("response from third party system is success, data={}", response.getData());

            return response.getData();
        }
        catch (Exception e) {
            log.error("encounter unknown exception during integrating with third party system", e);
            ParamUtil.fail(ErrorCodeEnum.THIRD_PARTY_ERROR, e.getMessage());
            return null;
        }
    }
}
