package com.aliyun.gts.utc.common.enums;

/**
 * <AUTHOR>
 * @date 2021/5/19 10:28
 */
public enum ResponseCodeEnum {

    /**
     * 异常code
     */
    TPLCODESAME("1000101", "已存在的模板编码,请重新填写"),

    SOURCECODESAME("1000102", "已存在的消息源编码,请重新填写"),

    SOURCENAMESAME("1000103", "已存在的消息源名称,请重新填写"),
    SOURCENOTEXIST("1000104", "不存在的消息源"),
    SOURCEUSEDBYTPL("1000105", "消息源被消息模版使用，不允许删除");


    private final String code;
    private final String message;

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }


    private ResponseCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
