package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgChannelDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.CreateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.PageQueryUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.UpdateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgchannel.*;
import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import com.aliyun.gts.utc.core.repository.UmmMsgChannelRepository;
import com.aliyun.gts.utc.core.service.UmmMsgChannelManageCoreService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class UmmMsgChannelManageCoreServiceImpl implements UmmMsgChannelManageCoreService {

    private final UmmMsgChannelRepository ummMsgChannelRepository;

    public UmmMsgChannelManageCoreServiceImpl(UmmMsgChannelRepository ummMsgChannelRepository) {
        this.ummMsgChannelRepository = ummMsgChannelRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmMsgChannelResponse createUmmMsgChannel(CreateUmmMsgChannelRequest createUmmMsgChannelRequest) {

        UmmMsgChannelDTO ummMsgChannelDTO =
                createUmmMsgChannelRequest.getUmmMsgChannelDTO();

        UmmMsgChannel ummMsgChannel =
                UmmMsgChannelDTOConverter.INSTANCE.convert(ummMsgChannelDTO);
        UmmMsgChannel add = ummMsgChannelRepository.add(ummMsgChannel);
        ummMsgChannelDTO.setId(add.getId());

        CreateUmmMsgChannelResponse response = new CreateUmmMsgChannelResponse();
        response.setUmmMsgChannelDTO(ummMsgChannelDTO);

        return response;
    }

    @Override
    public QueryUmmMsgChannelResponse queryUmmMsgChannel(Long id) {

        UmmMsgChannel ummMsgChannel = ummMsgChannelRepository.getById(id);
        UmmMsgChannelDTO ummMsgChannelDTO = UmmMsgChannelDTOConverter.INSTANCE.convert(ummMsgChannel);
        QueryUmmMsgChannelResponse response = new QueryUmmMsgChannelResponse();
        response.setUmmMsgChannelDTO(ummMsgChannelDTO);
        return response;
    }

    @Override
    public List<UmmMsgChannel> queryUmmMsgChannelByMsgId(Long msgId) {
        return ummMsgChannelRepository.getByMsgId(msgId);
    }

    @Override
    public PageQueryUmmMsgChannelResponse pageQueryUmmMsgChannel(PageQueryUmmMsgChannelRequest request) {

        UmmMsgChannelDTO ummMsgChannelDTO = request.getUmmMsgChannelDTO();
        UmmMsgChannel ummMsgChannel = UmmMsgChannelDTOConverter.INSTANCE.convert(ummMsgChannelDTO);
        IPage<UmmMsgChannel> byRange = ummMsgChannelRepository.getByRange(request.getPageNum(), request.getPageSize(), ummMsgChannel);

        PageQueryUmmMsgChannelResponse response = new PageQueryUmmMsgChannelResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
            .map(UmmMsgChannelDTOConverter.INSTANCE::convert)
            .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmMsgChannelResponse deleteUmmMsgChannel(Long id) {
        DeleteUmmMsgChannelResponse deleteUmmMsgChannelResponse = new DeleteUmmMsgChannelResponse();
        deleteUmmMsgChannelResponse.setIsDelete(ummMsgChannelRepository.deleteById(id));
        return deleteUmmMsgChannelResponse;
    }

    @Override
    public UpdateUmmMsgChannelResponse updateUmmMsgChannel(UpdateUmmMsgChannelRequest request) {

        UmmMsgChannelDTO ummMsgChannelDTO = request.getUmmMsgChannelDTO();

        UmmMsgChannel ummMsgChannel = UmmMsgChannelDTOConverter.INSTANCE.convert(ummMsgChannelDTO);
        UpdateUmmMsgChannelResponse updateUmmMsgChannelResponse = new UpdateUmmMsgChannelResponse();
        updateUmmMsgChannelResponse.setIsUpdate(ummMsgChannelRepository.update(ummMsgChannel));
        return updateUmmMsgChannelResponse;
    }

    @Mapper
    interface UmmMsgChannelDTOConverter {
        UmmMsgChannelDTOConverter INSTANCE = Mappers.getMapper(UmmMsgChannelDTOConverter.class);

        UmmMsgChannel convert(UmmMsgChannelDTO ummMsgChannelDTO);

        UmmMsgChannelDTO convert(UmmMsgChannel ummMsgChannel);
    }

}
