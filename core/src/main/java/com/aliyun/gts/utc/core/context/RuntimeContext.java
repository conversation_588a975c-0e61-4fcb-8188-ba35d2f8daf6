package com.aliyun.gts.utc.core.context;

import com.yunqiao.foura.service.model.UserInfoDTO;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class RuntimeContext {
    private String jwt;
    private UserInfoDTO userInfo;
    private Map<String, String> map = new HashMap();
    private static ThreadLocal<RuntimeContext> runtimeContext = ThreadLocal.withInitial(RuntimeContext::new);

    private RuntimeContext() {
    }

    public static RuntimeContext get() {
        return runtimeContext.get();
    }

    public static Map<String, String> getMap() {
        return runtimeContext.get().map;
    }

    public static void setMap(Map<String, String> map) {
        runtimeContext.get().map = map;
    }

    public static void setJwt(String token) {
        runtimeContext.get().jwt = token;
    }

    public static void setUserDetail(UserInfoDTO userInfo) {
        runtimeContext.get().userInfo = userInfo;
    }

    public static void set(String key, String value) {
        getMap().put(key, value);
    }

    public static void set(RuntimeContext ctx) {
        runtimeContext.set(ctx);
    }

    public static void remove() {
        runtimeContext.remove();
    }
}