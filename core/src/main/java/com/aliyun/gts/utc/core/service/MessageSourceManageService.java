package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.facade.request.source.CreateSourceSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.DeleteMessageSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.PageQueryMessageSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.UpdateMessageSourceRequest;
import com.aliyun.gts.utc.api.facade.response.source.CreateMessageSourceResponse;
import com.aliyun.gts.utc.api.facade.response.source.DeleteMessageSourceResponse;
import com.aliyun.gts.utc.api.facade.response.source.QueryMessageSourceResponse;
import com.aliyun.gts.utc.api.facade.response.source.UpdateMessageSourceResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
public interface MessageSourceManageService {

    /**
     * 新增消息源
     * @param request 新增消息源请求体
     * @return 新增消息模板返回体
     */
    CreateMessageSourceResponse add(CreateSourceSourceRequest request);

    /**
     * 新增校验消息源编码或名称是否存在重复
     * @param sourceCode
     * @return
     */
    public Boolean checkSourceCode(String sourceCode);


    /**
     * 编辑消息源
     * @param request 请求体
     * @return 编辑结果
     */
    UpdateMessageSourceResponse modify(UpdateMessageSourceRequest request);

    /**
     * 查询消息源详情
     * @param id 消息源id
     * @return 消息源详情
     */
    QueryMessageSourceResponse detail(Long id);

    /**
     * 批量删除消息源
     * @param request 请求体，消息模板id列表
     * @return 删除结果
     */
    DeleteMessageSourceResponse delete(DeleteMessageSourceRequest request);

    /**
     * 分页查询消息源
     * @param request 请求体
     * @return 分页后的消息源列表
     */
    Page<QueryMessageSourceResponse> pageQuery(PageQueryMessageSourceRequest request);
}
