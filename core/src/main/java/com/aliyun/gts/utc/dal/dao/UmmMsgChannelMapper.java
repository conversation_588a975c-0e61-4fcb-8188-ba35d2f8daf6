/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dao;

import com.aliyun.gts.utc.dal.dataobject.UmmMsgChannelDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgChannelMapper.java
 * source table name: umm_msg_channel
 * initial code lines: 25
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Mapper
@Component
public interface UmmMsgChannelMapper extends BaseMapper<UmmMsgChannelDO> {

    Boolean batchAdd(@Param("list") List<UmmMsgChannelDO> ummMsgChannelDOS);
}
