/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dao;

import com.aliyun.gts.utc.api.facade.response.ummmessage.StatisticsMessageResponse;
import com.aliyun.gts.utc.dal.dataobject.UmmMessageDO;
import com.aliyun.gts.utc.dal.dto.PageMessageDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageQueryDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessageMapper.java
 * source table name: umm_message
 * initial code lines: 25
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Mapper
@Component
public interface UmmMessageMapper extends BaseMapper<UmmMessageDO> {

    List<UmmMessageDO> pageQuery(@Param("offset") long offset,
                                   @Param("pageSize") long pageSize,
                                   @Param("dto") PageMessageQueryDTO query);

    Long count(@Param("dto") PageMessageQueryDTO query);

    Boolean updateStatus(@Param("msgId") Long msgId,
                         @Param("successNum") Integer successNum,
                         @Param("tenantCode") String tenantCode);

    List<StatisticsMessageResponse> selectByDaysBefore(@Param("days")int days);
}
