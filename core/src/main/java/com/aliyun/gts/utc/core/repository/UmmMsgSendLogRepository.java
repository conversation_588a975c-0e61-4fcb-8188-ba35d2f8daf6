/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendLogDTO;
import com.aliyun.gts.utc.core.domain.UmmMsgSendLog;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;


public interface UmmMsgSendLogRepository {

    /**
     * 通过 id 获取ummMsgSendLog
     *
     * @param id ummMsgSendLog id
     * @return ummMsgSendLog；null 如果账户不存在
     */
    UmmMsgSendLog getById(Long id);


    Boolean batchCreateUmmMsgSendLog(List<UmmMsgSendLogDTO> ummMsgSendLogDTOS);

    /**
     * 获取一个区间内的ummMsgSendLog
     *
     * @param pageNum
     * @param pageSize      期望个数
     * @return 区间内的ummMsgSendLog列表
     */
    IPage<UmmMsgSendLog> getByRange(Long pageNum, Long pageSize,UmmMsgSendLog ummMsgSendLog);

    /**
     * 添加ummMsgSendLog
     *
     * @param ummMsgSendLog 待添加ummMsgSendLog
     */
    UmmMsgSendLog add(UmmMsgSendLog ummMsgSendLog);

    /**
     * 更新ummMsgSendLog
     *
     * @param ummMsgSendLog 待更新ummMsgSendLog对象
     * @return true，更新成功；false，ummMsgSendLog不存在
     */
    boolean update(UmmMsgSendLog ummMsgSendLog);

    /**
     * 通过 id 删除ummMsgSendLog
     *
     * @param id 账户 id
     * @return true，删除成功；false，ummMsgSendLog不存在
     */
    boolean deleteById(Long id);

}