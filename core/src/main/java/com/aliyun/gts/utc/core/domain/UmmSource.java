package com.aliyun.gts.utc.core.domain;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.gts.utc.api.facade.request.source.MailSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.SmsSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.SxxySourceRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */

@Data
public class UmmSource {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 消息源名称
     */
    private String sourceName;

    /**
     * 消息源编码
     */
    private String sourceCode;

    /**
     * 消息源类型
     */
    private String sourceType;

    /**
     * 消息源参数json存储
     */
    private String sourceParam;

    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人guid
     */
    private String updateUserGuid;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;

    /**
     * 逻辑删除
     */
    private Long isDeleted;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 租户编码
     */
    private String tenantCode;



}
