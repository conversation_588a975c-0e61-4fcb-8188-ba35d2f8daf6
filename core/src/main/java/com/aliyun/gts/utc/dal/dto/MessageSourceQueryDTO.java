package com.aliyun.gts.utc.dal.dto;

import io.swagger.annotations.ApiParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2023/12/6
 */
@Data
public class MessageSourceQueryDTO {
    @ApiParam("消息源名称关键字")
    private String sourceName;

    @ApiParam("消息源类型")
    private String sourceType;

    @ApiParam("提交人关键字")
    private String updateUserName;

    @ApiParam("排序字段 sourceName|sourceType|submitName|gmtModify")
    private String orderField;

    @ApiParam("是否升序")
    private Boolean isAsc;

    @ApiParam("租户code")
    private String tenantCode;
}