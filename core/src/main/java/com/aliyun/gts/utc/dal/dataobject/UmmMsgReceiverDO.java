/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("umm_msg_receiver")
public class UmmMsgReceiverDO {

    /**
    * 主键id
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * msg主键id
    */
    @TableField("msg_id")
    private Long msgId;

    /**
    * 接收主体 ALL,ORG,POS
    */
    @TableField("receiver_type")
    private String receiverType;

    /**
    * ORG guid, POS guid
    */
    @TableField("receiver_guid")
    private String receiverGuid;

    /**
    * 生效
    */
    @TableField("start_date")
    private Date startDate;

    /**
    * 失效日期
    */
    @TableField("end_date")
    private Date endDate;

    /**
    * 租户编码
    */
    @TableField("tenant_code")
    private String tenantCode;

    /**
    * 应用id
    */
    @TableField("app_id")
    private Long appId;

    /**
    * 1启用 2禁用
    */
    @TableField("status")
    private Integer status;

}