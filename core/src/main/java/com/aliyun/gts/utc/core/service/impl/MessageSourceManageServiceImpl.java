package com.aliyun.gts.utc.core.service.impl;


import com.alibaba.fastjson.JSON;
import com.aliyun.gts.utc.api.dto.UmmSourceDTO;
import com.aliyun.gts.utc.api.error.ErrorCodeEnum;
import com.aliyun.gts.utc.api.facade.request.source.*;
import com.aliyun.gts.utc.api.facade.response.source.CreateMessageSourceResponse;
import com.aliyun.gts.utc.api.facade.response.source.DeleteMessageSourceResponse;
import com.aliyun.gts.utc.api.facade.response.source.QueryMessageSourceResponse;
import com.aliyun.gts.utc.api.facade.response.source.UpdateMessageSourceResponse;
import com.aliyun.gts.utc.common.constants.MessageSourceConstant;
import com.aliyun.gts.utc.common.enums.ResponseCodeEnum;
import com.aliyun.gts.utc.core.base.enums.MessageSourceEnum;
import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.factory.MessageSourceFactory;
import com.aliyun.gts.utc.core.repository.UmmSourceRepository;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.aliyun.gts.utc.core.service.MessageSourceManageService;
import com.aliyun.gts.utc.core.utils.AES256Util;
import com.aliyun.gts.utc.core.utils.EnvUtils;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dto.MessageSourceQueryDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.sdk.service.UserContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 消息源服务层
 */
@Service
public class MessageSourceManageServiceImpl implements MessageSourceManageService {
    @Autowired
    private UserContext userContext;

    @Autowired
    private UmmSourceRepository ummSourceRepository;

    @Autowired
    private UmmTemplateRepository ummTemplateRepository;

    @Override
    public CreateMessageSourceResponse add(CreateSourceSourceRequest request) {
        checkSourceNameExist(request.getSourceName());
        UmmSourceDTO UmmSourceDTO = MessageSourceFactory.createSource(request);

        UmmSource ummSource = UmmSourceDTOConverter.INSTANCE.convert(UmmSourceDTO);
        if (EnvUtils.isMockUser()) {
            ummSource.setCreateUserGuid(RuntimeContextUtil.getUserGuid());
            ummSource.setCreateUserName(RuntimeContextUtil.getUserName());
            ummSource.setUpdateUserGuid(RuntimeContextUtil.getUserGuid());
            ummSource.setUpdateUserName(RuntimeContextUtil.getUserName());
        } else {
            UserInfoDTO currentUser = userContext.currentUserInfo(false);
            ummSource.setCreateUserGuid(currentUser.getBaseInfo().getUserCode());
            ummSource.setCreateUserName(currentUser.getBaseInfo().getUserName());
            ummSource.setUpdateUserGuid(currentUser.getBaseInfo().getUserCode());
            ummSource.setUpdateUserName(currentUser.getBaseInfo().getUserName());
        }
        UmmSource add = ummSourceRepository.add(ummSource);

        CreateMessageSourceResponse response = new CreateMessageSourceResponse();
        response.setId(add.getId());

        return response;
    }

    @Override
    public Boolean checkSourceCode(String sourceCode) {
        return ummSourceRepository.checkSourceCode(sourceCode);
    }

    @Override
    public UpdateMessageSourceResponse modify(UpdateMessageSourceRequest request) {
        UpdateMessageSourceResponse response = new UpdateMessageSourceResponse();
        //通过消息源code找到原消息源
        UmmSource ummSource = ummSourceRepository.getBySourceCode(request.getSourceCode());
        if (ummSource == null) {
            throw new RuntimeException(ResponseCodeEnum.SOURCENOTEXIST.getMessage());
        }
        ummSource.setSourceName(request.getSourceName());
        ummSource.setSourceType(request.getSourceType());
        if(MessageSourceEnum.MAIL.getType().equals(ummSource.getSourceType())){
             MailSourceRequest mail = request.getMail();
            if(StringUtils.isNotBlank(mail.getMailPassWord())
                    && !MessageSourceConstant.IDENTIFICATIONPASSWORD.equals(mail.getMailPassWord())) {
                mail.setMailPassWord(AES256Util.encryptAes(mail.getMailPassWord()));
            } else {
                mail.setMailPassWord(mail.getMailPassWord());
            }
            ummSource.setSourceParam(JSON.toJSONString(mail));
        }else if (MessageSourceEnum.SMS.getType().equals(ummSource.getSourceType())){
            SmsSourceRequest sms = request.getSms();
            ummSource.setSourceParam(JSON.toJSONString(sms));
        }else if (MessageSourceEnum.SXXY.getType().equals(ummSource.getSourceType())){
            SxxySourceRequest sxxy = request.getSxxy();
            ummSource.setSourceParam(JSON.toJSONString(sxxy));
        }
        ummSource.setGmtModify(new Date());
        if (EnvUtils.isMockUser()) {
            ummSource.setUpdateUserGuid(RuntimeContextUtil.getUserGuid());
            ummSource.setUpdateUserName(RuntimeContextUtil.getUserName());
        } else {
            UserInfoDTO currentUser = userContext.currentUserInfo(false);
            ummSource.setUpdateUserGuid(currentUser.getBaseInfo().getUserCode());
            ummSource.setUpdateUserName(currentUser.getBaseInfo().getUserName());
        }
        response.setResult(ummSourceRepository.update(ummSource));
        return response;
    }

    @Override
    public QueryMessageSourceResponse detail(Long id) {
        UmmSource ummSource = checkUmmSourceExistById(id);
        UmmSourceDTO ummSourceDTO = UmmSourceDTOConverter.INSTANCE.convert(ummSource);
        return MessageSourceFactory.createSourceDetailResponse(ummSourceDTO);
    }

    @Override
    public DeleteMessageSourceResponse delete(DeleteMessageSourceRequest request) {
        List<Long> idList = request.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            throw new RuntimeException(ErrorCodeEnum.PARAM_ILLEGAL.getDescription());
        }

        // 校验消息源的租户可见性
        idList.parallelStream().forEach(this::checkUmmSourceExistById);
        // 校验消息源是否被使用
        idList.parallelStream().forEach(this::checkUsedSource);

        DeleteMessageSourceResponse response = new DeleteMessageSourceResponse();
        response.setResult(ummSourceRepository.batchDelete(request.getIdList()));
        return response;
    }

    @Override
    public Page<QueryMessageSourceResponse> pageQuery(PageQueryMessageSourceRequest request) {
        Page<UmmSourceDTO> page = new Page<>(request.getPageNum(), request.getPageSize());
        MessageSourceQueryDTO query = MessageSourceFactory.createQuery(request);
        page = ummSourceRepository.pageQuery(page, query);
        List<UmmSourceDTO> records = page.getRecords();
        List<QueryMessageSourceResponse> responseList = new ArrayList<>();
        for (UmmSourceDTO ummSourceDTO : records) {
            responseList.add(MessageSourceFactory.createSourceDetailResponse(ummSourceDTO));
        }
        Page<QueryMessageSourceResponse> response = new Page<>();
        response.setRecords(responseList);
        response.setTotal(page.getTotal());
        response.setCurrent(page.getCurrent());
        response.setTotal(page.getTotal());
        response.setSize(page.getSize());
        return response;
    }

    private void checkSourceNameExist(String sourceName) {
        if (ummSourceRepository.countSourceName(sourceName) > 0) {
            throw new RuntimeException(ResponseCodeEnum.SOURCENAMESAME.getMessage());
        }
    }

    private UmmSource checkUmmSourceExistById(Long id) {
        UmmSource ummSource = ummSourceRepository.getById(id);
        if(ummSource == null || !Objects.equals(ummSource.getTenantCode(), RuntimeContextUtil.getTenantCode())){
            throw new RuntimeException(ResponseCodeEnum.SOURCENOTEXIST.getMessage());
        }
        return ummSource;
    }

    private void checkUsedSource(Long id) {
        List<UmmTemplate> tplBySourceId = ummTemplateRepository.getBySourceId(id);
        if (CollectionUtils.isNotEmpty(tplBySourceId)) {
            throw new RuntimeException(ResponseCodeEnum.SOURCEUSEDBYTPL.getMessage());
        }
    }


    @Mapper
    interface UmmSourceDTOConverter {
        UmmSourceDTOConverter INSTANCE = Mappers.getMapper(UmmSourceDTOConverter.class);

        UmmSource convert(UmmSourceDTO ummSourceDTO);

        UmmSourceDTO convert(UmmSource ummSource);
    }
}
