package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.request.SmsMessageSendRequest;
import com.aliyun.gts.utc.core.service.SmsService;
import com.aliyun.gts.utc.core.utils.EnvUtils;
import com.aliyun.gts.utc.integration.client.alisms.SmsClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.Valid;

@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    private final SmsClient smsClient;

    public SmsServiceImpl(SmsClient smsClient) {
        this.smsClient = smsClient;
    }

    @Override
    public void sendSms(@Valid SmsMessageSendRequest request) {
        try {
            if (EnvUtils.isMockSendSms()) {
                log.info("send sms message is mocked");
                return;
            }
            smsClient.sendSms(request.getTemplateCode(), request.getExtParams(),
                    request.getPhoneNum());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
