package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.response.statistics.ChartResponse;
import com.aliyun.gts.utc.api.facade.response.statistics.FormResponse;
import com.aliyun.gts.utc.api.facade.response.statistics.QueryStatisticsResponse;
import com.aliyun.gts.utc.api.facade.response.ummmessage.StatisticsMessageResponse;
import com.aliyun.gts.utc.core.base.enums.MessageSourceEnum;
import com.aliyun.gts.utc.core.service.MessageStatisticsService;
import com.aliyun.gts.utc.dal.dao.UmmMessageMapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 消息统计服务
 *
 * @author: guoshengLi
 * @create: 2025-02-26 13:11
 * @Description:
 */
@Slf4j
@Service
public class MessageStatisticsServiceImpl implements MessageStatisticsService {
  @Autowired
  private UmmMessageMapper ummMessageMapper;
  final static  String TITLE_TOTAL = "TOTAL";


  private static  DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
  /**
   * 消息请求统计 昨天开始前days天到昨天的数据
   *
   * @param days 昨天前多少天
   */
  @Override
  public QueryStatisticsResponse queryMessageRequest(int days) {
    checkDays(days);
    QueryStatisticsResponse queryStatsResponse = new QueryStatisticsResponse();
    // 查询从昨天开始之前的30天数据
    List<StatisticsMessageResponse> messageStatisticsList = ummMessageMapper.selectByDaysBefore(days);
    List<FormResponse> formResponses = new ArrayList<>();
    // 统计总数
    FormResponse totalFormResponse = new FormResponse();
    totalFormResponse.setTitle(TITLE_TOTAL);
    totalFormResponse.setCount(messageStatisticsList.size());
    formResponses.add(totalFormResponse);
    //查询每天的总请求数
    List<ChartResponse> chartResponses = dealMessageChargeTotal(days, messageStatisticsList, false);
    // 按消息来源类型分组并统计总数
    Map<String, List<StatisticsMessageResponse>> typeToMessagesMap = messageStatisticsList.stream().collect(Collectors.groupingBy(StatisticsMessageResponse::getSourceType));
    List<String> typeList = MessageSourceEnum.getTypeList();
    for (String sourceTypeKey : typeList) {
      FormResponse formResponse = new FormResponse();
      List<StatisticsMessageResponse> messagesByType = typeToMessagesMap.get(sourceTypeKey);
      formResponse.setTitle(sourceTypeKey);
      if (CollectionUtils.isNotEmpty(messagesByType)) {
        formResponse.setCount(messagesByType.size());
      }else {
        formResponse.setCount(0);
      }
      formResponses.add(formResponse);
      List<ChartResponse> typeChartResponses = dealMessageCharge(days,sourceTypeKey, messagesByType, false);
      chartResponses.addAll(typeChartResponses);
    }
    //组装数据
    assemblyData(queryStatsResponse, chartResponses, formResponses);
    return queryStatsResponse;
  }


  /**
   * 消息推送次数 昨天开始前days天到昨天的数据
   * @param days 昨天前多少天
   *
   */
  @Override
  public QueryStatisticsResponse queryMessagePush(int days) {
    checkDays(days);
    QueryStatisticsResponse queryStatsResponse = new QueryStatisticsResponse();
    // 查询从昨天开始之前的30天数据
    List<StatisticsMessageResponse> messageStatisticsList = ummMessageMapper.selectByDaysBefore(days);
    List<FormResponse> formResponses = new ArrayList<>();
    FormResponse totalFormResponse = new FormResponse();
    // 统计总数
    int totalMessages = messageStatisticsList.stream().filter(item -> item.getTotalNum() != null).mapToInt(StatisticsMessageResponse::getTotalNum).sum();
    totalFormResponse.setCount(totalMessages);
    totalFormResponse.setTitle(TITLE_TOTAL);
    // 统计成功数
    int successfulMessages = messageStatisticsList.stream().filter(item -> item.getSuccessNum() != null).mapToInt(StatisticsMessageResponse::getSuccessNum).sum();
    // 计算成功率并格式化为百分比
    dealRate(totalFormResponse, successfulMessages, totalMessages);
    formResponses.add(totalFormResponse);
    List<ChartResponse> chartResponses = dealMessageChargeTotal(days, messageStatisticsList, true);
    // 按消息来源类型分组并统计总数和成功率
    Map<String, List<StatisticsMessageResponse>> typeToMessagesMap = messageStatisticsList.stream().collect(Collectors.groupingBy(StatisticsMessageResponse::getSourceType));
    List<String> typeList = MessageSourceEnum.getTypeList();
    for (String sourceTypeKey : typeList) {
      FormResponse formResponse = new FormResponse();
      formResponse.setTitle(sourceTypeKey);
      List<StatisticsMessageResponse> messagesByType = typeToMessagesMap.get(sourceTypeKey);
      //表格数据
      if (CollectionUtils.isNotEmpty(messagesByType)) {
        int totalMessagesByType = messagesByType.stream().filter(item -> item.getTotalNum() != null).mapToInt(StatisticsMessageResponse::getTotalNum).sum();
        int successfulMessagesByType = messagesByType.stream().filter(item -> item.getSuccessNum() != null).mapToInt(StatisticsMessageResponse::getSuccessNum).sum();
        // 计算成功率并格式化为百分比
        dealRate(formResponse, successfulMessagesByType, totalMessagesByType);
        formResponse.setCount(totalMessagesByType);
      }else {
        formResponse.setCount(0);
        formResponse.setSuccessRate("0%");
      }
      List<ChartResponse> typeChartResponses = dealMessageCharge(days,sourceTypeKey, messagesByType,true);
      chartResponses.addAll(typeChartResponses);
      formResponses.add(formResponse);
    }
    //组装数据
    assemblyData(queryStatsResponse, chartResponses, formResponses);
    return queryStatsResponse;
  }


  /**
   * 计算成功率并格式化为百分比
   * @param formResponse             表格响应对象
   * @param successfulMessagesByType 成功数
   * @param totalMessagesByType      总数
   */
  private void dealRate(FormResponse formResponse, int successfulMessagesByType, int totalMessagesByType) {
    if (totalMessagesByType != 0) {
      String successRate = String.format("%.2f%%", (double) successfulMessagesByType / totalMessagesByType * 100);
      formResponse.setSuccessRate(successRate);
    }else {
      formResponse.setSuccessRate("0%");
    }
  }

  /**
   * 处理消息统计信息，按日期分组并统计总数或请求次数
   * @param days 昨天前多少天
   * @param sourceTypeKey 消息来源类型
   * @param messages      消息列表
   * @param isPush        是否为推送消息
   * @return 统计结果列表
   */
  private List<ChartResponse> dealMessageCharge(int days,String sourceTypeKey, List<StatisticsMessageResponse> messages,boolean isPush) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    // 按日期分组消息
    Map<String, List<StatisticsMessageResponse>> dateToMessagesMap = new HashMap<>();
    if(CollectionUtils.isNotEmpty(messages)){
      dateToMessagesMap = messages.stream().filter(item -> item.getSendTime() != null).collect(Collectors.groupingBy(item -> sdf.format(item.getSendTime())));
    }
    // 按日期分组并统计总数或请求次数
    // 初始化统计结果列表
    List<ChartResponse> chartResponses = new ArrayList<>();
    for (int i = 1; i <= days; i++) {
      //获取当前日期前从昨天开始
      String dateKey = getDateBeforeDays(i);
      List<StatisticsMessageResponse> messagesByDate = dateToMessagesMap.get(dateKey);
      ChartResponse chartResponse = new ChartResponse();
      chartResponse.setDate(dateKey);
      chartResponse.setType(sourceTypeKey);
      // 如果该日期有消息，则进行统计
      if (CollectionUtils.isNotEmpty(messagesByDate)) {
        // 根据是否为推送消息，统计总数或请求次数
        int count = isPush ? messagesByDate.stream().filter(item -> item.getTotalNum() != null).mapToInt(StatisticsMessageResponse::getTotalNum).sum() : messagesByDate.size();
        chartResponse.setNumber(count);
      }else {
        chartResponse.setNumber(0);
      }
      // 添加统计结果到列表
      chartResponses.add(chartResponse);
    }
    return chartResponses;
  }

  /**
   * 处理日期总数统计信息，按日期分组并统计总数或请求次数
   * @param days 昨天前多少天
   * @param messageStatisticsList      消息列表
   * @param isPush        是否为推送消息
   * @return 统计结果列表
   */
  private List<ChartResponse> dealMessageChargeTotal(int days,List<StatisticsMessageResponse> messageStatisticsList,boolean isPush){
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    // 按日期分组并统计总数
    Map<String, List<StatisticsMessageResponse>> dateToMessagesMap = messageStatisticsList
            .stream().filter(item -> item.getSendTime() != null)
            .collect(Collectors.groupingBy(item -> sdf.format(item.getSendTime())));
    List<ChartResponse> chartResponses = new ArrayList<>();
    for (int i = 1; i <= days; i++) {
      //获取当前日期前从昨天开始
      String dateKey = getDateBeforeDays(i);
      List<StatisticsMessageResponse> messagesByDate = dateToMessagesMap.get(dateKey);
      ChartResponse chartResponse = new ChartResponse();
      chartResponse.setDate(dateKey);
      chartResponse.setType(TITLE_TOTAL);
      // 如果该日期有消息，则进行统计
      if (CollectionUtils.isNotEmpty(messagesByDate)) {
        // 根据是否为推送消息，统计总数或请求次数
        int count = isPush ? messagesByDate.stream().filter(item -> item.getTotalNum() != null).mapToInt(StatisticsMessageResponse::getTotalNum).sum() : messagesByDate.size();
        chartResponse.setNumber(count);
      }else {
        chartResponse.setNumber(0);
      }
      chartResponses.add(chartResponse);
    }
    return chartResponses;
  }

  /**
   * 组装数据
   *
   * @param queryStatsResponse 查询统计响应
   * @param chartResponses     图表响应
   * @param formResponses      表单响应
   */
  private void assemblyData(QueryStatisticsResponse queryStatsResponse, List<ChartResponse> chartResponses, List<FormResponse> formResponses) {
    chartResponses.sort(Comparator.comparing(ChartResponse::getDate));
    queryStatsResponse.setCharList(chartResponses);
    queryStatsResponse.setFromList(formResponses);
  }

  /**
   * 检查天数参数
   * @param days 天数
   */
  private void checkDays(int days) {
    if (Objects.isNull(days)) {
      //参数异常
      throw new RuntimeException("参数不为空");
    }
    if (days < 0) {
      throw new RuntimeException("参数必须大于等于1");
    }
  }

  private  String getDateBeforeDays(int days) {
    LocalDate currentDate = LocalDate.now();
    LocalDate dateBefore = currentDate.minusDays(days);
    String format = dateBefore.format(FORMATTER);
    return format;
  }


}
