/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dao;

import com.aliyun.gts.utc.dal.dataobject.UmmTemplateDO;
import com.aliyun.gts.utc.dal.dto.MessageTemplateQueryDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageTemplateDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmTemplateMapper.java
 * source table name: umm_template
 * initial code lines: 25
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Mapper
@Component
public interface UmmTemplateMapper extends BaseMapper<UmmTemplateDO> {

    /**
     * 分页查询
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<PageMessageTemplateDTO> pageQuery(Page<PageMessageTemplateDTO> page, @Param("dto") MessageTemplateQueryDTO query);
}