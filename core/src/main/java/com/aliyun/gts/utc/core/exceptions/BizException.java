package com.aliyun.gts.utc.core.exceptions;

public class BizException extends RuntimeException {
    private static final long serialVersionUID = 9168815076571525711L;
    private String errorCode;
    private String errorMsg;

    public BizException() {
    }

    public BizException(String errorMsg) {
        super(errorMsg);
        this.errorMsg = errorMsg;
    }

    public BizException(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public String getErrorMsg() {
        return this.errorMsg;
    }
}
