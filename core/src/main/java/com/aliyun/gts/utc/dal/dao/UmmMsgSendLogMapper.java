/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dao;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendLogDTO;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgSendLogDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendLogMapper.java
 * source table name: umm_msg_send_log
 * initial code lines: 25
 * initial generated date: 2021-06-03 22:51:27
 *
 * @date 2021-06-03 22:51:27
 */
@Mapper
@Component
public interface UmmMsgSendLogMapper extends BaseMapper<UmmMsgSendLogDO> {

    Boolean batchAdd(@Param("dto") List<UmmMsgSendLogDTO> ummMsgSendLogDTOS);

}