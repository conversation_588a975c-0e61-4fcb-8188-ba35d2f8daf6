/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.api.error.ErrorCodeEnum;
import com.aliyun.gts.utc.common.constants.MessageConstant;
import com.aliyun.gts.utc.common.constants.MessageTemplateConstant;
import com.aliyun.gts.utc.core.constants.StatusEnum;
import com.aliyun.gts.utc.core.domain.UmmCategory;
import com.aliyun.gts.utc.core.exceptions.BizException;
import com.aliyun.gts.utc.core.repository.UmmCategoryRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dao.UmmCategoryMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmCategoryDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmCategoryRepositoryImpl.java
 * source table name: umm_category
 * initial code lines: 146
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Repository
public class UmmCategoryRepositoryImpl implements UmmCategoryRepository {

    @Autowired
    private UmmCategoryMapper ummCategoryMapper;

    @Override
    public UmmCategory getById(Long id) {
        LambdaQueryWrapper<UmmCategoryDO> queryWrapper = new LambdaQueryWrapper<UmmCategoryDO>()
                .eq(UmmCategoryDO::getId, id)
                .eq(UmmCategoryDO::getTenantCode, RuntimeContextUtil.getTenantCode());

        UmmCategoryDO ummCategoryDO = ummCategoryMapper.selectOne(queryWrapper);
        return UmmCategoryDOConverter.INSTANCE.convert(ummCategoryDO);
    }

    @Override
    public List<UmmCategory> getByIds(List<Long> ids) {
        LambdaQueryWrapper<UmmCategoryDO> queryWrapper = new QueryWrapper<UmmCategoryDO>()
                .lambda().in(UmmCategoryDO::getId, ids)
                .eq(UmmCategoryDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .eq(UmmCategoryDO::getIsDeleted, MessageConstant.NOT_DELETED);
        return ummCategoryMapper.selectList(queryWrapper).stream().map(UmmCategoryDOConverter.INSTANCE::convert).filter(o -> StatusEnum.ENABLE.getState() == o.getStatus()).collect(Collectors.toList());

    }

    @Override
    public UmmCategory getByCode(String code) {
        LambdaQueryWrapper<UmmCategoryDO> queryWrapper = new QueryWrapper<UmmCategoryDO>()
                .lambda().eq(UmmCategoryDO::getCategoryCode, code)
                .eq(UmmCategoryDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .eq(UmmCategoryDO::getIsDeleted, MessageConstant.NOT_DELETED).last("limit 1");
        UmmCategoryDO ummCategoryDO = ummCategoryMapper.selectOne(queryWrapper);
        return UmmCategoryDOConverter.INSTANCE.convert(ummCategoryDO);
    }

    @Override
    public IPage<UmmCategory> getByRange(long pageNum, long pageSize,
                                         UmmCategory ummCategory) {

        Page<UmmCategoryDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        UmmCategoryDO ummCategoryDO =
                UmmCategoryDOConverter.INSTANCE.convert(ummCategory);
        ummCategoryDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        QueryWrapper<UmmCategoryDO> queryWrapper = new QueryWrapper();
        queryWrapper.setEntity(ummCategoryDO);
        IPage<UmmCategoryDO> iPage = ummCategoryMapper.selectPage(page,
                queryWrapper);
        List<UmmCategory> list = iPage.getRecords().stream()
                .map(UmmCategoryDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
        Page<UmmCategory> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setSize(iPage.getSize());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    @Override
    public UmmCategory add(UmmCategory ummCategory) {

        UmmCategoryDO ummCategoryDO = UmmCategoryDOConverter.INSTANCE.convert(ummCategory);
        ummCategoryDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        ummCategoryMapper.insert(ummCategoryDO);
        ummCategory.setId(ummCategoryDO.getId());
        return ummCategory;
    }

    @Override
    public boolean update(UmmCategory ummCategory) {

        UmmCategoryDO ummCategoryDO = UmmCategoryDOConverter.INSTANCE.convert(ummCategory);
        LambdaQueryWrapper<UmmCategoryDO> queryWrapper = new LambdaQueryWrapper<UmmCategoryDO>()
                .eq(UmmCategoryDO::getId, ummCategory.getId())
                .eq(UmmCategoryDO::getTenantCode, RuntimeContextUtil.getTenantCode());

        UmmCategoryDO newUmmCategoryDO = ummCategoryMapper.selectOne(queryWrapper);
        if (newUmmCategoryDO == null) {
            return false;
        }
        if (ummCategoryDO.getGuid() != null) {
            newUmmCategoryDO.setGuid(ummCategoryDO.getGuid());
        }
        if (ummCategoryDO.getCategoryName() != null) {
            newUmmCategoryDO.setCategoryName(ummCategoryDO.getCategoryName());
        }
        if (ummCategoryDO.getParentId() != null) {
            newUmmCategoryDO.setParentId(ummCategoryDO.getParentId());
        }
        if (ummCategoryDO.getStatus() != null) {
            newUmmCategoryDO.setStatus(ummCategoryDO.getStatus());
        }
        if (ummCategoryDO.getCreateUserGuid() != null) {
            newUmmCategoryDO.setCreateUserGuid(ummCategoryDO.getCreateUserGuid());
        }
        if (ummCategoryDO.getCreateUserName() != null) {
            newUmmCategoryDO.setCreateUserName(ummCategoryDO.getCreateUserName());
        }
        if (ummCategoryDO.getUpdateUserGuid() != null) {
            newUmmCategoryDO.setUpdateUserGuid(ummCategoryDO.getUpdateUserGuid());
        }
        if (ummCategoryDO.getUpdateUserName() != null) {
            newUmmCategoryDO.setUpdateUserName(ummCategoryDO.getUpdateUserName());
        }
        if (ummCategoryDO.getGmtCreate() != null) {
            newUmmCategoryDO.setGmtCreate(ummCategoryDO.getGmtCreate());
        }
        if (ummCategoryDO.getGmtModify() != null) {
            newUmmCategoryDO.setGmtModify(ummCategoryDO.getGmtModify());
        }
        if (ummCategoryDO.getFeatures() != null) {
            newUmmCategoryDO.setFeatures(ummCategoryDO.getFeatures());
        }
        if (ummCategoryDO.getIsDeleted() != null) {
            newUmmCategoryDO.setIsDeleted(ummCategoryDO.getIsDeleted());
        }
        if (ummCategoryDO.getVersion() != null) {
            newUmmCategoryDO.setVersion(ummCategoryDO.getVersion());
        }
        if (ummCategoryDO.getAppId() != null) {
            newUmmCategoryDO.setAppId(ummCategoryDO.getAppId());
        }

        ummCategoryMapper.updateById(newUmmCategoryDO);
        return true;
    }

    @Override
    public boolean deleteById(Long id) {
        LambdaQueryWrapper<UmmCategoryDO> queryWrapper = new LambdaQueryWrapper<UmmCategoryDO>()
                .eq(UmmCategoryDO::getId, id)
                .eq(UmmCategoryDO::getTenantCode, RuntimeContextUtil.getTenantCode());

        UmmCategoryDO ummCategoryDO = ummCategoryMapper.selectOne(queryWrapper);
        if (ummCategoryDO == null) {
            return false;
        }
        return ummCategoryMapper.deleteById(id) > 0;
    }

    /**
     * 获取此id下级栏目id列表
     *
     * @param id 主键
     * @return 下级栏目id列表
     */
    @Override
    public List<UmmCategory> getByParentId(Long id) {
        LambdaQueryWrapper<UmmCategoryDO> queryWrapper = new QueryWrapper<UmmCategoryDO>()
                .lambda().eq(UmmCategoryDO::getParentId, id)
                .eq(UmmCategoryDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .eq(UmmCategoryDO::getIsDeleted, MessageConstant.NOT_DELETED);
        return ummCategoryMapper.selectList(queryWrapper)
                .stream().map(UmmCategoryDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<UmmCategory> getByParentIds(List<Long> ids) {
        LambdaQueryWrapper<UmmCategoryDO> queryWrapper = new QueryWrapper<UmmCategoryDO>()
                .lambda()
                .eq(UmmCategoryDO::getStatus, StatusEnum.ENABLE.getState())
                .eq(UmmCategoryDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .in(UmmCategoryDO::getParentId, ids);
        return ummCategoryMapper.selectList(queryWrapper)
                .stream().map(UmmCategoryDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public void checkCategoryExistInCurrentTenantCode(Long id) {
        UmmCategory ummCategory = getById(id);
        if (ummCategory == null || !Objects.equals(ummCategory.getTenantCode(), RuntimeContextUtil.getTenantCode())) {
            throw new BizException(ErrorCodeEnum.NO_PERMISSION.getDetailCode(), "当前租户下不存在此栏目，不支持操作");
        }
    }

    /**
     * 获取告警通知栏目id
     *
     * @return 栏目id
     */
    @Override
    public Long getWarningId() {
        LambdaQueryWrapper<UmmCategoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UmmCategoryDO::getCategoryName, MessageTemplateConstant.WARNING_CATEGORY)
                .eq(UmmCategoryDO::getIsDeleted, MessageTemplateConstant.NOT_DELETED)
                .eq(UmmCategoryDO::getStatus, MessageTemplateConstant.ENABLE)
                .eq(UmmCategoryDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .last("limit 1");
        UmmCategoryDO categoryDO = ummCategoryMapper.selectOne(queryWrapper);
        return null == categoryDO ? null : categoryDO.getId();
    }

    @Mapper
    interface UmmCategoryDOConverter {
        UmmCategoryDOConverter INSTANCE = Mappers.getMapper(UmmCategoryDOConverter.class);

        UmmCategoryDO convert(UmmCategory ummCategory);

        UmmCategory convert(UmmCategoryDO ummCategoryDO);
    }

}