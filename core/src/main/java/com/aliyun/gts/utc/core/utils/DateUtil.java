package com.aliyun.gts.utc.core.utils;

import com.google.common.collect.Lists;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.TimeZone;
import javax.annotation.CheckForNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DateUtil {
    private static final Logger log = LoggerFactory.getLogger(DateUtil.class);
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss";
    public static final String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DEFAULT_DATESTAMP_FORMAT = "yyyyMMddHHmmssSSS";
    public static final String NYRSF_DATESTAMP_FORMAT = "yyyyMMddHHmm";

    private DateUtil() {
    }

    public static Date getNow() {
        return Calendar.getInstance().getTime();
    }

    public static String getNow(String format) {
        return dateToStr(getNow(), format);
    }

    public static String getCurrentDate() {
        return getNow("yyyy-MM-dd");
    }

    public static String getCurrentTime() {
        return getNow("HH:mm:ss");
    }

    public static String getCurrentDateTime() {
        return getNow("yyyy-MM-dd HH:mm:ss");
    }

    public static Date getTomorrow() {
        return getEndDateByDay(getNow(), 1);
    }

    public static String getTomorrow(String format) {
        return dateToStr(getTomorrow(), format);
    }

    public static Date getYesterday() {
        return getEndDateByDay(getNow(), -1);
    }

    public static String getYesterday(String format) {
        return dateToStr(getYesterday(), format);
    }

    public static Date getDate(int year, int month, int day) {
        Calendar c = new GregorianCalendar();
        c.set(year, month - 1, day);
        return c.getTime();
    }

    public static Date getDate(int year, int month, int day, int hour, int minute, int second) {
        Calendar c = new GregorianCalendar();
        c.set(year, month - 1, day, hour, minute, second);
        return c.getTime();
    }

    public static String getCurrentYear() {
        return getNow("yyyy");
    }

    public static String getPreYear() {
        return dateToStr(getEndDateByYear(getNow(), -1), "yyyy");
    }

    public static String getNextYear() {
        return dateToStr(getEndDateByYear(getNow(), 1), "yyyy");
    }

    public static String getCurrentMonth() {
        return getNow("MM");
    }

    public static String getPreMonth() {
        return dateToStr(getEndDateByMonth(getNow(), -1), "MM");
    }

    public static String getNextMonth() {
        return dateToStr(getEndDateByMonth(getNow(), 1), "MM");
    }

    public static String getCurrentDay() {
        return getNow("dd");
    }

    public static String getPreDay() {
        return getYesterday("dd");
    }

    public static String getNextDay() {
        return getTomorrow("dd");
    }

    public static String dateToStr(Date date, String format) {
        DateFormat dateFormat = new SimpleDateFormat(format);
        String dateString = dateFormat.format(date);
        return dateString;
    }

    public static String dateToStr(Date date) {
        return dateToStr(date, "yyyy-MM-dd HH:mm:ss");
    }

    public static String getTimestamp() {
        return dateToStr(getNow(), "yyyyMMddHHmmssSSS");
    }

    public static String getNyrTimestamp() {
        return dateToStr(getNow(), "yyyyMMddHHmm");
    }

    public static String getTimestamp(String format) {
        return dateToStr(getNow(), format);
    }

    public static Date strToDate(String date, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);

        try {
            return dateFormat.parse(date);
        } catch (ParseException var4) {
            log.error("Can not convert String to Date, cause: " + var4.getMessage());
            return null;
        }
    }

    public static Date strToDate(String date) {
        return strToDate(date, "yyyy-MM-dd HH:mm:ss");
    }

    public static Date getEndDate(Date sDate, int type, int interval) {
        Calendar sCalendar = Calendar.getInstance();
        sCalendar.setTime(sDate);
        sCalendar.add(type, interval);
        return sCalendar.getTime();
    }

    public static Date getEndDateByMilliSecond(Date sDate, int milliSecond) {
        return getEndDate(sDate, 14, milliSecond);
    }

    public static Date getEndDateBySecond(Date sDate, int second) {
        return getEndDate(sDate, 13, second);
    }

    public static Date getEndDateByMinute(Date sDate, int minute) {
        return getEndDate(sDate, 12, minute);
    }

    public static Date getEndDateByHour(Date sDate, int hour) {
        return getEndDate(sDate, 10, hour);
    }

    public static Date getEndDateByDay(Date sDate, int days) {
        return getEndDate(sDate, 5, days);
    }

    public static Date getEndDateByMonth(Date sDate, int monthes) {
        return getEndDate(sDate, 2, monthes);
    }

    public static Date getEndDateByYear(Date sDate, int years) {
        return getEndDate(sDate, 1, years);
    }

    public static long getDiffByMilliSecond(Date sDate, Date eDate) {
        return eDate.getTime() - sDate.getTime();
    }

    public static long getDiffBySecond(Date sDate, Date eDate) {
        return getDiffByMilliSecond(sDate, eDate) / 1000L;
    }

    public static long getDiffByMinute(Date sDate, Date eDate) {
        return getDiffBySecond(sDate, eDate) / 60L;
    }

    public static long getDiffByHour(Date sDate, Date eDate) {
        return getDiffByMinute(sDate, eDate) / 60L;
    }

    public static long getDiffByDay(Date sDate, Date eDate) {
        return getDiffByHour(sDate, eDate) / 24L;
    }

    public static long getDiffByMonth(Date sDate, Date eDate) {
        Calendar start = Calendar.getInstance();
        start.setTime(sDate);
        Calendar end = Calendar.getInstance();
        end.setTime(eDate);
        int months = end.get(2) - start.get(2);
        int yearMonths = (end.get(1) - start.get(1)) * 12;
        return (long)(yearMonths + months);
    }

    public static int getDayOfWeek(Date date) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(date);
        return cal.get(7);
    }

    public static int getDayOfMonth(Date date) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(date);
        return cal.get(5);
    }

    public static int getDaysOfMonth(Date date) {
        int[] mArray = new int[]{31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        if (isLeapYear(date)) {
            mArray[1] = 29;
        }

        return mArray[getMonthOfYear(date) - 1];
    }

    public static int getDayOfYear(Date date) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(date);
        return cal.get(6);
    }

    public static int getWeekOfMonth(Date date) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(date);
        return cal.get(4);
    }

    public static int getWeekOfYear(Date date) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(date);
        return cal.get(3);
    }

    public static int getMonthOfYear(Date date) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(date);
        return cal.get(2) + 1;
    }

    public static String[] getSelectYear(int startYear, int endYear) {
        if (startYear <= endYear && startYear >= 0 && endYear >= 0) {
            String[] yearList = new String[endYear - startYear + 1];

            for(int i = startYear; i <= endYear; ++i) {
                yearList[i - startYear] = String.valueOf(i);
            }

            return yearList;
        } else {
            return new String[0];
        }
    }

    public static String[] getSelectMonth(int startMonth, int endMonth) {
        if (startMonth > endMonth) {
            return new String[0];
        } else {
            startMonth = startMonth >= 0 && startMonth <= 12 ? startMonth : 1;
            endMonth = endMonth >= 0 && endMonth <= 12 ? endMonth : 12;
            String[] monthList = new String[endMonth - startMonth + 1];

            for(int i = startMonth; i <= endMonth; ++i) {
                monthList[i - startMonth] = String.valueOf(i);
            }

            return monthList;
        }
    }

    public static boolean isInRange(Date date, Date start, Date end) {
        return compareDate(start, date) <= 0 && compareDate(end, date) >= 0;
    }

    public static int compareDate(Date sDate, Date eDate) {
        Calendar sCalendar = Calendar.getInstance();
        sCalendar.setTime(sDate);
        Calendar eCalendar = Calendar.getInstance();
        eCalendar.setTime(eDate);
        return sCalendar.compareTo(eCalendar);
    }

    public static Date transformDateBetweenTimeZone(@CheckForNull Date date, TimeZone sourceTimeZone, TimeZone targetTimeZone) {
        assert date != null;

        long targetTime = date.getTime() - (long)sourceTimeZone.getRawOffset() + (long)targetTimeZone.getRawOffset();
        return new Date(targetTime);
    }

    public static Date transformDateBetweenTimeZone(String dateStr, String dateFormat, TimeZone sourceTimeZone, TimeZone targetTimeZone) {
        Date date = strToDate(dateStr, dateFormat);
        return transformDateBetweenTimeZone(date, sourceTimeZone, targetTimeZone);
    }

    public static String[] getAllTimeZone() {
        List<String> v = Lists.newArrayList();
        String[] timeZones = TimeZone.getAvailableIDs();

        for(int i = 0; i < timeZones.length; ++i) {
            v.add(timeZones[i]);
        }

        Collections.sort(v, String.CASE_INSENSITIVE_ORDER);
        timeZones = (String[])v.toArray(timeZones);
        v = null;
        return timeZones;
    }

    private static boolean isLeapYear(Date date) {
        int year = Integer.valueOf(dateToStr(date, "yyyy"));
        return year % 4 == 0 && year % 100 != 0 || year % 400 == 0;
    }

    public static List<String> getWeekdayStrs() {
        List<String> weekDayStrs = new ArrayList();
        List<Date> weekDays = getWeekdays();
        weekDays.forEach((weekDay) -> {
            weekDayStrs.add(dateToStr(weekDay, "yyyy-MM-dd"));
        });
        return weekDayStrs;
    }

    public static List<Date> getWeekdays() {
        List<Date> weekDays = new ArrayList();
        Calendar calendar = Calendar.getInstance();
        setToFirstDay(calendar);

        for(int i = 0; i < 7; ++i) {
            weekDays.add(calendar.getTime());
            calendar.add(5, 1);
        }

        return weekDays;
    }

    private static void setToFirstDay(Calendar calendar) {
        while(calendar.get(7) != 2) {
            calendar.add(5, -1);
        }

    }

    public static Date getFirstDayOfMonth(Date date) {
        return getFirstDayOfMonth(date, 0);
    }

    public static Date getFirstDayOfMonth(Date date, int months) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(2, months);
        calendar.set(5, 1);
        return getDateZero(calendar);
    }

    public static Date getDateZero(Date date) {
        return getDateZero(date, 0);
    }

    public static Date getDateZero(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(5, days);
        return getDateZero(calendar);
    }

    private static Date getDateZero(Calendar calendar) {
        calendar.set(11, 0);
        calendar.set(12, 0);
        calendar.set(13, 0);
        calendar.set(14, 0);
        return calendar.getTime();
    }
}
