package com.aliyun.gts.utc.core.converter;

import com.aliyun.gts.bpaas.framework.api.util.StringUtil;
import com.aliyun.gts.utc.core.constants.ErrorCodeEnum;
import com.aliyun.gts.utc.core.exceptions.BizException;
import com.aliyun.gts.utc.core.response.ResponseUtil;
import com.aliyun.gts.utc.core.response.ResultObject;
import org.mybatis.spring.MyBatisSystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.util.Iterator;
import java.util.List;

@Primary
@RestControllerAdvice(
    basePackages = {"com.aliyun.gts.utc"}
)
public class CustomExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(CustomExceptionHandler.class);

    public CustomExceptionHandler() {
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({Exception.class})
    public ResultObject<Object> defaultErrorHandler(Exception e) {
        log.error(e.getMessage(), e);
        String errorCode = ErrorCodeEnum.UNKNOWN_ERROR.getDetailCode();
        String errorMsg = ErrorCodeEnum.UNKNOWN_ERROR.getDescription();
        return ResponseUtil.getResult(errorCode, errorMsg, Boolean.FALSE);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({MissingServletRequestParameterException.class})
    public ResultObject<Object> handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.error(e.getMessage(), e);
        String errorCode = ErrorCodeEnum.MISSINGPARAMETER_ERROR.getDetailCode();
        String errorMsg = ErrorCodeEnum.MISSINGPARAMETER_ERROR.getDescription();
        return ResponseUtil.getResult(errorCode, errorMsg, Boolean.FALSE);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({HttpMessageNotReadableException.class})
    public ResultObject<Object> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error(e.getMessage(), e);
        String errorCode = ErrorCodeEnum.HTTPMESSAGENOTREADABLE_ERROR.getDetailCode();
        String errorMsg = ErrorCodeEnum.HTTPMESSAGENOTREADABLE_ERROR.getDescription();
        return ResponseUtil.getResult(errorCode, errorMsg, Boolean.FALSE);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({MaxUploadSizeExceededException.class})
    public ResultObject<Object> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error(e.getMessage(), e);
        String errorCode = ErrorCodeEnum.UPLOAD_ERROR.getDetailCode();
        String errorMsg = ErrorCodeEnum.UPLOAD_ERROR.getDescription();
        return ResponseUtil.getResult(errorCode, errorMsg, Boolean.FALSE);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({MyBatisSystemException.class})
    public ResultObject<Object> handleKSQLException(MyBatisSystemException e) {
        log.error(e.getMessage(), e);
        String errorCode = ErrorCodeEnum.DATABASE_ERROR.getDetailCode();
        String errorMsg = ErrorCodeEnum.DATABASE_ERROR.getDescription();
        return ResponseUtil.getResult(errorCode, errorMsg, Boolean.FALSE);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public ResultObject<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        BindingResult result = e.getBindingResult();
        List<FieldError> fieldErrors = result.getFieldErrors();
        StringBuilder builder = new StringBuilder();
        Iterator var5 = fieldErrors.iterator();

        while(var5.hasNext()) {
            FieldError error = (FieldError)var5.next();
            builder.append(error.getDefaultMessage() + "\n");
        }

        String errorCode = ErrorCodeEnum.ARGUMENTNOTVALID_ERROR.getDetailCode();
        String errorMsg = ErrorCodeEnum.ARGUMENTNOTVALID_ERROR.getDescription();
        return ResponseUtil.getResult(errorCode, errorMsg + builder.toString(), Boolean.FALSE);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({BindException.class})
    public ResultObject<Object> handleBindException(BindException e) {
        log.error(e.getMessage(), e);
        String errorCode = ErrorCodeEnum.PARAMETERBIND_ERROR.getDetailCode();
        String errorMsg = ErrorCodeEnum.PARAMETERBIND_ERROR.getDescription();
        return ResponseUtil.getResult(errorCode, errorMsg, Boolean.FALSE);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({ConstraintViolationException.class})
    public ResultObject<Object> handleConstraintViolationException(ConstraintViolationException e) {
        log.error(e.getMessage(), e);
        StringBuilder errorMsg = new StringBuilder();
        Iterator var3 = e.getConstraintViolations().iterator();

        while(var3.hasNext()) {
            ConstraintViolation<?> constraintViolation = (ConstraintViolation)var3.next();
            errorMsg.append(constraintViolation.getMessage());
            errorMsg.append(";\n");
        }

        return ResponseUtil.getResult(ErrorCodeEnum.CONSTRAINTVIOLATION_ERROR.getDetailCode(), ErrorCodeEnum.CONSTRAINTVIOLATION_ERROR.getDescription() + errorMsg.toString(), Boolean.FALSE);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({ValidationException.class})
    public ResultObject<Object> handleValidationException(ValidationException e) {
        log.error(e.getMessage(), e);
        String errorCode = ErrorCodeEnum.VALIDATION_ERROR.getDetailCode();
        String errorMsg = ErrorCodeEnum.VALIDATION_ERROR.getDescription();
        return ResponseUtil.getResult(errorCode, errorMsg, Boolean.FALSE);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({BizException.class})
    public ResultObject<Object> handleServiceException(BizException e) {
        log.error(e.getMessage(), e);
        String errorCode = StringUtil.isNotBlank(e.getErrorCode()) ? e.getErrorCode() : "-1";
        String errorMsg = StringUtil.isNotBlank(e.getMessage()) ? e.getMessage() : "failure";
        return ResponseUtil.getResult(errorCode, errorMsg, Boolean.FALSE);
    }
}
