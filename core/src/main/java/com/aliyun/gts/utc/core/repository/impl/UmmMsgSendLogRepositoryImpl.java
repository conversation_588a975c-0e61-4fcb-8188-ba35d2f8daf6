/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendLogDTO;
import com.aliyun.gts.utc.core.domain.UmmMsgSendLog;
import com.aliyun.gts.utc.core.repository.UmmMsgSendLogRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dao.UmmMsgSendLogMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgSendLogDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendLogRepositoryImpl.java
 * source table name: umm_msg_send_log
 * initial code lines: 122
 * initial generated date: 2021-06-03 22:51:27
 *
 * @date 2021-06-03 22:51:27
 */
@Repository
public class UmmMsgSendLogRepositoryImpl implements UmmMsgSendLogRepository {

    @Autowired
    private UmmMsgSendLogMapper ummMsgSendLogMapper;

    @Override
    public UmmMsgSendLog getById(Long id) {
        LambdaQueryWrapper<UmmMsgSendLogDO> queryWrapper = new QueryWrapper<UmmMsgSendLogDO>().lambda()
                .eq(UmmMsgSendLogDO::getId, id)
                .eq(UmmMsgSendLogDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMsgSendLogDO ummMsgSendLogDO = ummMsgSendLogMapper.selectOne(queryWrapper);
        return UmmMsgSendLogDOConverter.INSTANCE.convert(ummMsgSendLogDO);
    }

    @Override
    public Boolean batchCreateUmmMsgSendLog(List<UmmMsgSendLogDTO> ummMsgSendLogDTOS) {
        return ummMsgSendLogMapper.batchAdd(ummMsgSendLogDTOS);
    }

    @Override
    public IPage<UmmMsgSendLog> getByRange(Long pageNum, Long pageSize, UmmMsgSendLog ummMsgSendLog) {

        Page<UmmMsgSendLogDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        UmmMsgSendLogDO ummMsgSendLogDO = UmmMsgSendLogDOConverter.INSTANCE.convert(ummMsgSendLog);
        ummMsgSendLogDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        QueryWrapper<UmmMsgSendLogDO> queryWrapper = new QueryWrapper();
        queryWrapper.setEntity(ummMsgSendLogDO);
        IPage<UmmMsgSendLogDO> iPage = ummMsgSendLogMapper.selectPage(page, queryWrapper);
        List<UmmMsgSendLog> list = iPage.getRecords().stream()
            .map(UmmMsgSendLogDOConverter.INSTANCE::convert)
            .collect(Collectors.toList());
        Page<UmmMsgSendLog> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setSize(iPage.getSize());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    @Override
    public UmmMsgSendLog add(UmmMsgSendLog ummMsgSendLog) {

        UmmMsgSendLogDO ummMsgSendLogDO = UmmMsgSendLogDOConverter.INSTANCE.convert(ummMsgSendLog);
        ummMsgSendLogDO.setTenantCode(RuntimeContextUtil.getTenantCode());
        ummMsgSendLogMapper.insert(ummMsgSendLogDO);
        ummMsgSendLog.setId(ummMsgSendLogDO.getId());
        return ummMsgSendLog;
    }

    @Override
    public boolean update(UmmMsgSendLog ummMsgSendLog) {

        UmmMsgSendLogDO ummMsgSendLogDO = UmmMsgSendLogDOConverter.INSTANCE.convert(ummMsgSendLog);
        LambdaQueryWrapper<UmmMsgSendLogDO> queryWrapper = new QueryWrapper<UmmMsgSendLogDO>().lambda()
                .eq(UmmMsgSendLogDO::getId, ummMsgSendLogDO.getId())
                .eq(UmmMsgSendLogDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMsgSendLogDO newUmmMsgSendLogDO = ummMsgSendLogMapper.selectOne(queryWrapper);

        if (newUmmMsgSendLogDO == null) {
            return false;
        }
        if (ummMsgSendLogDO.getMsgSendId() != null) {
            newUmmMsgSendLogDO.setMsgSendId(ummMsgSendLogDO.getMsgSendId());
        }
        if (ummMsgSendLogDO.getMessageIn() != null) {
            newUmmMsgSendLogDO.setMessageIn(ummMsgSendLogDO.getMessageIn());
        }
        if (ummMsgSendLogDO.getMessageOut() != null) {
            newUmmMsgSendLogDO.setMessageOut(ummMsgSendLogDO.getMessageOut());
        }
        if (ummMsgSendLogDO.getSendTime() != null) {
            newUmmMsgSendLogDO.setSendTime(ummMsgSendLogDO.getSendTime());
        }
        if (ummMsgSendLogDO.getStatus() != null) {
            newUmmMsgSendLogDO.setStatus(ummMsgSendLogDO.getStatus());
        }
        if (ummMsgSendLogDO.getGmtCreate() != null) {
            newUmmMsgSendLogDO.setGmtCreate(ummMsgSendLogDO.getGmtCreate());
        }
        if (ummMsgSendLogDO.getGmtModify() != null) {
            newUmmMsgSendLogDO.setGmtModify(ummMsgSendLogDO.getGmtModify());
        }

        ummMsgSendLogMapper.updateById(newUmmMsgSendLogDO);
        return true;
    }

    @Override
    public boolean deleteById(Long id) {
        LambdaQueryWrapper<UmmMsgSendLogDO> queryWrapper = new QueryWrapper<UmmMsgSendLogDO>().lambda()
                .eq(UmmMsgSendLogDO::getId, id)
                .eq(UmmMsgSendLogDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMsgSendLogDO ummMsgSendLogDO = ummMsgSendLogMapper.selectOne(queryWrapper);
        if (ummMsgSendLogDO == null) {
            return false;
        }
        return ummMsgSendLogMapper.deleteById(id) > 0;
    }

    @Mapper
    interface UmmMsgSendLogDOConverter {
        UmmMsgSendLogDOConverter INSTANCE = Mappers.getMapper(UmmMsgSendLogDOConverter.class);

        UmmMsgSendLogDO convert(UmmMsgSendLog ummMsgSendLog);

        UmmMsgSendLog convert(UmmMsgSendLogDO ummMsgSendLogDO);
    }

}