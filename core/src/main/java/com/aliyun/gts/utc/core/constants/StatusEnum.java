package com.aliyun.gts.utc.core.constants;

public enum StatusEnum {
    DISABLE("无效", 0),
    ENABLE("有效", 1);

    private String name;
    private int state;

    StatusEnum(String name, int state) {
        this.name = name;
        this.state = state;
    }

    public static String getName(int state) {
        for(StatusEnum se : StatusEnum.values()){
            if (state == se.getState()){
                return se.name;
            }
        }
        return null;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getState() {
        return this.state;
    }

    public void setState(int state) {
        this.state = state;
    }
}