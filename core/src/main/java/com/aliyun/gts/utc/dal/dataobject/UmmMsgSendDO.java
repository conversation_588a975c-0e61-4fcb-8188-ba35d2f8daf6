/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dataobject;

import com.aliyun.gts.utc.core.dataobject.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("umm_msg_send")
public class UmmMsgSendDO extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField("guid")
    private String guid;

    /**
    * 用户guid
    */
    @TableField("user_guid")
    private String userGuid;

    /**
    * 用户编码，用户登录账号
    */
    @TableField("user_code")
    private String userCode;

    /**
    * 用户姓名
    */
    @TableField("user_name")
    private String userName;

    /**
    * 用户组织
    */
    @TableField("user_org")
    private String userOrg;

    /**
    * 用户岗位
    */
    @TableField("user_postion")
    private String userPostion;

    /**
    * 邮箱
    */
    @TableField("email")
    private String email;

    /**
    * 电话
    */
    @TableField("phone")
    private String phone;

    /**
    * 消息id
    */
    @TableField("msg_id")
    private String msgId;

    /**
    * 消息标题
    */
    @TableField("msg_title")
    private String msgTitle;

    /**
    * 消息内容
    */
    @TableField("msg_content")
    private String msgContent;

    /**
    * 消息渠道ID
    */
    @TableField("channel_id")
    private Long channelId;

    /**
    * 发送时间
    */
    @TableField("send_time")
    private Date sendTime;

    /**
     * 1.未发送,2.发送中，3.发送失败，4.发送失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 阅读状态
     */
    @TableField("read_status")
    private Integer readStatus;

    /**
     * 阅读时间
     */
    @TableField("read_time")
    private Date readTime;

    /**
    * 创建人guid
    */
    @TableField("create_user_guid")
    private String createUserGuid;

    /**
    * 创建人姓名
    */
    @TableField("create_user_name")
    private String createUserName;

    /**
    * 修改人guid
    */
    @TableField("update_user_guid")
    private String updateUserGuid;

    /**
    * 修改人姓名
    */
    @TableField("update_user_name")
    private String updateUserName;

    /**
    * 创建时间
    */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
    * 修改时间
    */
    @TableField("gmt_modify")
    private Date gmtModify;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    @TableField("features")
    private String features;

    /**
    * 逻辑删除
    */
    @TableField("is_deleted")
    private Long isDeleted;

    /**
    * 版本号
    */
    @TableField("version")
    private Integer version;

    /**
    * 应用id
    */
    @TableField("app_id")
    private Long appId;

    /**
    * 租户编码
    */
    @TableField("tenant_code")
    private String tenantCode;

    /**
    * 重试规则
    */
    @TableField("retry_rule")
    private String retryRule;

}