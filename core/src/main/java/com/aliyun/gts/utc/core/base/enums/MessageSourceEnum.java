package com.aliyun.gts.utc.core.base.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
public enum MessageSourceEnum {
    MAIL("MAIL"),
    SMS("SMS"),
    SXXY("SXXY");
    private String type;

    MessageSourceEnum(String type) {
        this.type = type;
    }
    public String getType() {
        return this.type;
    }

     public static List<String> getTypeList() {
         List<String> typeList = new ArrayList<>();
         //暂时只考虑邮件
//         for (MessageSourceEnum source : MessageSourceEnum.values()) {
//             typeList.add(source.getType());
//         }
         typeList.add(MAIL.getType());
         typeList.add(SMS.getType());
        return typeList;
     }



}
