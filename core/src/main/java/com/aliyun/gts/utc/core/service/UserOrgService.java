package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.facade.request.ummuserorg.PageQueryOrgMembersRequest;
import com.aliyun.gts.utc.api.facade.response.OrgTreeResponse;
import com.aliyun.gts.utc.api.facade.response.ummuserorg.PageQueryOrgMembersResponse;
import com.aliyun.gts.utc.spi.request.UserQueryRequest;
import com.aliyun.gts.utc.spi.dto.User;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/5 21:50
 */
public interface UserOrgService {

    /**
     * 获取组织树
     * @param type 类型
     * @return 组织树
     */
    List<OrgTreeResponse> listOrgTree(String type);

    /**
     * 根据组织机构分页查询人员信息接口
     * @param request 请求体
     * @return 人员信息列表
     */
    Page<PageQueryOrgMembersResponse> listOrgMembersByPage(PageQueryOrgMembersRequest request);

    /**
     * 获取下一级组织列表。如果orgGuid为null则返回根节点。
     * @param orgGuid
     * @return
     */
    List<OrgTreeResponse> listChildOrgList(String orgGuid);


    User getUser(String userCode);

    List<User> listUserByCodes(List<String> userCodeList);

    List<User> queryUser(String keyword);
}
