/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.gts.utc.api.dto.*;
import com.aliyun.gts.utc.api.error.ErrorCodeEnum;
import com.aliyun.gts.utc.api.facade.request.message.MessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.*;
import com.aliyun.gts.utc.api.facade.response.ummmessage.*;
import com.aliyun.gts.utc.application.MessageSendAppService;
import com.aliyun.gts.utc.application.UmmMessageManageAppService;
import com.aliyun.gts.utc.common.constants.MessageTemplateConstant;
import com.aliyun.gts.utc.common.enums.UmmMessageSendMethodEnum;
import com.aliyun.gts.utc.common.enums.UmmMsgStatusEnum;
import com.aliyun.gts.utc.core.domain.*;
import com.aliyun.gts.utc.core.exceptions.BizException;
import com.aliyun.gts.utc.core.repository.*;
import com.aliyun.gts.utc.core.service.MessageManageService;
import com.aliyun.gts.utc.core.utils.EnvUtils;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.core.xxljob.ScheduleRegister;
import com.aliyun.gts.utc.dal.dataobject.UmmMessageDO;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgChannelDO;
import com.aliyun.gts.utc.dal.dto.PageMessageDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageQueryDTO;
import com.aliyun.gts.utc.integration.client.MessageClientTypeEnum;
import com.aliyun.gts.utc.integration.client.MessageClientUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.sdk.service.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import javax.validation.Valid;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.aliyun.gts.utc.common.constants.MessageConstant.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessageManageAppServiceImpl.java
 * source table name: umm_message
 * initial code lines: 113
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Slf4j
@Service
public class UmmMessageManageAppServiceImpl implements UmmMessageManageAppService {
    @Autowired
    private UmmMessageRepository ummMessageRepository;
    @Autowired
    private UmmMsgChannelRepository ummMsgChannelRepository;
    @Autowired
    private UmmMsgReceiverRepository ummMsgReceiverRepository;
    @Autowired
    private UmmCategoryRepository ummCategoryRepository;
    @Autowired
    private UmmTemplateRepository ummTemplateRepository;

    @Value("${dingtalk.tenantId:4247}")
    private Long tenantId;
    @Autowired
    private MessageSendAppService messageSendAppService;

    @Autowired
    private UserContext userContext;

    @Autowired
    private ScheduleRegister register;

    @Autowired
    private MessageManageService mailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmMessageResponse createUmmMessage(@Valid CreateUmmMessageRequest createUmmMessageRequest) {
        UmmMessageCreateDTO ummMessageCreateDTO =
                createUmmMessageRequest.getUmmMessageDTO();
        //1.数据校验
        this.checkMsgModel(ummMessageCreateDTO);
        //2.数据转换   写入umm_message表 并返回消息表id
        UmmMessageDTO ummMessageDTO =
                UmmMessageDTOConverter.INSTANCE.convert(ummMessageCreateDTO);
        //2.1 初始化常规操作参数
        //2.2 解析设置参数
        this.resolveParams(ummMessageCreateDTO, ummMessageDTO);
        this.initModifyParam(ummMessageDTO);
        ummMessageDTO.setGuid(UUID.randomUUID().toString().replace("-", ""));

        if (EnvUtils.isMockUser()) {
            ummMessageDTO.setCreateUserGuid(RuntimeContextUtil.getUserGuid());
            ummMessageDTO.setCreateUserName(RuntimeContextUtil.getUserName());
            ummMessageDTO.setUpdateUserGuid(RuntimeContextUtil.getUserGuid());
            ummMessageDTO.setUpdateUserName(RuntimeContextUtil.getUserName());
        } else {
            UserInfoDTO currentUser = userContext.currentUserInfo(false);
            ummMessageDTO.setCreateUserGuid(currentUser.getBaseInfo().getUserCode());
            ummMessageDTO.setCreateUserName(currentUser.getBaseInfo().getUserName());
            ummMessageDTO.setUpdateUserGuid(currentUser.getBaseInfo().getUserCode());
            ummMessageDTO.setUpdateUserName(currentUser.getBaseInfo().getUserName());
        }

        ummMessageDTO.setGmtCreate(new Date());
        //2.2 写入umm_message表
        UmmMessage ummMessage =
                UmmMessageDTOConverter.INSTANCE.convert(ummMessageDTO);
        //2.3 如果发送方式为立即发送发送时间和实际发送时间为当前时间
        if(UmmMessageSendMethodEnum.SEND_AT_ONCE.getType().equals(ummMessage.getSendMethod())){
            ummMessage.setSendTime(new Date());
            ummMessage.setActualSendTime(new Date());
        }
        UmmMessage add = ummMessageRepository.add(ummMessage);
        ummMessageDTO.setId(add.getId());
        CreateUmmMessageResponse response = new CreateUmmMessageResponse();
        response.setUmmMessageDTO(ummMessageDTO);
        //3.写入渠道关联表转换数据转换  写入umm_message_channel表
        if (Objects.nonNull(ummMessageCreateDTO.getChannelId())) {
            UmmMsgChannel ummMsgChannel = new UmmMsgChannel();
            ummMsgChannel.setChannelId(ummMessageCreateDTO.getChannelId());
            ummMsgChannel.setMsgId(add.getId());
            ummMsgChannelRepository.add(ummMsgChannel);
        }
        //4.写入umm_msg_receiver
        this.addUmmMsgReceiver(ummMessageCreateDTO, add.getId());
            //5.调用  消息发送逻辑
        if (ummMessageCreateDTO.getIsSend()) {
            log.debug("发送消息,内容{}", JSON.toJSONString(ummMessageDTO));
            ummMessageDTO.setTenantId(tenantId);
            this.sendMsg(ummMessageDTO);
        }

        return response;
    }

    @Override
    public String messageSend(MessageSendRequest messageSendRequest) {
        //1.数据校验
        this.checkMsgModel(messageSendRequest);
        //2.数据转换   写入umm_message表 并返回消息表id
        UmmMessageDTO ummMessageDTO =
                UmmMessageDTOConverter.INSTANCE.convert(messageSendRequest);
        String tplcode = messageSendRequest.getTplCode();
        String categoryCode = messageSendRequest.getCategoryCode();
        String msgContent = messageSendRequest.getMsgContent();
        Map<String, Object> extendParam = new HashMap<>();
        String sendName = messageSendRequest.getSendName();
        String sendDept = messageSendRequest.getSendDept();
        extendParam.put(SEND_NAME,
                org.apache.commons.lang3.StringUtils.isNotBlank(sendName) ?
                        sendName : SMS_DEFAULT_USER);
        extendParam.put(SEND_DEPT,
                org.apache.commons.lang3.StringUtils.isNotBlank(sendDept) ?
                        sendDept : SMS_DEFAULT_DEPT);
        Map<String, Object> extendFields = messageSendRequest.getExtendFields();
        if (extendFields != null) {
            extendParam.putAll(extendFields);
        }
        List<CustomParamDTO> customParamList =
                messageSendRequest.getCustomParamList();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(tplcode)) {
            UmmTemplate ummTemplate = ummTemplateRepository.getByCode(tplcode);
            if (ummTemplate != null) {
                ummMessageDTO.setTplId(ummTemplate.getId());
                ummMessageDTO.setMsgContent(org.apache.commons.lang3.StringUtils.isNotBlank(msgContent) ?
                        msgContent : ummTemplate.getTplContent());
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(categoryCode)) {
            UmmCategory ummCategory =
                    ummCategoryRepository.getByCode(categoryCode);
            if (ummCategory != null) {
                ummMessageDTO.setCategoryId(ummCategory.getId());
            }
        }
        if (extendParam != null) {
            ummMessageDTO.setFeatures(JSON.toJSONString(extendParam));
        }
        if (CollectionUtils.isNotEmpty(customParamList)) {
            ummMessageDTO.setParam(JSON.toJSONString(customParamList));
        }
        ummMessageDTO.setGuid(UUID.randomUUID().toString().replace("-", ""));
        ummMessageDTO.setGmtCreate(new Date());
        ummMessageDTO.setGmtModify(new Date());
        ummMessageDTO.setSendMethod(messageSendRequest.getSendType());
        if(UmmMessageSendMethodEnum.SEND_AT_ONCE.getType().equals(ummMessageDTO.getSendMethod())){
            ummMessageDTO.setSendTime(new Date());
        }
        //2.2 写入umm_message表
        UmmMessage ummMessage =
                UmmMessageDTOConverter.INSTANCE.convert(ummMessageDTO);
        UmmMessage add = ummMessageRepository.add(ummMessage);
        ummMessageDTO.setId(add.getId());
        CreateUmmMessageResponse response = new CreateUmmMessageResponse();
        response.setUmmMessageDTO(ummMessageDTO);
        //3.写入渠道关联表转换数据转换  写入umm_message_channel表
        String channelCode = messageSendRequest.getChannelCode();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(channelCode)) {
            String[] codes = channelCode.split(",");
            List<UmmMsgChannelDO> ummMsgChannels = new ArrayList<>();
            for (String code : codes) {
                MessageClientTypeEnum clientType =
                        MessageClientUtils.getByCode(code);
                UmmMsgChannelDO ummMsgChannel = new UmmMsgChannelDO();
                ummMsgChannel.setChannelId(clientType.getClientID());
                ummMsgChannel.setMsgId(add.getId());
                ummMsgChannel.setTenantCode(RuntimeContextUtil.getTenantCode());
                ummMsgChannels.add(ummMsgChannel);
            }
            ummMsgChannelRepository.batchAdd(ummMsgChannels);
        }
        //4.写入umm_msg_reveiver
        this.addUmmMsgReceiver(messageSendRequest, add.getId());
        //5.调用  消息发送逻辑
        if (messageSendRequest.getIsSend()) {
            log.debug("发送消息,内容{}", JSON.toJSONString(ummMessageDTO));
            this.sendMsg(ummMessageDTO);
        }
        return add.getGuid();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean msgSend(Long msgId) {
        UmmMessageUpdateDTO ummMessageUpdateDTO = new UmmMessageUpdateDTO();
        ummMessageUpdateDTO.setId(msgId);
        this.checkMsgExist(ummMessageUpdateDTO.getId());
        UmmMessage ummMessage = new UmmMessage();
        ummMessage.setId(msgId);
        ummMessage.setStatus(UmmMsgStatusEnum.SEND_PENDING.getStatus());
        ummMessageRepository.update(ummMessage);
        UmmMessage ummMessagebyId = ummMessageRepository.getById(msgId);

        Long tpl = null;
        if (ummMessagebyId != null & ummMessagebyId.getTplId() != null) {
            tpl = ummMessagebyId.getTplId();
        }
        //如果定时消息直接发送则调用下面方法
        if(UmmMessageSendMethodEnum.SEND_AT_TIME.getType().equals(ummMessagebyId.getSendMethod())){
            return  mailService.sendMsgByUmmMessageId(msgId);
        }else {
            this.msgSendImpl(msgId, null, tpl);
        }
        UmmMessage afterSendMsg = ummMessageRepository.getById(msgId);
        if (UmmMsgStatusEnum.SEND_ALL_SUCESS.getStatus().equals(afterSendMsg.getStatus())) {
            return true;
        }

        if (UmmMsgStatusEnum.SEND_PART_SUCCESS.getStatus().equals(afterSendMsg.getStatus())) {
            throw new BizException("部分发送失败，请确认失败记录目标邮箱是否正确");
        }

        throw new BizException("发送失败，请联系管理员确认失败原因");
    }


    private void msgSendImpl(Long msgId, Long tenantId, Long tplId) {
        this.messageSendAppService.sendMessage(msgId, tenantId, tplId,true);
    }

    private void addSendAwaitMessage(Long msgId, Long tenantId, Long tplId){
        this.messageSendAppService.sendMessage(msgId, tenantId, tplId,false);
    }



    @Override
    public QueryUmmMessageResponse queryUmmMessage(Long id) {

        UmmMessage ummMessage = ummMessageRepository.getById(id);
        UmmMessageDTO ummMessageDTO =
                UmmMessageDTOConverter.INSTANCE.convert(ummMessage);
        QueryUmmMessageResponse response = new QueryUmmMessageResponse();
        response.setUmmMessageDTO(ummMessageDTO);
        return response;
    }

    @Override
    public PageQueryUmmMessageResponse pageQueryUmmMessage(PageQueryUmmMessageRequest request) {

        UmmMessageDTO ummMessageDTO = request.getUmmMessageDTO();
        UmmMessage ummMessage =
                UmmMessageDTOConverter.INSTANCE.convert(ummMessageDTO);
        IPage<UmmMessage> byRange =
                ummMessageRepository.getByRange(request.getPageNum(),
                        request.getPageSize(), ummMessage);
        PageQueryUmmMessageResponse response =
                new PageQueryUmmMessageResponse();
        if (Objects.nonNull(byRange)) {
            response.setTotalCount(byRange.getTotal());
            response.setPageNum(byRange.getCurrent());
            response.setPageSize(byRange.getSize());
            response.setList(byRange.getRecords().stream()
                    .map(UmmMessageDTOConverter.INSTANCE::convert)
                    .collect(Collectors.toList()));
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmMessageResponse deleteUmmMessage(Long id) {
        DeleteUmmMessageResponse deleteUmmMessageResponse =
                new DeleteUmmMessageResponse();
        deleteUmmMessageResponse.setIsDelete(ummMessageRepository.deleteById(id));
        return deleteUmmMessageResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DeleteUmmMessageResponse deleteUmmMessageOnBatch(DelUmmMessageRequest delUmmMessageRequest) {
        // 校验ids 非当前租户下的数据不可操作
        List<Long> idList = delUmmMessageRequest.getIds();
        if (CollectionUtils.isEmpty(idList)) {
            return new DeleteUmmMessageResponse(false);
        }
        idList.parallelStream().forEach(this::checkMsgExist);

        //校验ids 草稿和已撤销状态可删除
        List<UmmMessage> byIds =
                ummMessageRepository.getByIds(delUmmMessageRequest.getIds());
        if (CollectionUtils.isEmpty(byIds)) {
            return new DeleteUmmMessageResponse(false);
        }
        boolean present = byIds.stream().anyMatch(
                ummMessage -> !UmmMsgStatusEnum.DRAFT.getStatus().equals(ummMessage.getStatus())
                        && !UmmMsgStatusEnum.CANCELLED.getStatus().equals(ummMessage.getStatus())
        );
        if (CollectionUtils.isNotEmpty(byIds) && present) {
            throw new RuntimeException("非草稿和已撤销状态消息不能删除");
        }
        //批量删除
        boolean result =
                ummMessageRepository.deleteByIdOnBatch(delUmmMessageRequest.getIds());

        return new DeleteUmmMessageResponse(result);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UpdateUmmMessageResponse updateUmmMessage(@Valid UpdateUmmMessageRequest request) {

        UmmMessageUpdateDTO ummMessageUpdateDTO =
                request.getUmmMessageUpdateDTO();
        //数据校验
        this.checkMsgModel(ummMessageUpdateDTO);
        this.checkMsgExist(ummMessageUpdateDTO.getId());
        UmmMessageDTO ummMessageDTO =
                UmmMessageDTOConverter.INSTANCE.convert(ummMessageUpdateDTO);
        ummMessageDTO.setId(ummMessageUpdateDTO.getId());
        //解析参数
        this.resolveParams(ummMessageUpdateDTO, ummMessageDTO);
        this.initModifyParam(ummMessageDTO);
        ummMessageDTO.setGmtModify(new Date());
        if (EnvUtils.isMockUser()) {
            ummMessageDTO.setUpdateUserGuid(RuntimeContextUtil.getUserGuid());
            ummMessageDTO.setUpdateUserName(RuntimeContextUtil.getUserName());
        } else {
            UserInfoDTO currentUser = userContext.currentUserInfo(false);
            ummMessageDTO.setUpdateUserGuid(currentUser.getBaseInfo().getUserCode());
            ummMessageDTO.setUpdateUserName(currentUser.getBaseInfo().getUserName());
        }
        UmmMessage ummMessage =
                UmmMessageDTOConverter.INSTANCE.convert(ummMessageDTO);
        UpdateUmmMessageResponse updateUmmMessageResponse =
                new UpdateUmmMessageResponse();
        //更新消息表
        boolean update = ummMessageRepository.update(ummMessage);
        updateUmmMessageResponse.setIsUpdate(update);
        //更新消息渠道关联表 存在更新，否则写入
        if (Objects.nonNull(ummMessageUpdateDTO.getChannelId())) {
            UmmMsgChannel ummMsgChannel = new UmmMsgChannel();
            ummMsgChannel.setMsgId(ummMessageUpdateDTO.getId());
            IPage<UmmMsgChannel> byRange =
                    ummMsgChannelRepository.getByRange(1L, 1L, ummMsgChannel);
            if (CollectionUtils.isNotEmpty(byRange.getRecords())) {
                UmmMsgChannel oldData =
                        byRange.getRecords().stream().findFirst().get();
                ummMsgChannel.setChannelId(ummMessageUpdateDTO.getChannelId());
                ummMsgChannel.setId(oldData.getId());
                ummMsgChannelRepository.update(ummMsgChannel);
            } else {
                ummMsgChannel.setChannelId(ummMessageUpdateDTO.getChannelId());
                ummMsgChannelRepository.add(ummMsgChannel);
            }
        }
        //添加或更新消息接收对象表 发送状态或有数据写入
        this.addUmmMsgReceiver(ummMessageUpdateDTO, ummMessage.getId());
        //发送消息
        if (ummMessageUpdateDTO.getIsSend()) {
            log.debug("发送消息,内容{}", JSON.toJSONString(ummMessageDTO));
            this.sendMsg(ummMessageDTO);
        }

        return updateUmmMessageResponse;
    }

    private void addUmmMsgReceiver(UmmMessageCreateDTO ummMessageCreateDTO,
                                   Long msgId) {
        if (!StringUtils.hasLength(ummMessageCreateDTO.getReceiverType())) {
            return;
        }
        UmmMsgReceiver byMsgId = ummMsgReceiverRepository.getByMsgId(msgId);
        UmmMsgReceiver receiver = new UmmMsgReceiver();
        receiver.setMsgId(msgId);
        receiver.setReceiverType(ummMessageCreateDTO.getReceiverType());
        if (CollectionUtils.isNotEmpty(ummMessageCreateDTO.getReceiverGuidList())) {
            receiver.setReceiverGuid(String.join(",",
                    ummMessageCreateDTO.getReceiverGuidList()));
        } else if (CollectionUtils.isNotEmpty(ummMessageCreateDTO.getReceiverList())) {
            List<String> receiverGuidList = ummMessageCreateDTO.getReceiverList()
                    .stream().map(ReceiverDTO::getId).collect(Collectors.toList());
            receiver.setReceiverGuid(String.join(",",
                    receiverGuidList));
        } else {
            receiver.setReceiverGuid("");
        }
        if (Objects.isNull(byMsgId)) {
            ummMsgReceiverRepository.add(receiver);
        } else {
            receiver.setId(byMsgId.getId());
            ummMsgReceiverRepository.update(receiver);
        }
    }

    private void addUmmMsgReceiver(MessageSendRequest messageSendRequest,
                                   Long msgId) {
        if (!StringUtils.hasLength(messageSendRequest.getReceiverType())) {
            return;
        }
        UmmMsgReceiver byMsgId = ummMsgReceiverRepository.getByMsgId(msgId);
        UmmMsgReceiver receiver = new UmmMsgReceiver();
        receiver.setMsgId(msgId);
        receiver.setReceiverType(messageSendRequest.getReceiverType());
        if (CollectionUtils.isNotEmpty(messageSendRequest.getReceiverGuidList())) {
            receiver.setReceiverGuid(String.join(",",
                    messageSendRequest.getReceiverGuidList()));
        } else {
            receiver.setReceiverGuid("");
        }
        if (Objects.isNull(byMsgId)) {
            ummMsgReceiverRepository.add(receiver);
        } else {
            receiver.setId(byMsgId.getId());
            ummMsgReceiverRepository.update(receiver);
        }
    }

    @Override
    public Page<PageMessageDTO> pageQueryUmmMessage(PageQueryUmmMessageListRequest pageQueryUmmMessageRequest) {
        List<Long> submitDate = pageQueryUmmMessageRequest.getSubmitDate();
        if (CollectionUtils.isNotEmpty(pageQueryUmmMessageRequest.getSubmitDate())) {
            if (submitDate.size() < 2) {
                throw new RuntimeException("submitDate至少为2个且不能为空");
            }
            if (submitDate.size() > 0 && submitDate.get(0) != null) {
                pageQueryUmmMessageRequest.setSubmitDateStart(new Date(submitDate.get(0)));
            }
            if (submitDate.size() > 1 && submitDate.get(1) != null) {
                pageQueryUmmMessageRequest.setSubmitDateEnd(new Date(submitDate.get(1)));
            }
        }
        List<Long> list = new ArrayList<>();
        if (pageQueryUmmMessageRequest.getCategoryId() != null) {
            list.add(pageQueryUmmMessageRequest.getCategoryId());
        }
        if (CollectionUtils.isNotEmpty(pageQueryUmmMessageRequest.getCategoryIdList())) {
            list.addAll(pageQueryUmmMessageRequest.getCategoryIdList());
        }
        List<Long> cateGoryAndChildIds = this.getCategoryAndChildIds(list);

        Page<UmmMessageDO> pageMessageDTOPage =
                new Page<>(pageQueryUmmMessageRequest.getPageNum(),
                        pageQueryUmmMessageRequest.getPageSize());
        PageMessageQueryDTO pageMessageQueryDTO =
                UmmMessageDTOConverter.INSTANCE.convert(pageQueryUmmMessageRequest);
        pageMessageQueryDTO.setUpdateUserName(pageQueryUmmMessageRequest.getCreateUserName());
        pageMessageQueryDTO.setCategoryIdList(cateGoryAndChildIds);
        Page<PageMessageDTO> resultPage =
                ummMessageRepository.pageQuery(pageMessageDTOPage,
                        pageMessageQueryDTO);
        //填充关联数据
        List<PageMessageDTO> records = resultPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return resultPage;
        }
        List<Long> categoryIds =
                records.stream().map(PageMessageDTO::getCategoryId).collect(Collectors.toList());
        List<Long> msgIds =
                records.stream().map(PageMessageDTO::getId).collect(Collectors.toList());

        List<UmmMsgChannel> msgChannelList = ummMsgChannelRepository.getByMsgId(msgIds);
        //1.查询渠道
        Map<Long, Long> msgChannelMap = msgChannelList.stream().collect(Collectors.toMap(UmmMsgChannel::getMsgId, UmmMsgChannel::getChannelId, (o1, o2) -> {
            log.error("duplicate key found!");
            return o1;
        }));
        Map<Long, String> channelMap = MessageClientTypeEnum.getClientIdToClientNameMap();
        //2.查询栏目
        Map<Long, String> categoryNameMap =
                ummCategoryRepository.getByIds(categoryIds).stream().collect(Collectors.toMap(UmmCategory::getId, UmmCategory::getCategoryName));
        //3.遍历赋值
        records.stream().forEach(pageMessageDTO -> {
            pageMessageDTO.setCategoryName(categoryNameMap.get(pageMessageDTO.getCategoryId()));
            pageMessageDTO.setChannelId(msgChannelMap.get(pageMessageDTO.getId()));
            pageMessageDTO.setChannelName(channelMap.get(pageMessageDTO.getChannelId()));
        });
        return resultPage;
    }


    @Mapper
    interface UmmMessageDTOConverter {
        UmmMessageDTOConverter INSTANCE =
                Mappers.getMapper(UmmMessageDTOConverter.class);

        UmmMessage convert(UmmMessageDTO ummMessageDTO);

        UmmMessageDTO convert(UmmMessage ummMessage);

        UmmMessageDTO convert(MessageSendRequest messageSendRequest);

        PageMessageQueryDTO convert(PageQueryUmmMessageListRequest pageQueryUmmMessageListRequest);

        UmmMessageDTO convert(UmmMessageCreateDTO ummMessageCreatDTO);
    }

    private void resolveParams(UmmMessageCreateDTO ummMessageCreatDTO,
                               UmmMessageDTO ummMessageDTO) {
        //1 系统参数 以json形式存入param
        if (Objects.nonNull(ummMessageCreatDTO.getParamDTO())) {
            List<String> paramKeys = new ArrayList<>();
            List<CustomParamDTO> customParamList =
                    ummMessageCreatDTO.getParamDTO().getCustomParamList();

            if (Objects.nonNull(customParamList)) {
                boolean present =
                        customParamList.stream().anyMatch(customParamDTO -> {
                            if (StringUtils.hasLength(customParamDTO.getValue())) {
                                return customParamDTO.getValue().length() > 30;
                            }
                            return false;
                        });
                if (present) {
                    throw new RuntimeException("自定义参数长度值限制1-30");
                }
                List<String> collect =
                        customParamList.stream().map(CustomParamDTO::getName).collect(Collectors.toList());
                paramKeys.addAll(collect);
                ummMessageDTO.setParam(JSON.toJSONString(customParamList));

            } else {
                ummMessageDTO.setParam(JSON.toJSONString(Collections.emptyList()));
            }
            List<SystemParamDTO> systemParamList =
                    ummMessageCreatDTO.getParamDTO().getSystemParamList();
            if (Objects.nonNull(systemParamList)) {
                List<String> collect =
                        systemParamList.stream().map(SystemParamDTO::getExpression).collect(Collectors.toList());
                paramKeys.addAll(collect);
            }
            String userName = RuntimeContextUtil.getUserName();
//            String orgGuid = "";
//            List<OrganizationInfoDTO> belongOrgList = userCenterClient.listOrgTree();
//            if (CollectionUtils.isNotEmpty(belongOrgList)) {
//                orgGuid = belongOrgList.get(0).getGuid();
//            }
            Map<String, Object> extendParam = new HashMap<>();
            extendParam.put(SEND_NAME, SMS_DEFAULT_USER);
            extendParam.put(SEND_DEPT, SMS_DEFAULT_DEPT);
            extendParam.put(MessageTemplateConstant.RECEIVER_LIST,
                    ummMessageCreatDTO.getReceiverList());
            extendParam.put(MessageTemplateConstant.SYSTEM_PARAM_LIST,
                    systemParamList);
            ummMessageDTO.setFeatures(JSON.toJSONString(extendParam));

            //消息参数合法性校验
            if (ummMessageCreatDTO.getIsSend()) {
                this.checkMsgParamValid(ummMessageCreatDTO.getMsgTitle(),
                        paramKeys);
                this.checkMsgParamValid(ummMessageCreatDTO.getMsgContent(),
                        paramKeys);
            }
        } else {
            ummMessageDTO.setParam(JSON.toJSONString(Arrays.asList()));
            ummMessageDTO.setFeatures(JSON.toJSONString(Arrays.asList()));
        }

        ummMessageDTO.setSendMethod(ummMessageCreatDTO.getSendType());
        //2 消息状态参数设置 待发送/发送中/草稿
        if (ummMessageCreatDTO.getIsSend()) {
            if (UmmMessageSendMethodEnum.SEND_AT_ONCE.getType().equals(ummMessageCreatDTO.getSendType())) {
                ummMessageDTO.setStatus(UmmMsgStatusEnum.SEND_PENDING.getStatus());
            } else if (UmmMessageSendMethodEnum.SEND_AT_TIME.getType().equals(ummMessageCreatDTO.getSendType())) {
                ummMessageDTO.setStatus(UmmMsgStatusEnum.SEND_WAITTING.getStatus());
            }
        } else {
            ummMessageDTO.setStatus(UmmMsgStatusEnum.DRAFT.getStatus());
        }
    }

    private void initModifyParam(UmmMessageDTO ummMessageDTO) {
        ummMessageDTO.setIsDeleted(0L);
        ummMessageDTO.setGmtModify(new Date());
        ummMessageDTO.setUpdateUserName(RuntimeContextUtil.getUserName());
        ummMessageDTO.setUpdateUserGuid(RuntimeContextUtil.getUserGuid());
    }


    private void checkMsgExist(Long msgId) {
        UmmMessage ummMessage =
                ummMessageRepository.getById(msgId);
        if (Objects.isNull(ummMessage) || !Objects.equals(ummMessage.getTenantCode(), RuntimeContextUtil.getTenantCode())) {
            throw new BizException(ErrorCodeEnum.NO_PERMISSION.getDetailCode(), "当前租户下不存在消息，不支持操作");
        }
    }

    /**
     * 栏目可用性校验&渠道可用性校验&模板可用性校验&系统参数可用性校验
     *
     * @param ummMessageCreatDTO
     */
    private void checkMsgModel(UmmMessageCreateDTO ummMessageCreatDTO) {
        if (Objects.isNull(ummMessageCreatDTO)) {
            throw new RuntimeException("推送消息数据不存在");
        }
        if (!ummMessageCreatDTO.getIsSend()) {
            return;
        }
        //栏目可用性
        checkCategoryExistById(ummMessageCreatDTO.getCategoryId());
        checkTemplateExistById(ummMessageCreatDTO.getTplId());

        //渠道数据存在/可用性 TODO:渠道源按租户校验可用性
        if (Objects.isNull(ummMessageCreatDTO.getChannelId())) {
            throw new RuntimeException(ErrorCodeEnum.CHANNEL_MISS.getDescription());
        }
        //接收对象校验
        if (StringUtils.isEmpty(ummMessageCreatDTO.getReceiverType())) {
            throw new RuntimeException(ErrorCodeEnum.RECEIVER_MISS.getDescription());
        }
        if (!"ALL".equalsIgnoreCase(ummMessageCreatDTO.getReceiverType())
                && CollectionUtils.isEmpty(ummMessageCreatDTO.getReceiverGuidList())
                    && CollectionUtils.isEmpty(ummMessageCreatDTO.getReceiverList())
            ) {
            throw new RuntimeException(ErrorCodeEnum.RECEIVER_MISS.getDescription());
        }
        //发送时间校验
        if (UmmMessageSendMethodEnum.SEND_AT_TIME.getType().equals(ummMessageCreatDTO.getSendType()) && Objects.isNull(ummMessageCreatDTO.getSendTime())) {
            throw new RuntimeException(ErrorCodeEnum.SEND_TIME_MISS.getDescription());
        }
        //定时发送情况下，发送时间过期
        if (UmmMessageSendMethodEnum.SEND_AT_TIME.getType().equals(ummMessageCreatDTO.getSendType()) && Objects.nonNull(ummMessageCreatDTO.getSendTime()) && new Date().after(ummMessageCreatDTO.getSendTime())) {
            throw new RuntimeException(ErrorCodeEnum.SEND_TIME_OUTOFDATA.getDescription());
        }

    }

    private void checkMsgModel(MessageSendRequest messageSendRequest) {
        if (Objects.isNull(messageSendRequest)) {
            throw new RuntimeException("推送消息数据不存在");
        }
        if (!messageSendRequest.getIsSend()) {
            return;
        }
        //渠道数据存在/可用性
        if (StringUtils.isEmpty(messageSendRequest.getChannelCode())) {
            throw new RuntimeException(ErrorCodeEnum.CHANNEL_MISS.getDescription());
        }
        //接收对象校验
        if (StringUtils.isEmpty(messageSendRequest.getReceiverType())) {
            throw new RuntimeException(ErrorCodeEnum.RECEIVER_MISS.getDescription());
        }
        if (CollectionUtils.isEmpty(messageSendRequest.getReceiverGuidList())) {
            throw new RuntimeException(ErrorCodeEnum.RECEIVER_MISS.getDescription());
        }
        //发送时间校验
        if (UmmMessageSendMethodEnum.SEND_AT_TIME.getType().equals(messageSendRequest.getSendType()) && Objects.isNull(messageSendRequest.getSendTime())) {
            throw new RuntimeException(ErrorCodeEnum.SEND_TIME_MISS.getDescription());
        }
        //定时发送情况下，发送时间过期
        if (UmmMessageSendMethodEnum.SEND_AT_TIME.getType().equals(messageSendRequest.getSendType()) && Objects.nonNull(messageSendRequest.getSendTime()) && new Date().after(messageSendRequest.getSendTime())) {
            throw new RuntimeException(ErrorCodeEnum.SEND_TIME_OUTOFDATA.getDescription());
        }
        //模版权限租户校验
        checkTemplateExistByCode(messageSendRequest.getTplCode());
        checkCategoryExistByCode(messageSendRequest.getCategoryCode());
    }


    private void sendMsg(UmmMessageDTO ummMessageDTO) {
        if (EnvUtils.isMockSend()) {
            log.info("send message, send method {} is mocked", ummMessageDTO.getSendMethod());
            return;
        }

        //定时消息放xxl-job
        if (UmmMessageSendMethodEnum.SEND_AT_TIME.getType().equals(ummMessageDTO.getSendMethod())) {
            this.addSendAwaitMessage(ummMessageDTO.getId(),
                    ummMessageDTO.getTenantId(),ummMessageDTO.getTplId());
            register.addXxlJob(ummMessageDTO.getId(),
                    ummMessageDTO.getSendTime());
        }
        //立即发送 放mq
        if (UmmMessageSendMethodEnum.SEND_AT_ONCE.getType().equals(ummMessageDTO.getSendMethod())) {
            this.msgSendImpl(ummMessageDTO.getId(),
                    ummMessageDTO.getTenantId(),ummMessageDTO.getTplId());
        }
    }

    private List<Long> getCategoryAndChildIds(List<Long> ids) {
        List<Long> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids)) {
            return list;
        }
        list.addAll(ids);
        List<UmmCategory> byIds = ummCategoryRepository.getByParentIds(ids);
        List<Long> childIds =
                byIds.stream().map(UmmCategory::getId).collect(Collectors.toList());
        List<Long> cateGoryAndChildIds = this.getCategoryAndChildIds(childIds);
        list.addAll(cateGoryAndChildIds);
        return list.stream().distinct().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private void checkMsgParamValid(String content, List<String> paramKey) {
        String pattern = "(?<=\\$\\{).*?(?=})";
        Matcher matcher = Pattern.compile(pattern).matcher(content);
        List<String> names = new ArrayList<>();
        while (matcher.find()) {
            names.add(matcher.group());
        }
        boolean b = paramKey.containsAll(names);
        if (!b) {
            throw new RuntimeException("非法的消息参数");
        }

    }

    private void checkTemplateExistByCode(String tplCode) {
        UmmTemplate ummTemplate = ummTemplateRepository.getByCode(tplCode);
        if (ummTemplate == null || !Objects.equals(ummTemplate.getTenantCode(), RuntimeContextUtil.getTenantCode())) {
            throw new BizException(ErrorCodeEnum.NO_PERMISSION.getDetailCode(), "当前租户下不存在模版，不支持操作");
        }
    }

    private void checkCategoryExistByCode(String categoryCode) {
        UmmCategory ummCategory = ummCategoryRepository.getByCode(categoryCode);
        if (ummCategory == null || !Objects.equals(ummCategory.getTenantCode(), RuntimeContextUtil.getTenantCode())) {
            throw new BizException(ErrorCodeEnum.NO_PERMISSION.getDetailCode(), "当前租户下不存在此栏目，不支持操作");
        }
    }


    private void checkTemplateExistById(Long tplId) {
        UmmTemplate ummTemplate = ummTemplateRepository.getById(tplId);
        if (ummTemplate == null || !Objects.equals(ummTemplate.getTenantCode(), RuntimeContextUtil.getTenantCode())) {
            throw new BizException(ErrorCodeEnum.NO_PERMISSION.getDetailCode(), "当前租户下不存在模版，不支持操作");
        }
    }

    private void checkCategoryExistById(Long categoryId) {
        UmmCategory ummCategory = ummCategoryRepository.getById(categoryId);
        if (ummCategory == null || !Objects.equals(ummCategory.getTenantCode(), RuntimeContextUtil.getTenantCode())) {
            throw new BizException(ErrorCodeEnum.NO_PERMISSION.getDetailCode(), "当前租户下不存在此栏目，不支持操作");
        }
    }
}
