package com.aliyun.gts.utc.dal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.Date;

@Data
public class PageMessageDTO {
    /**
     * 主键id
     */
    @ApiParam("主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 模板id
     */
    @ApiParam("模板id")
    private Long tplId;

    /**
     *
     */
    @ApiParam("")
    private String guid;

    /**
     * 消息标题
     */
    @ApiParam("消息标题")
    private String msgTitle;

    /**
     * 消息内容
     */
    @ApiParam("消息内容")
    private String msgContent;

    /**
     * 发送参数,json存储
     */
    @ApiParam("发送参数,json存储")
    private String param;

    /**
     * 1.实时发送|2.定时发送
     */
    @ApiParam("1.实时发送|2.定时发送")
    private Integer sendMethod;

    /**
     * 分发时间，定时发送时的时间
     */
    @ApiParam("分发时间，定时发送时的时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date sendTime;

    /**
     * 实际发送时间
     */
    @ApiParam("实际发送时间")
    private Date actualSendTime;

    /**
     * 栏目ID
     */
    @ApiParam("栏目ID")
    private Long categoryId;

    @ApiParam("栏目名称")
    private String categoryName;
    /**
     * 状态  1待审批 2待发送 3发送成功 4审批未通过 5.发送失败  6已撤销
     */
    @ApiParam("状态  1待审批 2待发送 3发送成功 4审批未通过 5.发送失败  6已撤销")
    private Integer status;

    /**
     * 创建人guid
     */
    @ApiParam("创建人guid")
    private String createUserGuid;

    /**
     * 创建人姓名
     */
    @ApiParam("创建人姓名")
    private String createUserName;

    /**
     * 修改人guid
     */
    @ApiParam("修改人guid")
    private String updateUserGuid;

    /**
     * 修改人姓名
     */
    @ApiParam("修改人姓名")
    private String updateUserName;

    /**
     * 创建时间
     */
    @ApiParam("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @ApiParam("修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date gmtModify;

    /**
     * 所有外部系统冗余字段，以json形式存储
     */
    @ApiParam("所有外部系统冗余字段，以json形式存储")
    private String features;

    /**
     * 逻辑删除
     */
    @ApiParam("逻辑删除")
    private Long isDeleted;

    /**
     * 版本号
     */
    @ApiParam("版本号")
    private Integer version;

    /**
     * 应用id
     */
    @ApiParam("应用id")
    private Long appId;

    /**
     * 租户编码
     */
    @ApiParam("租户编码")
    private String tenantCode;

    @ApiParam("渠道id")
    private Long channelId;

    @ApiParam("渠道名称")
    private String channelName;

}
