package com.aliyun.gts.utc.core.factory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.gts.utc.api.dto.*;
import com.aliyun.gts.utc.api.facade.request.template.CreateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.PageQueryMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.UpdateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.response.template.PageQueryMessageTemplateResponse;
import com.aliyun.gts.utc.api.facade.response.template.QueryMessageTemplateResponse;
import com.aliyun.gts.utc.common.constants.MessageTemplateConstant;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.exceptions.BizException;
import com.aliyun.gts.utc.dal.dto.MessageTemplateQueryDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageTemplateDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 消息模板工厂类
 * <AUTHOR>
 * @date 2021/6/2 15:34
 */
@Slf4j
public class MessageTemplateFactory {

    /**
     * 组装新建消息模板
     * @param request 新建消息请求体
     * @return 消息模板
     */
    public static UmmTemplateDTO createTemplate(CreateMessageTemplateRequest request){
        UmmTemplateDTO ummTemplateDTO = new UmmTemplateDTO();
        ummTemplateDTO.setTplTitle(request.getMsgTitle());
        ummTemplateDTO.setTplContent(request.getMsgContent());
        ummTemplateDTO.setTplCode(request.getTplCode());
        ummTemplateDTO.setCategoryId(request.getCategoryId());
        ummTemplateDTO.setTplCode(request.getTplCode());
        if (request.getSource() != null) {
            ummTemplateDTO.setSourceId(request.getSource().getValue());
        }
        ParamDTO param = request.getParamDTO();
        MessageTemplateFeatureDTO featureDTO = new MessageTemplateFeatureDTO();
        BeanUtils.copyProperties(request,featureDTO);
        if(null != param) {
            if (null != param.getCustomParamList() && param.getCustomParamList().size() > 0) {
                ummTemplateDTO.setParam(JSON.toJSONString(param.getCustomParamList()));
            }
            featureDTO.setSystemParamList(param.getSystemParamList());
        }
        List<String> receiverGuidList = request.getReceiverGuidList();
        if((null == receiverGuidList || receiverGuidList.isEmpty())
                && CollectionUtils.isNotEmpty(request.getReceiverList())) {
            receiverGuidList = request.getReceiverList()
                    .stream().map(ReceiverDTO::getId).collect(Collectors.toList());
            request.setReceiverGuidList(receiverGuidList);
        }
        ummTemplateDTO.setFeatures(JSON.toJSONString(featureDTO));

        ummTemplateDTO.setStatus(MessageTemplateConstant.ENABLE);
        ummTemplateDTO.setIsDeleted(MessageTemplateConstant.NOT_DELETED);

        return ummTemplateDTO;

    }


    /**
     * 组装编辑消息模板
     * @param request 编辑消息请求体
     * @return 消息模板
     */
    public static UmmTemplateDTO createUpdateTemplate(UpdateMessageTemplateRequest request){
        UmmTemplateDTO ummTemplateDTO = new UmmTemplateDTO();
        ummTemplateDTO.setId(request.getId());
        ummTemplateDTO.setTplContent(request.getMsgContent());
        ummTemplateDTO.setTplTitle(request.getMsgTitle());
        ummTemplateDTO.setCategoryId(request.getCategoryId());
        ummTemplateDTO.setTplCode(request.getTplCode());
        if (request.getSource() != null) {
            ummTemplateDTO.setSourceId(request.getSource().getValue());
        }
        List<String> receiverGuidList = request.getReceiverGuidList();
        if ((null == receiverGuidList || receiverGuidList.isEmpty())
                && CollectionUtils.isNotEmpty(request.getReceiverList())) {
            receiverGuidList = request.getReceiverList()
                    .stream().map(ReceiverDTO::getId).collect(Collectors.toList());
            request.setReceiverGuidList(receiverGuidList);
        }
        MessageTemplateFeatureDTO featureDTO = new MessageTemplateFeatureDTO();
        ParamDTO param = request.getParamDTO();
        BeanUtils.copyProperties(request,featureDTO);
        if(null == param){
            throw new BizException("参数格式有误");
        }
        if (null != param.getCustomParamList() && !param.getCustomParamList().isEmpty()) {
            ummTemplateDTO.setParam(JSON.toJSONString(param.getCustomParamList()));
        }else{
            ummTemplateDTO.setParam(JSON.toJSONString(new ArrayList<>()));
        }
        if(null == param.getSystemParamList()){
            featureDTO.setSystemParamList(new ArrayList<>());
        }else {
            featureDTO.setSystemParamList(param.getSystemParamList());
        }
        ummTemplateDTO.setIsDeleted(MessageTemplateConstant.NOT_DELETED);
        ummTemplateDTO.setStatus(MessageTemplateConstant.ENABLE);
        ummTemplateDTO.setFeatures(JSON.toJSONString(featureDTO));

        return ummTemplateDTO;

    }

    /**
     * 组装消息模板详情返回体
     * @param ummTemplate 消息模板
     * @return 消息模板详情返回体
     */
    public static QueryMessageTemplateResponse createTemplateDetailResponse(UmmTemplate ummTemplate){
        QueryMessageTemplateResponse response = new QueryMessageTemplateResponse();
        response.setMsgTitle(ummTemplate.getTplTitle());
        response.setMsgContent(ummTemplate.getTplContent());
        response.setSubmitName(ummTemplate.getUpdateUserName());
        response.setSubmitTime(ummTemplate.getGmtCreate());
        response.setTplCode(ummTemplate.getTplCode());
        ObjectMapper objectMapper = new ObjectMapper();
        ParamDTO paramDTO = new ParamDTO();
        if(StringUtils.isNotEmpty(ummTemplate.getParam())){
            try{
                JSONArray jsonArray = JSON.parseArray(ummTemplate.getParam());
                List<CustomParamDTO> customParamDTOList = new ArrayList<>();
                for(Object object: jsonArray){
                    CustomParamDTO customParamDTO = objectMapper.convertValue(object,CustomParamDTO.class);
                    customParamDTOList.add(customParamDTO);
                }
                paramDTO.setCustomParamList(customParamDTOList);
            }catch (Exception e){
                log.info("{}消息模板存在脏数据，自定义参数无法转换",ummTemplate.getId());
            }
        }
        if(StringUtils.isNotEmpty(ummTemplate.getFeatures())){
            try{
                JSONObject jsonObject = JSON.parseObject(ummTemplate.getFeatures());
                if(null != jsonObject.get(MessageTemplateConstant.SYSTEM_PARAM_LIST)){
                    JSONArray jsonArray = jsonObject.getJSONArray(MessageTemplateConstant.SYSTEM_PARAM_LIST);
                    List<SystemParamDTO> systemParamDTOList = new ArrayList<>();
                    for(Object object: jsonArray){
                        SystemParamDTO systemParamDTO = objectMapper.convertValue(object,SystemParamDTO.class);
                        systemParamDTOList.add(systemParamDTO);
                    }
                    paramDTO.setSystemParamList(systemParamDTOList);
                }
                if(null != jsonObject.get(MessageTemplateConstant.RECEIVER_GUID_LIST)){
                    JSONArray jsonArray = jsonObject.getJSONArray(MessageTemplateConstant.RECEIVER_GUID_LIST);
                    List<String> receiverGuidList = new ArrayList<>();
                    for(Object object: jsonArray){
                        receiverGuidList.add(String.valueOf(object));
                    }
                    response.setReceiverGuidList(receiverGuidList);
                }
                if(null != jsonObject.get(MessageTemplateConstant.RECEIVER_LIST)){
                    JSONArray jsonArray = jsonObject.getJSONArray(MessageTemplateConstant.RECEIVER_LIST);
                    List<ReceiverDTO> receiverList = new ArrayList<>();
                    for(Object object: jsonArray){
                        ReceiverDTO receiverDTO = objectMapper.convertValue(object,ReceiverDTO.class);
                        receiverList.add(receiverDTO);
                    }
                    response.setReceiverList(receiverList);
                }
                if(null != jsonObject.get(MessageTemplateConstant.RECEIVER_TYPE)){
                    response.setReceiverType(jsonObject.getString(MessageTemplateConstant.RECEIVER_TYPE));
                }
                if(null != jsonObject.get(MessageTemplateConstant.CHANNEL_ID)) {
                    response.setChannelId(jsonObject.getLong(MessageTemplateConstant.CHANNEL_ID));
                }
                if(null != jsonObject.get(MessageTemplateConstant.CHANNEL_NAME)) {
                    response.setChannelName(jsonObject.getString(MessageTemplateConstant.CHANNEL_NAME));
                }
                if(null != jsonObject.get(MessageTemplateConstant.SEND_TYPE)) {
                    response.setSendType(jsonObject.getInteger(MessageTemplateConstant.SEND_TYPE));
                    if(1 == response.getSendType()){
                        response.setSendTypeName(MessageTemplateConstant.REAL_TIME_SEND);
                    }else {
                        response.setSendTypeName(MessageTemplateConstant.TIMING_SEND);
                    }
                }
            }catch (Exception e){
                log.info("{}消息模板存在脏数据，冗余字段无法转换",ummTemplate.getId());
            }
        }
        response.setParamDTO(paramDTO);
        return response;
    }


    /**
     * 组装分页查询消息模板请求
     * @param request 分页查询消息模板请求体
     * @return 分页查询消息模板请求
     */
    public static MessageTemplateQueryDTO createQuery(PageQueryMessageTemplateRequest request){
        MessageTemplateQueryDTO queryDTO = new MessageTemplateQueryDTO();
        BeanUtils.copyProperties(request,queryDTO);
        //处理关键字查询条件
        if(StringUtils.isNotEmpty(request.getTitleQuery())){
            queryDTO.setTitleQuery("%" + request.getTitleQuery() + "%");
        }
        if(StringUtils.isNotEmpty(request.getSubmitNameQuery())){
            queryDTO.setSubmitNameQuery("%" + request.getSubmitNameQuery() + "%");
        }
        //处理排序
        if(StringUtils.isNotEmpty(request.getOrderField())) {
            switch (request.getOrderField()) {
                case MessageTemplateConstant.TITLE:
                    queryDTO.setOrderField(MessageTemplateConstant.TITLE_TABLE);
                    break;
                case MessageTemplateConstant.CATEGORY:
                    queryDTO.setOrderField(MessageTemplateConstant.CATEGORY_TABLE);
                    break;
                case MessageTemplateConstant.GMT_MODIFY:
                    queryDTO.setOrderField(MessageTemplateConstant.GMT_MODIFY_TABLE);
                    break;
                default:
                    queryDTO.setOrderField(null);
                    break;
            }
        }
        return queryDTO;
    }

    /**
     * 组装分页查询消息模板返回体
     * @param page 分页后的消息模板
     * @param channelMap 渠道设置map
     * @return 分页查询消息模板返回体
     */
    public static Page<PageQueryMessageTemplateResponse> createQueryResponse(Page<PageMessageTemplateDTO> page, Map<Long,String> channelMap){
        Page<PageQueryMessageTemplateResponse> response = new Page<>();
        response.setCurrent(page.getCurrent());
        response.setTotal(page.getTotal());
        response.setSize(page.getSize());
        List<PageMessageTemplateDTO> templateDTOList = page.getRecords();
        if(null == templateDTOList || templateDTOList.size() == 0){
            return response;
        }
        List<PageQueryMessageTemplateResponse> templateResponseList = new ArrayList<>();
        for(PageMessageTemplateDTO messageTemplate : templateDTOList){
            PageQueryMessageTemplateResponse templateResponse = new PageQueryMessageTemplateResponse();
            BeanUtils.copyProperties(messageTemplate,templateResponse);
            try{
                //回填渠道id，名称以及发送类型，从冗余字段中获取
                if(StringUtils.isNotEmpty(messageTemplate.getFeatures())){
                    JSONObject jsonObject = JSON.parseObject(messageTemplate.getFeatures());
                    if(null != jsonObject.get(MessageTemplateConstant.CHANNEL_ID)) {
                        templateResponse.setChannelId(jsonObject.getLong(MessageTemplateConstant.CHANNEL_ID));
                        templateResponse.setChannelName(channelMap.get(templateResponse.getChannelId()));
                    }
                    if(null != jsonObject.get(MessageTemplateConstant.SEND_TYPE)) {
                        templateResponse.setSendType(jsonObject.getInteger(MessageTemplateConstant.SEND_TYPE));
                        if(1 == templateResponse.getSendType()){
                            templateResponse.setSendTypeName(MessageTemplateConstant.REAL_TIME_SEND);
                        }else {
                            templateResponse.setSendTypeName(MessageTemplateConstant.TIMING_SEND);
                        }
                    }
                }
            }catch (Exception e){
                log.info("分页查询返回时，第{}消息模板存在脏数据，冗余字段无法转换",messageTemplate.getId());
            }
            templateResponseList.add(templateResponse);
        }
        response.setRecords(templateResponseList);
        return response;
    }

}
