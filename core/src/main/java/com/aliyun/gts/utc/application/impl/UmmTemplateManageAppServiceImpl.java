/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmTemplateDTO;
import com.aliyun.gts.utc.api.facade.request.ummtemplate.CreateUmmTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.ummtemplate.PageQueryUmmTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.ummtemplate.UpdateUmmTemplateRequest;
import com.aliyun.gts.utc.api.facade.response.ummtemplate.*;
import com.aliyun.gts.utc.application.UmmTemplateManageAppService;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.repository.UmmCategoryRepository;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmTemplateManageAppServiceImpl.java
 * source table name: umm_template
 * initial code lines: 113
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Service
public class UmmTemplateManageAppServiceImpl implements UmmTemplateManageAppService {
    @Autowired
    private UmmTemplateRepository ummTemplateRepository;
    @Autowired
    private UmmCategoryRepository ummCategoryRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmTemplateResponse createUmmTemplate(CreateUmmTemplateRequest createUmmTemplateRequest){

        UmmTemplateDTO ummTemplateDTO = createUmmTemplateRequest.getUmmTemplateDTO();
        ummCategoryRepository.checkCategoryExistInCurrentTenantCode(ummTemplateDTO.getCategoryId());

        UmmTemplate ummTemplate = UmmTemplateDTOConverter.INSTANCE.convert(ummTemplateDTO);
        UmmTemplate add = ummTemplateRepository.add(ummTemplate);
        ummTemplateDTO.setId(add.getId());

        CreateUmmTemplateResponse response = new CreateUmmTemplateResponse();
        response.setUmmTemplateDTO(ummTemplateDTO);

        return response;
    }

    @Override
    public QueryUmmTemplateResponse queryUmmTemplate(Long id) {

        UmmTemplate ummTemplate = ummTemplateRepository.getById(id);
        UmmTemplateDTO ummTemplateDTO = UmmTemplateDTOConverter.INSTANCE.convert(ummTemplate);
        QueryUmmTemplateResponse response = new QueryUmmTemplateResponse();
        response.setUmmTemplateDTO(ummTemplateDTO);
        return response;
    }

    @Override
    public QueryUmmTemplateResponse queryUmmTemplateByCode(String code) {
        UmmTemplate ummTemplate =ummTemplateRepository.getByCode(code);
        UmmTemplateDTO ummTemplateDTO = UmmTemplateDTOConverter.INSTANCE.convert(ummTemplate);
        QueryUmmTemplateResponse response = new QueryUmmTemplateResponse();
        response.setUmmTemplateDTO(ummTemplateDTO);
        return response;
    }

    @Override
    public PageQueryUmmTemplateResponse pageQueryUmmTemplate(PageQueryUmmTemplateRequest request) {

        UmmTemplateDTO ummTemplateDTO = request.getUmmTemplateDTO();
        UmmTemplate ummTemplate = UmmTemplateDTOConverter.INSTANCE.convert(ummTemplateDTO);
        IPage<UmmTemplate> byRange = ummTemplateRepository.getByRange(request.getPageNum().intValue(), request.getPageSize().intValue(), ummTemplate);

        PageQueryUmmTemplateResponse response = new PageQueryUmmTemplateResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
            .map(UmmTemplateDTOConverter.INSTANCE::convert)
            .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmTemplateResponse deleteUmmTemplate(Long id) {
        DeleteUmmTemplateResponse deleteUmmTemplateResponse = new DeleteUmmTemplateResponse();
        deleteUmmTemplateResponse.setIsDelete(ummTemplateRepository.deleteById(id));
        return deleteUmmTemplateResponse;
    }

    @Override
    public UpdateUmmTemplateResponse updateUmmTemplate(UpdateUmmTemplateRequest request) {

        UmmTemplateDTO ummTemplateDTO = request.getUmmTemplateDTO();

        UmmTemplate ummTemplate = UmmTemplateDTOConverter.INSTANCE.convert(ummTemplateDTO);
        UpdateUmmTemplateResponse updateUmmTemplateResponse = new UpdateUmmTemplateResponse();
        updateUmmTemplateResponse.setIsUpdate(ummTemplateRepository.update(ummTemplate));
        return updateUmmTemplateResponse;
    }

    @Override
    public PageQueryUmmTemplateResponse queryAllUmmTemplate() {
        UmmTemplate ummTemplate = new UmmTemplate();
        ummTemplate.setStatus(1);
        IPage<UmmTemplate> byRange = ummTemplateRepository.getByRange(1, 500,
                ummTemplate);
        PageQueryUmmTemplateResponse response =
                new PageQueryUmmTemplateResponse();
        response.setPageNum(null);
        response.setPageSize(null);
        response.setTotalCount(byRange.getTotal());
        response.setList(byRange.getRecords().stream()
                .map(UmmTemplateDTOConverter.INSTANCE::convert)
                .collect(Collectors.toList()));
        return response;
    }

    @Mapper
    interface UmmTemplateDTOConverter {
        UmmTemplateDTOConverter INSTANCE = Mappers.getMapper(UmmTemplateDTOConverter.class);

        UmmTemplate convert(UmmTemplateDTO ummTemplateDTO);

        UmmTemplateDTO convert(UmmTemplate ummTemplate);
    }

}