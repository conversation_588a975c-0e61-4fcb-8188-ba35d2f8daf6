/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.message.PageQueryMessageSendResultRequest;
import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;


public interface UmmMsgSendRepository {

    /**
     * 通过 id 获取ummMsgSend
     *
     * @param id ummMsgSend id
     * @return ummMsgSend；null 如果账户不存在
     */
    UmmMsgSend getById(Long id);

    List<UmmMsgSend> getByIds(List<Long> ids);



    List<UmmMsgSendDTO> queryUmmMsgSendByMsgId(String msgId);

    /**
     * 获取一个区间内的ummMsgSend
     *
     * @param pageNum
     * @param pageSize 期望个数
     * @return 区间内的ummMsgSend列表
     */
    IPage<UmmMsgSend> getByRange(Long pageNum, Long pageSize,
                                 UmmMsgSend ummMsgSend);

    /**
     * 添加ummMsgSend
     *
     * @param ummMsgSend 待添加ummMsgSend
     */
    UmmMsgSend add(UmmMsgSend ummMsgSend);

    /**
     * 批量添加ummMsgSend
     *
     * @param ummMsgSendDTOS 待添加ummMsgSend
     */
    Boolean batchAdd(List<UmmMsgSendDTO> ummMsgSendDTOS);
    /**
     * 批量添加ummMsgSend
     *
     * @param ummMsgSendDTOS 待添加ummMsgSend
     */
    Boolean batchUpdate(List<UmmMsgSendDTO> ummMsgSendDTOS);

    /**
     * 更新ummMsgSend
     *
     * @param ummMsgSend 待更新ummMsgSend对象
     * @return true，更新成功；false，ummMsgSend不存在
     */
    boolean update(UmmMsgSend ummMsgSend);


    boolean batchUpdateUmmMsgSendStatus(List<Long> sendMsgIds, Integer status);

    /**
     * 通过 id 删除ummMsgSend
     *
     * @param id 账户 id
     * @return true，删除成功；false，ummMsgSend不存在
     */
    boolean deleteById(Long id);

    /**
     * 分页查询消息发送明细
     *
     * @param request 请求体
     * @return 消息发送明细分页结果
     */
    Page<UmmMsgSend> resultPageQuery(PageQueryMessageSendResultRequest request);

    /**
     * 获取该消息发送成功数量
     *
     * @param msgId 消息id
     * @return 该消息发送成功数量
     */
    int getSendSuccessCount(Long msgId);

    /**
     * 获取该消息发送总数量
     *
     * @param msgId 消息id
     * @return 该消息发送总数量
     */
    int getSendCount(Long msgId);

    /**
     * 获取该消息等待数量
     *
     * @param msgId 消息id
     * @return 该消息等待数量
     */
    int getSendAwaitCount(Long msgId);

}
