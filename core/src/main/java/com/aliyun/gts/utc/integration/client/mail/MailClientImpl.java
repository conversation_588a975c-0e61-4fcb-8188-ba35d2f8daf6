package com.aliyun.gts.utc.integration.client.mail;

import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.MailSender;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.util.Assert;

import javax.mail.internet.MimeMessage;
import java.util.Date;

@Slf4j
public class MailClientImpl implements MailClient {

    private final JavaMailSender mailSender;

    public MailClientImpl(MailSender mailSender) {
        this.mailSender = (JavaMailSender) mailSender;
    }

    @Override
    public void sendMail(String subject, String content,
                         String receiverAddress, String sender,
                         boolean htmlContent) {
        Assert.hasText(subject, "subject is blank");
        Assert.hasText(content, "content is blank");
        Assert.hasText(receiverAddress, "receiverAddress is blank");
        Assert.hasText(sender, "sender is blank");
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        try {
            MimeMessageHelper mimeMessageHelper =
                    new MimeMessageHelper(mimeMessage, true);
            mimeMessageHelper.setText(content, htmlContent);
            mimeMessageHelper.setSubject(subject);
            mimeMessageHelper.setFrom(sender);
            mimeMessageHelper.setTo(receiverAddress);
            mimeMessageHelper.setSentDate(new Date());
            log.info(sender + " send mail to " + receiverAddress);
            mailSender.send(mimeMessage);
        } catch (Exception e) {
            log.error(e.getMessage());
            // 报错只记录日志，不报RuntimeException
            // throw new RuntimeException(e);
        }
    }
}
