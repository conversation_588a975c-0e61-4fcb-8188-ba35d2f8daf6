package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.aliyun.gts.utc.api.facade.request.NoticeRollbackRequest;
import com.aliyun.gts.utc.core.event.NoticeRollbackEvent;
import com.aliyun.gts.utc.core.service.NoticeRollbackService;
import com.aliyun.gts.utc.integration.client.MessageClientTypeEnum;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class NoticeRollbackServiceImpl implements NoticeRollbackService {


    private final ApplicationContext context;

    public NoticeRollbackServiceImpl(ApplicationContext context) {
        this.context = context;
    }

    @Override
    public ResultResponse noticeRollback(NoticeRollbackRequest noticeRollbackRequest) {
        Map<String, Object> extend = new HashMap<>();
        context.publishEvent(new NoticeRollbackEvent(noticeRollbackRequest,
                MessageClientTypeEnum.DING_WORK_NOTICE.getClientType(),
                extend));
        return ResultResponse.succResult();
    }
}
