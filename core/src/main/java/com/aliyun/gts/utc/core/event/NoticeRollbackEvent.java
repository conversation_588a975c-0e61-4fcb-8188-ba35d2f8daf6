package com.aliyun.gts.utc.core.event;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

@Setter
@Getter
@ToString
public class NoticeRollbackEvent extends ApplicationEvent {

    private String channelType;

    private Map<String,Object> extend;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public NoticeRollbackEvent(Object source, String channelType, Map<String,Object> extend) {
        super(source);
        this.channelType=channelType;
        this.extend=extend;
    }
}
