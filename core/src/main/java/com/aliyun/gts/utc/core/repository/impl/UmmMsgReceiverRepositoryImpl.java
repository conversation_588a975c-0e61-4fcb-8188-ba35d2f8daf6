/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmMsgReceiver;
import com.aliyun.gts.utc.core.repository.UmmMsgReceiverRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dao.UmmMsgReceiverMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgReceiverDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgReceiverRepositoryImpl.java
 * source table name: umm_msg_receiver
 * initial code lines: 125
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Repository
public class UmmMsgReceiverRepositoryImpl implements UmmMsgReceiverRepository {

    @Autowired
    private UmmMsgReceiverMapper ummMsgReceiverMapper;

    @Override
    public UmmMsgReceiver getById(Long id) {
        LambdaQueryWrapper<UmmMsgReceiverDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgReceiverDO>()
                .eq(UmmMsgReceiverDO::getId, id)
                .eq(UmmMsgReceiverDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .last("limit 1");

        UmmMsgReceiverDO ummMsgReceiverDO = ummMsgReceiverMapper.selectOne(wrapperSelectDetail);
        return UmmMsgReceiverDOConverter.INSTANCE.convert(ummMsgReceiverDO);
    }

    @Override
    public UmmMsgReceiver getByMsgId(Long msgId) {
        LambdaQueryWrapper<UmmMsgReceiverDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgReceiverDO>()
                .eq(UmmMsgReceiverDO::getMsgId,msgId)
                .eq(UmmMsgReceiverDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .last("limit 1");
        UmmMsgReceiverDO ummMsgReceiverDO = ummMsgReceiverMapper.selectOne(wrapperSelectDetail);
        return UmmMsgReceiverDOConverter.INSTANCE.convert(ummMsgReceiverDO);
    }

    @Override
    public IPage<UmmMsgReceiver> getByRange(Long pageNum, Long pageSize,
                                            UmmMsgReceiver ummMsgReceiver) {

        Page<UmmMsgReceiverDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        UmmMsgReceiverDO ummMsgReceiverDO =
                UmmMsgReceiverDOConverter.INSTANCE.convert(ummMsgReceiver);
        ummMsgReceiverDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        QueryWrapper<UmmMsgReceiverDO> queryWrapper = new QueryWrapper();
        queryWrapper.setEntity(ummMsgReceiverDO);
        IPage<UmmMsgReceiverDO> iPage = ummMsgReceiverMapper.selectPage(page,
                queryWrapper);
        List<UmmMsgReceiver> list = iPage.getRecords().stream()
                .map(UmmMsgReceiverDOConverter.INSTANCE::convert)
            .collect(Collectors.toList());
        Page<UmmMsgReceiver> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setSize(iPage.getSize());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    @Override
    public UmmMsgReceiver add(UmmMsgReceiver ummMsgReceiver) {

        UmmMsgReceiverDO ummMsgReceiverDO = UmmMsgReceiverDOConverter.INSTANCE.convert(ummMsgReceiver);
        ummMsgReceiverDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        ummMsgReceiverMapper.insert(ummMsgReceiverDO);
        ummMsgReceiver.setId(ummMsgReceiverDO.getId());
        return ummMsgReceiver;
    }

    @Override
    public boolean update(UmmMsgReceiver ummMsgReceiver) {

        UmmMsgReceiverDO ummMsgReceiverDO = UmmMsgReceiverDOConverter.INSTANCE.convert(ummMsgReceiver);
        LambdaQueryWrapper<UmmMsgReceiverDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgReceiverDO>()
                .eq(UmmMsgReceiverDO::getId, ummMsgReceiverDO.getId())
                .eq(UmmMsgReceiverDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .last("limit 1");

        UmmMsgReceiverDO newUmmMsgReceiverDO = ummMsgReceiverMapper.selectOne(wrapperSelectDetail);
        if (newUmmMsgReceiverDO == null) {
            return false;
        }
        if (ummMsgReceiverDO.getMsgId() != null) {
            newUmmMsgReceiverDO.setMsgId(ummMsgReceiverDO.getMsgId());
        }
        if (ummMsgReceiverDO.getReceiverType() != null) {
            newUmmMsgReceiverDO.setReceiverType(ummMsgReceiverDO.getReceiverType());
        }
        if (ummMsgReceiverDO.getReceiverGuid() != null) {
            newUmmMsgReceiverDO.setReceiverGuid(ummMsgReceiverDO.getReceiverGuid());
        }
        if (ummMsgReceiverDO.getStartDate() != null) {
            newUmmMsgReceiverDO.setStartDate(ummMsgReceiverDO.getStartDate());
        }
        if (ummMsgReceiverDO.getEndDate() != null) {
            newUmmMsgReceiverDO.setEndDate(ummMsgReceiverDO.getEndDate());
        }
        if (ummMsgReceiverDO.getAppId() != null) {
            newUmmMsgReceiverDO.setAppId(ummMsgReceiverDO.getAppId());
        }
        if (ummMsgReceiverDO.getStatus() != null) {
            newUmmMsgReceiverDO.setStatus(ummMsgReceiverDO.getStatus());
        }

        ummMsgReceiverMapper.updateById(newUmmMsgReceiverDO);
        return true;
    }

    @Override
    public boolean deleteById(Long id) {
        LambdaQueryWrapper<UmmMsgReceiverDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgReceiverDO>()
                .eq(UmmMsgReceiverDO::getId, id)
                .eq(UmmMsgReceiverDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .last("limit 1");

        UmmMsgReceiverDO ummMsgReceiverDO = ummMsgReceiverMapper.selectOne(wrapperSelectDetail);
        if (ummMsgReceiverDO == null) {
            return false;
        }
        return ummMsgReceiverMapper.deleteById(id) > 0;
    }

   
    @Mapper
    interface UmmMsgReceiverDOConverter {
        UmmMsgReceiverDOConverter INSTANCE = Mappers.getMapper(UmmMsgReceiverDOConverter.class);

        UmmMsgReceiverDO convert(UmmMsgReceiver ummMsgReceiver);

        UmmMsgReceiver convert(UmmMsgReceiverDO ummMsgReceiverDO);
    }

}