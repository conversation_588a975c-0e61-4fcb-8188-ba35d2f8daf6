/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgChannelDTO;
import com.aliyun.gts.utc.api.facade.request.ummmsgchannel.CreateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgchannel.PageQueryUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgchannel.UpdateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgchannel.*;
import com.aliyun.gts.utc.application.UmmMsgChannelManageAppService;
import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import com.aliyun.gts.utc.core.repository.UmmMsgChannelRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgChannelManageAppServiceImpl.java
 * source table name: umm_msg_channel
 * initial code lines: 113
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Service
public class UmmMsgChannelManageAppServiceImpl implements UmmMsgChannelManageAppService {
    @Autowired
    private UmmMsgChannelRepository ummMsgChannelRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmMsgChannelResponse createUmmMsgChannel(CreateUmmMsgChannelRequest createUmmMsgChannelRequest) {

        UmmMsgChannelDTO ummMsgChannelDTO =
                createUmmMsgChannelRequest.getUmmMsgChannelDTO();

        UmmMsgChannel ummMsgChannel =
                UmmMsgChannelDTOConverter.INSTANCE.convert(ummMsgChannelDTO);
        UmmMsgChannel add = ummMsgChannelRepository.add(ummMsgChannel);
        ummMsgChannelDTO.setId(add.getId());

        CreateUmmMsgChannelResponse response =
                new CreateUmmMsgChannelResponse();
        response.setUmmMsgChannelDTO(ummMsgChannelDTO);

        return response;
    }

    @Override
    public QueryUmmMsgChannelResponse queryUmmMsgChannel(Long id) {

        UmmMsgChannel ummMsgChannel = ummMsgChannelRepository.getById(id);
        UmmMsgChannelDTO ummMsgChannelDTO =
                UmmMsgChannelDTOConverter.INSTANCE.convert(ummMsgChannel);
        QueryUmmMsgChannelResponse response = new QueryUmmMsgChannelResponse();
        response.setUmmMsgChannelDTO(ummMsgChannelDTO);
        return response;
    }

    @Override
    public PageQueryUmmMsgChannelResponse pageQueryUmmMsgChannel(PageQueryUmmMsgChannelRequest request) {

        UmmMsgChannelDTO ummMsgChannelDTO = request.getUmmMsgChannelDTO();
        UmmMsgChannel ummMsgChannel =
                UmmMsgChannelDTOConverter.INSTANCE.convert(ummMsgChannelDTO);
        IPage<UmmMsgChannel> byRange =
                ummMsgChannelRepository.getByRange(request.getPageNum(),
                        request.getPageSize(), ummMsgChannel);

        PageQueryUmmMsgChannelResponse response =
                new PageQueryUmmMsgChannelResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
                .map(UmmMsgChannelDTOConverter.INSTANCE::convert)
                .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmMsgChannelResponse deleteUmmMsgChannel(Long id) {
        DeleteUmmMsgChannelResponse deleteUmmMsgChannelResponse =
                new DeleteUmmMsgChannelResponse();
        deleteUmmMsgChannelResponse.setIsDelete(ummMsgChannelRepository.deleteById(id));
        return deleteUmmMsgChannelResponse;
    }

    @Override
    public UpdateUmmMsgChannelResponse updateUmmMsgChannel(UpdateUmmMsgChannelRequest request) {

        UmmMsgChannelDTO ummMsgChannelDTO = request.getUmmMsgChannelDTO();

        UmmMsgChannel ummMsgChannel =
                UmmMsgChannelDTOConverter.INSTANCE.convert(ummMsgChannelDTO);
        UpdateUmmMsgChannelResponse updateUmmMsgChannelResponse =
                new UpdateUmmMsgChannelResponse();
        updateUmmMsgChannelResponse.setIsUpdate(ummMsgChannelRepository.update(ummMsgChannel));
        return updateUmmMsgChannelResponse;
    }

    @Mapper
    interface UmmMsgChannelDTOConverter {
        UmmMsgChannelDTOConverter INSTANCE =
                Mappers.getMapper(UmmMsgChannelDTOConverter.class);

        UmmMsgChannel convert(UmmMsgChannelDTO ummMsgChannelDTO);

        UmmMsgChannelDTO convert(UmmMsgChannel ummMsgChannel);
    }

}