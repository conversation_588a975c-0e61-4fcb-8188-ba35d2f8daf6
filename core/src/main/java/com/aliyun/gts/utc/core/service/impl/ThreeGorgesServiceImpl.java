package com.aliyun.gts.utc.core.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.source.SmsSourceRequest;
import com.aliyun.gts.utc.common.constants.HttpConstant;
import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.core.service.ThreeGorgesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * 三峡短信实现类
 *
 * @author: guoshengLi
 * @create: 2025-02-26 17:29
 * @Description:
 */
@Slf4j
@Service
public class ThreeGorgesServiceImpl implements ThreeGorgesService {

  @Value("${third.smsUrl}")
  private String smsUrl;

  @Value("${third.apiKey}")
  private String apiKey;

  /**
   * todo 接口未测试 appid areaid businessid
   * <p>
   * 发送短信
   *
   * @param ummSource
   * @param ummMsgSendDTO
   * @return
   */
  @Override
  public Boolean sendSms(UmmSource ummSource, UmmMsgSendDTO ummMsgSendDTO) {
    Map<String, String> formDataJson = new HashMap<>();
    formDataJson.put("mobilePhone", ummMsgSendDTO.getPhone());
    formDataJson.put("content", ummMsgSendDTO.getMsgContent());
    Date gmtCreate = ummMsgSendDTO.getGmtCreate();
    if (null != gmtCreate) {
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      String formattedDate = simpleDateFormat.format(gmtCreate);
      //Date转  yyyy-MM-dd HH:mm:ss 字符串
      formDataJson.put("sendDate", formattedDate);
    }
    try {
      SmsSourceRequest sourceParamSms = JSONObject.parseObject(ummSource.getSourceParam(), SmsSourceRequest.class);
      HttpRequest request = HttpRequest.post(smsUrl)
              .header("Content-Type", "application/json")
              .header("apiKey", apiKey)
              .header("appid", sourceParamSms.getAppId())
              .header("areaid", sourceParamSms.getAreaId())
              .header("businessid", sourceParamSms.getBusinessId())
              .body(JSONObject.toJSONString(formDataJson));
      request.setConnectionTimeout(HttpConstant.CONNECTION_TIMEOUT);
      request.setReadTimeout(HttpConstant.READ_TIMEOUT);
      HttpResponse execute = request.execute();
      String result = execute.body();
      log.info("获取结果：{}", result);
      if (StrUtil.isNotBlank(result) && HttpStatus.OK.value() == execute.getStatus()) {
        JSONObject jsonResponse = JSON.parseObject(result);
        JSONObject error = jsonResponse.getJSONObject("error");
        if(null != error && error.get("message") != null){
          log.error("发送短信失败：{}", error.get("message"));
          return false;
        }

      } else {
        Object message = JSON.parseObject(result).get("message");
        log.error("发送短信失败：{}", message);
        return false;
      }
    } catch (Exception e) {
      log.error("发送短信失败：{}", e.getMessage());
      return false;
    }
    return true;
  }


}
