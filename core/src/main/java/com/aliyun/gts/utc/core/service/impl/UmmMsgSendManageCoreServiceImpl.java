/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsend.PageQueryUmmMsgSendRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsend.*;
import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import com.aliyun.gts.utc.core.repository.UmmMsgSendRepository;
import com.aliyun.gts.utc.core.service.UmmMsgSendManageCoreService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class UmmMsgSendManageCoreServiceImpl implements UmmMsgSendManageCoreService {

    private final UmmMsgSendRepository ummMsgSendRepository;

    public UmmMsgSendManageCoreServiceImpl(UmmMsgSendRepository ummMsgSendRepository) {
        this.ummMsgSendRepository = ummMsgSendRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmMsgSendResponse createUmmMsgSend(UmmMsgSendDTO ummMsgSendDTO) {


        UmmMsgSend ummMsgSend =
                UmmMsgSendDTOConverter.INSTANCE.convert(ummMsgSendDTO);
        UmmMsgSend add = ummMsgSendRepository.add(ummMsgSend);
        ummMsgSendDTO.setId(add.getId());

        CreateUmmMsgSendResponse response = new CreateUmmMsgSendResponse();
        response.setUmmMsgSendDTO(ummMsgSendDTO);

        return response;
    }

    @Override
    public Boolean batchCreateUmmMsgSend(List<UmmMsgSendDTO> ummMsgSendDTOS) {
        return ummMsgSendRepository.batchAdd(ummMsgSendDTOS);
    }

    @Override
    public Boolean batchUpdateUmmMsgSend(List<UmmMsgSendDTO> ummMsgSendDTOS) {
        return ummMsgSendRepository.batchUpdate(ummMsgSendDTOS);
    }

    @Override
    public QueryUmmMsgSendResponse queryUmmMsgSend(Long id) {
        LambdaQueryWrapper<UmmMsgSend> queryWrapper = new LambdaQueryWrapper<>();

        UmmMsgSend ummMsgSend = ummMsgSendRepository.getById(id);
        UmmMsgSendDTO ummMsgSendDTO = UmmMsgSendDTOConverter.INSTANCE.convert(ummMsgSend);
        QueryUmmMsgSendResponse response = new QueryUmmMsgSendResponse();
        response.setUmmMsgSendDTO(ummMsgSendDTO);
        return response;
    }

    @Override
    public List<UmmMsgSendDTO> queryUmmMsgSendByMsgId(String msgId) {
        return ummMsgSendRepository.queryUmmMsgSendByMsgId(msgId);
    }

    @Override
    public PageQueryUmmMsgSendResponse pageQueryUmmMsgSend(PageQueryUmmMsgSendRequest request) {

        UmmMsgSendDTO ummMsgSendDTO = request.getUmmMsgSendDTO();
        UmmMsgSend ummMsgSend = UmmMsgSendDTOConverter.INSTANCE.convert(ummMsgSendDTO);
        IPage<UmmMsgSend> byRange = ummMsgSendRepository.getByRange(request.getPageNum(), request.getPageSize(), ummMsgSend);

        PageQueryUmmMsgSendResponse response = new PageQueryUmmMsgSendResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
                .map(UmmMsgSendDTOConverter.INSTANCE::convert)
                .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmMsgSendResponse deleteUmmMsgSend(Long id) {
        DeleteUmmMsgSendResponse deleteUmmMsgSendResponse = new DeleteUmmMsgSendResponse();
        deleteUmmMsgSendResponse.setIsDelete(ummMsgSendRepository.deleteById(id));
        return deleteUmmMsgSendResponse;
    }

    @Override
    public UpdateUmmMsgSendResponse updateUmmMsgSend(UmmMsgSendDTO ummMsgSendDTO) {

        UmmMsgSend ummMsgSend = UmmMsgSendDTOConverter.INSTANCE.convert(ummMsgSendDTO);
        UpdateUmmMsgSendResponse updateUmmMsgSendResponse = new UpdateUmmMsgSendResponse();
        updateUmmMsgSendResponse.setIsUpdate(ummMsgSendRepository.update(ummMsgSend));
        return updateUmmMsgSendResponse;
    }

    @Override
    public Boolean batchUpdateUmmMsgSendStatus(List<Long> sendMsgIds, Integer status) {
        return ummMsgSendRepository.batchUpdateUmmMsgSendStatus(sendMsgIds, status);
    }

    @Mapper
    interface UmmMsgSendDTOConverter {
        UmmMsgSendDTOConverter INSTANCE = Mappers.getMapper(UmmMsgSendDTOConverter.class);

        UmmMsgSend convert(UmmMsgSendDTO ummMsgSendDTO);

        UmmMsgSendDTO convert(UmmMsgSend ummMsgSend);
    }

}
