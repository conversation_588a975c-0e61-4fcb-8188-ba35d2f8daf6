/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dataobject;

import com.aliyun.gts.utc.core.dataobject.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgParameterDO.java
 * source table name: umm_msg_parameter
 * initial code lines: 125
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Data
@TableName("umm_msg_parameter")
public class UmmMsgParameterDO extends BaseEntity {

    /**
     * 参数id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * msg主键id
     */
    @TableField("guid")
    private String guid;

    /**
     *
     */
    @TableField("param_name")
    private String paramName;

    /**
     * 参数表达式
     */
    @TableField("param_exp")
    private String paramExp;

    /**
     * 状态 0禁用 1启用
     */
    @TableField("status")
    private Long status;

    /**
     * 创建人guid
     */
    @TableField("create_user_guid")
    private String createUserGuid;

    /**
     * 创建人姓名
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 修改人guid
     */
    @TableField("update_user_guid")
    private String updateUserGuid;

    /**
     * 修改人姓名
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField("gmt_modify")
    private Date gmtModify;

    /**
     * 所有外部系统冗余字段，以json形式存储
     */
    @TableField("features")
    private String features;

    /**
     * 逻辑删除
     */
    @TableField("is_deleted")
    private Long isDeleted;

    /**
     * 版本号
     */
    @TableField("version")
    private Integer version;

    /**
     * 应用id
     */
    @TableField("app_id")
    private Long appId;

    /**
     * 租户编码
     */
    @TableField("tenant_code")
    private String tenantCode;
}
