/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.domain;

import lombok.Data;

import java.util.Date;


@Data
public class UmmTemplate {

    /**
    * 主键id
    */
    private Long id;

    /**
    * 
    */
    private String guid;

    /**
    * 消息标题
    */
    private String tplTitle;

    /**
    * 消息内容
    */
    private String tplContent;
    /**
    * 模板编码
    */
    private String tplCode;

    /**
    * 发送参数json存储，存储模板中涉及到的参数，存储数组
    */
    private String param;

    /**
     * 栏目id
     */
    private Long categoryId;

    /**
    * 支持的channels, 存储channel id的数组
    */
    private String supportChannels;

    /**
    * 状态 0禁用 1启用
    */
    private Integer status;

    /**
    * 创建人guid
    */
    private String createUserGuid;

    /**
    * 创建人姓名
    */
    private String createUserName;

    /**
    * 修改人guid
    */
    private String updateUserGuid;

    /**
    * 修改人姓名
    */
    private String updateUserName;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModify;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    private String features;

    /**
    * 逻辑删除
    */
    private Long isDeleted;

    /**
    * 版本号
    */
    private Integer version;

    /**
    * 应用id
    */
    private Long appId;

    /**
    * 租户编码
    */
    private String tenantCode;

    /**
     * 消息源id
     */
    private Long sourceId;
}