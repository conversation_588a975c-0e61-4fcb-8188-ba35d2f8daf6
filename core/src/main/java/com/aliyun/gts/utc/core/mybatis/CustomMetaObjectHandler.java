package com.aliyun.gts.utc.core.mybatis;

import com.aliyun.gts.bpaas.framework.api.util.UuidUtil;
import com.aliyun.gts.utc.core.utils.DateUtil;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import java.util.Date;
import java.util.Objects;
import java.util.function.Supplier;
import org.apache.ibatis.reflection.MetaObject;

public class CustomMetaObjectHandler implements MetaObjectHandler {
    private static final String FIELD_GTM_CREATE = "gmtCreate";
    private static final String FIELD_GTM_MODIFIED = "gmtModify";
    private static final String FIELD_CREATE_USER_GUID = "createUserGuid";
    private static final String FIELD_CREATE_USER_NAME = "createUserName";
    private static final String FIELD_UPDATE_USER_GUID = "updateUserGuid";
    private static final String FIELD_UPDATE_USER_NAME = "updateUserName";
    private static final String FIELD_TENANT_CODE = "tenantCode";
    private static final String FIELD_APP_ID = "appId";
    private static final String FIELD_GUID = "guid";

    public CustomMetaObjectHandler() {
    }

    public void insertFill(MetaObject metaObject) {
        Date now = DateUtil.getNow();
        this.strictInsertFill(metaObject, "gmtCreate", Date.class, now);
        this.strictInsertFill(metaObject, "gmtModify", Date.class, now);
        this.strictInsertFill(metaObject, "createUserGuid", String.class, this.getUserGuid());
        this.strictInsertFill(metaObject, "createUserName", String.class, this.getUserName());
        this.strictInsertFill(metaObject, "updateUserGuid", String.class, this.getUserGuid());
        this.strictInsertFill(metaObject, "updateUserName", String.class, this.getUserName());
        if (metaObject.hasGetter("guid") && null == metaObject.getValue("guid")) {
            this.strictInsertFill(metaObject, "guid", String.class, UuidUtil.uuidWithoutUnderscore());
        }

        if (metaObject.hasGetter("tenantCode") && null == metaObject.getValue("tenantCode")) {
            this.strictInsertFill(metaObject, "tenantCode", String.class, this.getTenantCode());
        }

        if (metaObject.hasGetter("appId") && null == metaObject.getValue("appId")) {
            this.strictInsertFill(metaObject, "appId", String.class, this.getAppId());
        }

    }

    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "gmtModify", Date.class, DateUtil.getNow());
        this.strictUpdateFill(metaObject, "updateUserGuid", String.class, this.getUserGuid());
        this.strictUpdateFill(metaObject, "updateUserName", String.class, this.getUserName());
    }

    public MetaObjectHandler strictFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        Object obj = fieldVal.get();
        if (Objects.nonNull(obj)) {
            metaObject.setValue(fieldName, obj);
        }

        return this;
    }

    private String getUserGuid() {
        return RuntimeContextUtil.getUserGuid();
    }

    private String getUserName() {
        return RuntimeContextUtil.getUserName();
    }

    private String getTenantCode() {
        return RuntimeContextUtil.getTenantCode();
    }

    private String getAppId() {
        return RuntimeContextUtil.getAppId();
    }
}