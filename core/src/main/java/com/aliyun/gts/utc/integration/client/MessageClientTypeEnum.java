package com.aliyun.gts.utc.integration.client;

import java.util.HashMap;
import java.util.Map;

public enum MessageClientTypeEnum {

    //    INTERNAL(1L, "INTERNAL"),
    DING_WORK_NOTICE(2L, "DING_WORK_NOTICE", "钉钉"),

    //    SMS(3L, "SMS"),
    MAIL(5L, "MAIL", "邮件"),
    //短信
    ALISMS(6L, "ALISMS", "短信"),
    SMS(7L, "SMS", "短信"),
    SXXY(8L, "SXXY", "三峡行云");

    private Long clientId;

    private String clientType;

    private String clientName;


    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public Long getClientID() {
        return clientId;
    }

    public void setClientID(Long clientID) {
        this.clientId = clientID;
    }

    MessageClientTypeEnum(Long clientID, String clientType,
                          String clientName) {
        this.clientId = clientID;
        this.clientType = clientType;
        this.clientName = clientName;
    }

    public static MessageClientTypeEnum getById(Long id) {
        for (MessageClientTypeEnum ele : values()) {
            if (ele.getClientID().equals(id)) {
                return ele;
            }
        }
        return null;
    }

    public static MessageClientTypeEnum getByCode(String code) {
        for (MessageClientTypeEnum ele : values()) {
            if (ele.getClientType().equals(code)) {
                return ele;
            }
        }
        return null;
    }

    public static Long getChannelId(String channelType) {
        for (MessageClientTypeEnum ele : values()) {
            if (ele.getClientType().equals(channelType)) {
                return ele.getClientID();
            }
        }
        return null;
    }

    public static String getChannelType(Long channelId) {
        for (MessageClientTypeEnum ele : values()) {
            if (ele.getClientID().equals(channelId)) {
                return ele.getClientType();
            }
        }
        return null;
    }

    public String getClientName() {
        return clientName;
    }

    private static final Map<Long, String> clientIdToClientNameMap = new HashMap<>();

    static {
        for (MessageClientTypeEnum type : MessageClientTypeEnum.values()) {
            clientIdToClientNameMap.put(type.getClientID(), type.getClientName());
        }
    }

    public static Map<Long, String> getClientIdToClientNameMap() {
        return new HashMap<>(clientIdToClientNameMap);
    }
}
