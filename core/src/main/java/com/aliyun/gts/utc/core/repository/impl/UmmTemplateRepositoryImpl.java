/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.common.constants.MessageTemplateConstant;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dao.UmmTemplateMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmTemplateDO;
import com.aliyun.gts.utc.dal.dto.MessageTemplateQueryDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageTemplateDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmTemplateRepositoryImpl.java
 * source table name: umm_template
 * initial code lines: 152
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Repository
public class UmmTemplateRepositoryImpl implements UmmTemplateRepository {

    @Autowired
    private UmmTemplateMapper ummTemplateMapper;

    @Override
    public UmmTemplate getById(Long id) {
        LambdaQueryWrapper<UmmTemplateDO> queryWrapper = new QueryWrapper<UmmTemplateDO>()
                .lambda().eq(UmmTemplateDO::getId, id)
                .eq(UmmTemplateDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmTemplateDO ummTemplateDO = ummTemplateMapper.selectOne(queryWrapper);
        return UmmTemplateDOConverter.INSTANCE.convert(ummTemplateDO);
    }

    @Override
    public UmmTemplate getByCode(String code) {
        LambdaQueryWrapper<UmmTemplateDO> queryWrapper = new QueryWrapper<UmmTemplateDO>()
                .lambda().eq(UmmTemplateDO::getTplCode, code)
                .eq(UmmTemplateDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .eq(UmmTemplateDO::getIsDeleted, MessageTemplateConstant.NOT_DELETED).last("limit 1");
        UmmTemplateDO ummTemplateDO = ummTemplateMapper.selectOne(queryWrapper);
        UmmTemplate ummTemplate = UmmTemplateDOConverter.INSTANCE.convert(ummTemplateDO);
        return ummTemplate;
    }

    @Override
    public IPage<UmmTemplate> getByRange(int pageNum, int pageSize,
                                         UmmTemplate ummTemplate) {

        Page<UmmTemplateDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        UmmTemplateDO ummTemplateDO =
                UmmTemplateDOConverter.INSTANCE.convert(ummTemplate);
        ummTemplateDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        QueryWrapper<UmmTemplateDO> queryWrapper = new QueryWrapper();
        queryWrapper.setEntity(ummTemplateDO);
        IPage<UmmTemplateDO> iPage = ummTemplateMapper.selectPage(page,
                queryWrapper);
        List<UmmTemplate> list = iPage.getRecords().stream()
                .map(UmmTemplateDOConverter.INSTANCE::convert)
            .collect(Collectors.toList());
        Page<UmmTemplate> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setSize(iPage.getSize());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    @Override
    public UmmTemplate add(UmmTemplate ummTemplate) {

        UmmTemplateDO ummTemplateDO = UmmTemplateDOConverter.INSTANCE.convert(ummTemplate);
        ummTemplateDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        ummTemplateMapper.insert(ummTemplateDO);
        ummTemplate.setId(ummTemplateDO.getId());
        return ummTemplate;
    }

    @Override
    public Boolean checkTplCode(String tplCode) {
        LambdaQueryWrapper<UmmTemplateDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmTemplateDO>()
                .eq(UmmTemplateDO::getIsDeleted, MessageTemplateConstant.NOT_DELETED)
                .eq(UmmTemplateDO::getTplCode, tplCode)
                .eq(UmmTemplateDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmTemplateDO ummTemplateDO = ummTemplateMapper.selectOne(wrapperSelectDetail);
        if (ummTemplateDO != null) {
            return true;
        }
        return false;
    }

    @Override
    public boolean update(UmmTemplate ummTemplate) {

        UmmTemplateDO ummTemplateDO = UmmTemplateDOConverter.INSTANCE.convert(ummTemplate);
        LambdaQueryWrapper<UmmTemplateDO> queryWrapper = new QueryWrapper<UmmTemplateDO>()
                .lambda().eq(UmmTemplateDO::getId, ummTemplateDO.getId())
                .eq(UmmTemplateDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmTemplateDO newUmmTemplateDO = ummTemplateMapper.selectOne(queryWrapper);

        if (newUmmTemplateDO == null) {
            return false;
        }
        if (ummTemplateDO.getGuid() != null) {
            newUmmTemplateDO.setGuid(ummTemplateDO.getGuid());
        }
        if (ummTemplateDO.getTplTitle() != null) {
            newUmmTemplateDO.setTplTitle(ummTemplateDO.getTplTitle());
        }
        if (ummTemplateDO.getTplContent() != null) {
            newUmmTemplateDO.setTplContent(ummTemplateDO.getTplContent());
        }
        if (ummTemplateDO.getParam() != null) {
            newUmmTemplateDO.setParam(ummTemplateDO.getParam());
        }
        if (ummTemplateDO.getSupportChannels() != null) {
            newUmmTemplateDO.setSupportChannels(ummTemplateDO.getSupportChannels());
        }
        if (ummTemplateDO.getStatus() != null) {
            newUmmTemplateDO.setStatus(ummTemplateDO.getStatus());
        }
        if (ummTemplateDO.getCreateUserGuid() != null) {
            newUmmTemplateDO.setCreateUserGuid(ummTemplateDO.getCreateUserGuid());
        }
        if (ummTemplateDO.getCreateUserName() != null) {
            newUmmTemplateDO.setCreateUserName(ummTemplateDO.getCreateUserName());
        }
        if (ummTemplateDO.getUpdateUserGuid() != null) {
            newUmmTemplateDO.setUpdateUserGuid(ummTemplateDO.getUpdateUserGuid());
        }
        if (ummTemplateDO.getUpdateUserName() != null) {
            newUmmTemplateDO.setUpdateUserName(ummTemplateDO.getUpdateUserName());
        }
        if (ummTemplateDO.getGmtCreate() != null) {
            newUmmTemplateDO.setGmtCreate(ummTemplateDO.getGmtCreate());
        }
        if (ummTemplateDO.getGmtModify() != null) {
            newUmmTemplateDO.setGmtModify(ummTemplateDO.getGmtModify());
        }
        if (ummTemplateDO.getFeatures() != null) {
            newUmmTemplateDO.setFeatures(ummTemplateDO.getFeatures());
        }
        if (ummTemplateDO.getIsDeleted() != null) {
            newUmmTemplateDO.setIsDeleted(ummTemplateDO.getIsDeleted());
        }
        if (ummTemplateDO.getVersion() != null) {
            newUmmTemplateDO.setVersion(ummTemplateDO.getVersion());
        }
        if (ummTemplateDO.getAppId() != null) {
            newUmmTemplateDO.setAppId(ummTemplateDO.getAppId());
        }
        if(ummTemplateDO.getCategoryId() != null) {
            newUmmTemplateDO.setCategoryId(ummTemplate.getCategoryId());
        }
        if(ummTemplateDO.getTplCode() != null){
            newUmmTemplateDO.setTplCode(ummTemplate.getTplCode());
        }
        if(ummTemplateDO.getSourceId() != null){
            newUmmTemplateDO.setSourceId(ummTemplate.getSourceId());
        }
        // 更新20231017：移除这段神逻辑
        //处理core拦截器更新时时间、更新人guid、更新人name不生效问题
//        newUmmTemplateDO.setGmtModify(null);
//        newUmmTemplateDO.setUpdateUserGuid(null);
//        newUmmTemplateDO.setUpdateUserName(null);

        ummTemplateMapper.updateById(newUmmTemplateDO);
        return true;
    }

    @Override
    public boolean deleteById(Long id) {
        LambdaQueryWrapper<UmmTemplateDO> queryWrapper = new QueryWrapper<UmmTemplateDO>()
                .lambda().eq(UmmTemplateDO::getId, id)
                .eq(UmmTemplateDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmTemplateDO ummTemplateDO = ummTemplateMapper.selectOne(queryWrapper);
        if (ummTemplateDO == null) {
            return false;
        }
        return ummTemplateMapper.deleteById(id) > 0;
    }

    /**
     * 批量逻辑删除消息模板
     *
     * @param idList 消息模板id列表
     * @return 删除结果
     */
    @Override
    public boolean batchDelete(List<Long> idList) {
        return ummTemplateMapper.deleteBatchIds(idList) > 0;
    }

    /**
     * 分页查询消息模板
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    @Override
    public Page<PageMessageTemplateDTO> pageQuery(Page<PageMessageTemplateDTO> page, MessageTemplateQueryDTO query) {
        query.setTenantCode(RuntimeContextUtil.getTenantCode());
        return ummTemplateMapper.pageQuery(page,query);
    }

    /**
     * 统计标题数量
     *
     * @param msgTitle 标题
     * @param msgId    消息id
     * @return 数量
     */
    @Override
    public int countTitle(String msgTitle, Long msgId) {
        LambdaQueryWrapper<UmmTemplateDO> queryWrapper = new QueryWrapper<UmmTemplateDO>()
                .lambda().eq(UmmTemplateDO::getTplTitle, msgTitle)
                .eq(UmmTemplateDO::getIsDeleted, MessageTemplateConstant.NOT_DELETED)
                .eq(UmmTemplateDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        if(null != msgId){
            queryWrapper.ne(UmmTemplateDO::getId,msgId);
        }
        return Math.toIntExact(ummTemplateMapper.selectCount(queryWrapper));
    }

    /**
     * 通过栏目id获取模板
     *
     * @param categoryId 栏目id
     * @return 模板列表
     */
    @Override
    public List<UmmTemplate> getByCategoryId(Long categoryId) {
        LambdaQueryWrapper<UmmTemplateDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UmmTemplateDO::getCategoryId, categoryId)
                .eq(UmmTemplateDO::getIsDeleted, MessageTemplateConstant.NOT_DELETED)
                .eq(UmmTemplateDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .eq(UmmTemplateDO::getStatus, MessageTemplateConstant.ENABLE);
        return ummTemplateMapper.selectList(queryWrapper)
                .stream().map(UmmTemplateDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public  List<UmmTemplate> getBySourceId(Long sourceId) {
        LambdaQueryWrapper<UmmTemplateDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UmmTemplateDO::getSourceId, sourceId)
                .eq(UmmTemplateDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .eq(UmmTemplateDO::getIsDeleted, MessageTemplateConstant.NOT_DELETED);

        return ummTemplateMapper.selectList(queryWrapper)
                .stream().map(UmmTemplateDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Mapper
    interface UmmTemplateDOConverter {
        UmmTemplateDOConverter INSTANCE = Mappers.getMapper(UmmTemplateDOConverter.class);

        UmmTemplateDO convert(UmmTemplate ummTemplate);

        UmmTemplate convert(UmmTemplateDO ummTemplateDO);
    }

}