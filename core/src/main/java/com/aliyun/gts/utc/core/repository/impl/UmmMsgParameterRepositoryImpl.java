/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.api.facade.request.parameter.PageQueryMessageParameterRequest;
import com.aliyun.gts.utc.common.constants.MessageConstant;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import com.aliyun.gts.utc.core.repository.UmmMsgParameterRepository;
import com.aliyun.gts.utc.dal.dao.UmmMsgParameterMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgParameterDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgParameterRepositoryImpl.java
 * source table name: umm_msg_parameter
 * initial code lines: 146
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Repository
public class UmmMsgParameterRepositoryImpl implements UmmMsgParameterRepository {

    @Autowired
    private UmmMsgParameterMapper ummMsgParameterMapper;

    @Override
    public UmmMsgParameter getById(Long id) {

        UmmMsgParameterDO ummMsgParameterDO =
                ummMsgParameterMapper.selectById(id);
        return UmmMsgParameterDOConverter.INSTANCE.convert(ummMsgParameterDO);
    }

    @Override
    public IPage<UmmMsgParameter> getByRange(int pageNum, int pageSize,
                                             UmmMsgParameter ummMsgParameter) {

        Page<UmmMsgParameterDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        UmmMsgParameterDO ummMsgParameterDO =
                UmmMsgParameterDOConverter.INSTANCE.convert(ummMsgParameter);
        QueryWrapper<UmmMsgParameterDO> queryWrapper = new QueryWrapper();
        queryWrapper.setEntity(ummMsgParameterDO);
        IPage<UmmMsgParameterDO> iPage =
                ummMsgParameterMapper.selectPage(page, queryWrapper);
        List<UmmMsgParameter> list = iPage.getRecords().stream()
                .map(UmmMsgParameterDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
        Page<UmmMsgParameter> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setSize(iPage.getSize());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    @Override
    public UmmMsgParameter add(UmmMsgParameter ummMsgParameter) {

        UmmMsgParameterDO ummMsgParameterDO =
                UmmMsgParameterDOConverter.INSTANCE.convert(ummMsgParameter);
        ummMsgParameterMapper.insert(ummMsgParameterDO);
        ummMsgParameter.setId(ummMsgParameterDO.getId());
        return ummMsgParameter;
    }

    @Override
    public boolean update(UmmMsgParameter ummMsgParameter) {

        UmmMsgParameterDO ummMsgParameterDO =
                UmmMsgParameterDOConverter.INSTANCE.convert(ummMsgParameter);
        UmmMsgParameterDO newUmmMsgParameterDO =
                ummMsgParameterMapper.selectById(ummMsgParameterDO.getId());
        if (newUmmMsgParameterDO == null) {
            return false;
        }
        if (ummMsgParameterDO.getGuid() != null) {
            newUmmMsgParameterDO.setGuid(ummMsgParameterDO.getGuid());
        }
        if (ummMsgParameterDO.getParamName() != null) {
            newUmmMsgParameterDO.setParamName(ummMsgParameterDO.getParamName());
        }
        if (ummMsgParameterDO.getParamExp() != null) {
            newUmmMsgParameterDO.setParamExp(ummMsgParameterDO.getParamExp());
        }
        if (ummMsgParameterDO.getStatus() != null) {
            newUmmMsgParameterDO.setStatus(ummMsgParameterDO.getStatus());
        }
        if (ummMsgParameterDO.getCreateUserGuid() != null) {
            newUmmMsgParameterDO.setCreateUserGuid(ummMsgParameterDO.getCreateUserGuid());
        }
        if (ummMsgParameterDO.getCreateUserName() != null) {
            newUmmMsgParameterDO.setCreateUserName(ummMsgParameterDO.getCreateUserName());
        }
        if (ummMsgParameterDO.getUpdateUserGuid() != null) {
            newUmmMsgParameterDO.setUpdateUserGuid(ummMsgParameterDO.getUpdateUserGuid());
        }
        if (ummMsgParameterDO.getUpdateUserName() != null) {
            newUmmMsgParameterDO.setUpdateUserName(ummMsgParameterDO.getUpdateUserName());
        }
        if (ummMsgParameterDO.getGmtCreate() != null) {
            newUmmMsgParameterDO.setGmtCreate(ummMsgParameterDO.getGmtCreate());
        }
        if (ummMsgParameterDO.getGmtModify() != null) {
            newUmmMsgParameterDO.setGmtModify(ummMsgParameterDO.getGmtModify());
        }
        if (ummMsgParameterDO.getFeatures() != null) {
            newUmmMsgParameterDO.setFeatures(ummMsgParameterDO.getFeatures());
        }
        if (ummMsgParameterDO.getIsDeleted() != null) {
            newUmmMsgParameterDO.setIsDeleted(ummMsgParameterDO.getIsDeleted());
        }
        if (ummMsgParameterDO.getVersion() != null) {
            newUmmMsgParameterDO.setVersion(ummMsgParameterDO.getVersion());
        }
        if (ummMsgParameterDO.getAppId() != null) {
            newUmmMsgParameterDO.setAppId(ummMsgParameterDO.getAppId());
        }
        if (ummMsgParameterDO.getTenantCode() != null) {
            newUmmMsgParameterDO.setTenantCode(ummMsgParameterDO.getTenantCode());
        }

        ummMsgParameterMapper.updateById(newUmmMsgParameterDO);
        return true;
    }

    @Override
    public boolean deleteById(Long id) {
        return ummMsgParameterMapper.deleteById(id) > 0;
    }

    /**
     * 获取所有系统参数
     *
     * @return 系统参数列表
     */
    @Override
    public List<UmmMsgParameter> getAll() {
        LambdaQueryWrapper<UmmMsgParameterDO> queryWrapper =
                new QueryWrapper<UmmMsgParameterDO>()
                .lambda().eq(UmmMsgParameterDO::getIsDeleted,
                        MessageConstant.NOT_DELETED);
        return ummMsgParameterMapper.selectList(queryWrapper).stream()
                .map(UmmMsgParameterDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    /**
     * 根据查询条件分页查询参数
     *
     * @param pageNum  页码
     * @param pageSize 页面大小
     * @param request  查询条件
     * @return 分页后的参数列表
     */
    @Override
    public Page<UmmMsgParameter> getPageQuery(int pageNum, int pageSize,
                                              PageQueryMessageParameterRequest request) {
        String query = request.getQuery();
        Page<UmmMsgParameterDO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<UmmMsgParameterDO> queryWrapper =
                new QueryWrapper<UmmMsgParameterDO>()
                        .lambda().eq(UmmMsgParameterDO::getIsDeleted,
                        MessageConstant.NOT_DELETED);
        if (StringUtils.isNotEmpty(query)) {
            queryWrapper.like(UmmMsgParameterDO::getParamName, query)
                    .or().like(UmmMsgParameterDO::getParamExp, query);
        }
        if (StringUtils.isNotEmpty(request.getOrderField())) {
            switch (request.getOrderField()) {
                case MessageConstant.PARAM_NAME:
                    if (null == request.getIsAsc() || !request.getIsAsc()) {
                        queryWrapper.orderByDesc(UmmMsgParameterDO::getParamName);
                    } else {
                        queryWrapper.orderByAsc(UmmMsgParameterDO::getParamName);
                    }
                    break;
                case MessageConstant.PARAM_EXP:
                    if (null == request.getIsAsc() || !request.getIsAsc()) {
                        queryWrapper.orderByDesc(UmmMsgParameterDO::getParamExp);
                    } else {
                        queryWrapper.orderByAsc(UmmMsgParameterDO::getParamExp);
                    }
                    break;
                default:
                    break;
            }
        } else {
            queryWrapper.orderByAsc(UmmMsgParameterDO::getParamName);
        }
        Page<UmmMsgParameterDO> iPage = ummMsgParameterMapper.selectPage(page
                , queryWrapper);
        List<UmmMsgParameter> list = iPage.getRecords().stream()
                .map(UmmMsgParameterDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
        Page<UmmMsgParameter> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setTotal(iPage.getTotal());
        newPage.setSize(iPage.getSize());
        newPage.setRecords(list);
        return newPage;

    }

    @Mapper
    interface UmmMsgParameterDOConverter {
        UmmMsgParameterDOConverter INSTANCE =
                Mappers.getMapper(UmmMsgParameterDOConverter.class);

        UmmMsgParameterDO convert(UmmMsgParameter ummMsgParameter);

        UmmMsgParameter convert(UmmMsgParameterDO ummMsgParameterDO);
    }

    @Override
    public List<UmmMsgParameterDO> query() {
        LambdaQueryWrapper<UmmMsgParameterDO> wrapperSelectDetail =
                new LambdaQueryWrapper<UmmMsgParameterDO>();
        return ummMsgParameterMapper.selectList(wrapperSelectDetail);
    }
}