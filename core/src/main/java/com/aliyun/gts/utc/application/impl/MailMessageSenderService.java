package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.MailHostPropsDTO;
import com.aliyun.gts.utc.api.facade.request.MailMessageSendRequest;
import com.aliyun.gts.utc.core.utils.EnvUtils;
import com.aliyun.gts.yunqiao.message.boot.MailProperties;
import com.aliyun.gts.yunqiao.message.request.MailMessageRequest;
import com.aliyun.gts.yunqiao.message.service.TemplateService;
import com.aliyun.gts.yunqiao.message.util.MailSenderServiceUtils;
import com.aliyun.tea.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Service;

import javax.mail.Transport;
import javax.mail.internet.MimeMessage;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class MailMessageSenderService {

    private final Map<String, JavaMailSender> mailSenderMap = new ConcurrentHashMap<>();

    private final Map<String, Boolean> mailSenderAvailabilityMap = new ConcurrentHashMap<>();

    private final TemplateService templateService;

    public MailMessageSenderService() {
        templateService = new TemplateService();
    }

    @Value("${spring.mail.properties.mail.smtp.timeout}")
    private String timeout;
    @Value("${spring.mail.properties.mail.smtp.connectionTimeout}")
    private String connectionTimeout;

    public boolean sendMail(MailMessageSendRequest mailMessageSenderRequest) {
        if (EnvUtils.isMockSendMail()) {
            log.info("send mail message is mocked");
            return true;
        }
        String senderKey = buildMailSenderKey(mailMessageSenderRequest);
        try {
            JavaMailSender javaMailSender = mailSenderMap.get(senderKey);
            if (javaMailSender == null) {
                javaMailSender = new JavaMailSenderImpl();
                MailProperties mailProperties = parseMailProperties(mailMessageSenderRequest);
                MailSenderServiceUtils.applyProperties(javaMailSender, mailProperties);
                mailSenderMap.put(senderKey, javaMailSender);
            }
            MailMessageRequest mailMessageRequest = buildMailMessageRequest(mailMessageSenderRequest);
            templateService.renderTplContent(mailMessageRequest);
            MimeMessage mimeMessage = MailSenderServiceUtils.buildMimeMessage(mailMessageRequest, javaMailSender);
            javaMailSender.send(mimeMessage);
            return true;
        } catch (Exception e) {
            mailSenderMap.remove(senderKey);
            log.error("fail to send mail: {}", senderKey, e);
        }
        return false;
    }

    public JavaMailSender customizeMailSender(MailMessageSendRequest senderRequest) {
        String senderKey = buildMailSenderKey(senderRequest);
        JavaMailSender javaMailSender = mailSenderMap.get(senderKey);
        if (javaMailSender == null) {
            javaMailSender = new JavaMailSenderImpl();
            MailProperties mailProperties = parseMailProperties(senderRequest);
            MailSenderServiceUtils.applyProperties(javaMailSender, mailProperties);
            mailSenderMap.put(senderKey, javaMailSender);
            return javaMailSender;
        }
        return javaMailSender;
    }

    public boolean sendCustomizeMailSenderMail(JavaMailSender customizeMailSender, MailMessageSendRequest mailMessageSenderRequest) {
        String senderKey = buildMailSenderKey(mailMessageSenderRequest);
        //如果sender不可用，直接返回
        if (mailSenderAvailabilityMap.get(senderKey) != null
                && !mailSenderAvailabilityMap.get(senderKey)) {
            return false;
        }
        //邮箱为空直接返回
        if (StringUtils.isEmpty(mailMessageSenderRequest.getEmail())) {
            return false;
        }
        try {
            MailMessageRequest mailMessageRequest = buildMailMessageRequest(mailMessageSenderRequest);
            templateService.renderTplContent(mailMessageRequest);
            MimeMessage mimeMessage = MailSenderServiceUtils.buildMimeMessage(mailMessageRequest, customizeMailSender);
            customizeMailSender.send(mimeMessage);
            return true;
        } catch (Exception e) {
            mailSenderAvailabilityMap.put(senderKey, false);
            mailSenderMap.remove(senderKey);
            log.error("fail to send mail: {}", senderKey, e);
        }
        return false;
    }


    private String buildMailSenderKey(MailMessageSendRequest senderRequest) {
        MailHostPropsDTO mailProps = senderRequest.getMailHostProps();
        return String.format("%s_%s_%s_%s_%s_%s", mailProps.getHost(), mailProps.getPort(), mailProps.getUsername(), mailProps.getPassword(), mailProps.getProtocol(), mailProps.getSslEnable());
    }

    private MailMessageRequest buildMailMessageRequest(MailMessageSendRequest senderRequest) {
        MailMessageRequest mailMessageRequest = new MailMessageRequest();
        mailMessageRequest.setSubject(senderRequest.getSubject());
        mailMessageRequest.setContent(senderRequest.getContent());
//        mailMessageRequest.setFrom(senderRequest.getFrom());
        mailMessageRequest.setFrom(senderRequest.getMailHostProps().getUsername());
        mailMessageRequest.setTo(senderRequest.getEmail());
        mailMessageRequest.setHtml(senderRequest.getHtmlContent());
        mailMessageRequest.getTplVarsMap().putAll(senderRequest.getTplVarsMap());
        mailMessageRequest.setSubmitter(senderRequest.getMailHostProps().getSubmitter());
        return mailMessageRequest;
    }

    private MailProperties parseMailProperties(MailMessageSendRequest senderRequest) {
        MailHostPropsDTO mailProps = senderRequest.getMailHostProps();
        MailProperties mailProperties = new MailProperties();
        mailProperties.setHost(mailProps.getHost());
        mailProperties.setPort(mailProps.getPort());
        mailProperties.setUsername(mailProps.getUsername());
        mailProperties.setPassword(mailProps.getPassword());
        mailProperties.setProtocol(mailProps.getProtocol());
        mailProperties.setSslEnable(mailProps.getSslEnable());
        mailProperties.setTimeout(timeout);
        mailProperties.setConnectionTimeout(connectionTimeout);
        mailProperties.setSubmitter(mailProps.getSubmitter());
        return mailProperties;
    }

    public void removeCustomizeMailSender(MailMessageSendRequest mailMessageSendRequest) {
        String senderKey = buildMailSenderKey(mailMessageSendRequest);
        mailSenderAvailabilityMap.remove(senderKey);
    }
}
