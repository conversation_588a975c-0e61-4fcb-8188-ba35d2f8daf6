package com.aliyun.gts.utc.dal.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.Date;

/**
 * 分页查询消息模板sql连表查询返回
 * <AUTHOR>
 * @date 2021/6/3 10:04
 */
@Data
public class PageMessageTemplateDTO {

    private Long id;

    private String msgTitle;

    private Long categoryId;

    private String categoryName;

    @ApiParam("提交人")
    private String submitName;

    @ApiParam("更改时间, yyyy-MM-dd HH:mm:ss")
    private Date gmtModify;

    @ApiParam("冗余字段，发送类型，渠道id，渠道名称存在此")
    private String features;

    @ApiParam("消息源id")
    private Long sourceId;
}
