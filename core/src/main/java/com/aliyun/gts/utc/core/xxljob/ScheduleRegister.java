package com.aliyun.gts.utc.core.xxljob;

import com.aliyun.gts.utc.core.utils.CronUtils;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class ScheduleRegister {
    @Value("${xxl.job.executor.appname}")
    private String appname;

    @Autowired
    private XxlJobApi xxlJobApi;

    public void addXxlJob(Long msgId, Date date) {
        this.addXxlJob(msgId, CronUtils.convertDateToSingleCron(date),
                RuntimeContextUtil.getUserName());
    }

    public void addXxlJob(Long msgId, String cron, String userName) {
        xxlJobApi.addJob(userName+"创建的消息定时发送", cron, appname, msgId.toString());
    }


}
