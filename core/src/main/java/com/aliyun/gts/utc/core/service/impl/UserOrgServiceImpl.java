package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.request.ummuserorg.PageQueryOrgMembersRequest;
import com.aliyun.gts.utc.api.facade.response.OrgTreeResponse;
import com.aliyun.gts.utc.api.facade.response.ummuserorg.PageQueryOrgMembersResponse;
import com.aliyun.gts.utc.core.service.UserOrgService;
import com.aliyun.gts.utc.spi.OrgServiceSpi;
import com.aliyun.gts.utc.spi.UserServiceSpi;
import com.aliyun.gts.utc.spi.dto.Org;
import com.aliyun.gts.utc.spi.dto.User;
import com.aliyun.gts.utc.spi.request.UserQueryRequest;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserOrgServiceImpl implements UserOrgService {

    private final OrgServiceSpi orgServiceSpi;
    private final UserServiceSpi userServiceSpi;

    private static final int PAGE_SIZE = 20;

    public UserOrgServiceImpl(OrgServiceSpi orgServiceSpi, UserServiceSpi userServiceSpi) {
        this.orgServiceSpi = orgServiceSpi;
        this.userServiceSpi = userServiceSpi;
    }

    /**
     * 获取组织树
     *
     * @param type 类型
     * @return 组织树
     */
    @Override
    public List<OrgTreeResponse> listOrgTree(String type) {
        List<Org> orgs = orgServiceSpi.queryAllOrg();
        return orgs
                .stream().map(UserOrgServiceImpl::convert)
                .collect(Collectors.toList());
    }

    /**
     * 根据组织机构分页查询人员信息接口
     *
     * @param request 请求体
     * @return 人员信息列表
     */
    @Override
    public Page<PageQueryOrgMembersResponse> listOrgMembersByPage(PageQueryOrgMembersRequest request) {
        List<User> users = userServiceSpi.listMembersByOrgCode(request.getOrgGuidList());
        List<User> filteredUsers = users.stream()
                .filter(user -> {
                    if(Strings.isNullOrEmpty(request.getUserCode())) {
                        return true;
                    }
                    if(user != null && !Strings.isNullOrEmpty(user.getUserCode())
                            && user.getUserCode().contains(request.getUserCode())) {
                        return true;
                    }
                    return false;
                }).filter(user -> {
                    if(Strings.isNullOrEmpty(request.getUserName())) {
                        return true;
                    }
                    if(user != null && !Strings.isNullOrEmpty(user.getUserName())
                            && user.getUserName().contains(request.getUserName())) {
                        return true;
                    }
                    return false;
                }).filter(user -> {
                    if(Strings.isNullOrEmpty(request.getEmail())) {
                        return true;
                    }
                    if(user != null && !Strings.isNullOrEmpty(user.getEmail())
                            && user.getEmail().contains(request.getEmail())) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());

        List<PageQueryOrgMembersResponse> dataList =
                filteredUsers.stream().map(i -> {
                    PageQueryOrgMembersResponse r = new PageQueryOrgMembersResponse();
                    r.setEmail(i.getEmail());
                    r.setGuid(i.getUserId());
                    r.setUserCode(i.getUserId());
                    r.setUserName(i.getUserName());
                    r.setNickName(i.getNickName());
                    r.setPhone(i.getPhone());
                    r.setPosName(Collections.singletonList(i.getPosition()));
                    r.setOrgName(Collections.singletonList(i.getOrg()));
                    return r;
                }).collect(Collectors.toList());

        Page<PageQueryOrgMembersResponse> res = new Page<>();
        res.setRecords(dataList);
        res.setTotal(users.size());
        res.setSize(request.getPageSize());
        res.setCurrent(request.getPageNum());
        return res;
    }

    @Override
    public List<OrgTreeResponse> listChildOrgList(String orgGuid) {
        List<Org> orgs = orgServiceSpi.listChildOrgList(orgGuid);
        return orgs.stream().map(UserOrgServiceImpl::convert)
                .collect(Collectors.toList());
    }

    @Override
    public User getUser(String userCode) {
        return userServiceSpi.queryUser(userCode);
    }

    @Override
    public List<User> listUserByCodes(List<String> userCodeList) {
        return userServiceSpi.queryUser(userCodeList);
    }

    @Override
    public List<User> queryUser(String keyword) {
        UserQueryRequest userQueryRequest = new UserQueryRequest();
        if(!Strings.isNullOrEmpty(keyword)) {
            userQueryRequest.setKeyword(keyword);
        }
        userQueryRequest.setPageSize(PAGE_SIZE);
        userQueryRequest.setPageNum(1);
        return userServiceSpi.queryUser(userQueryRequest);
    }


    private static OrgTreeResponse convert(Org org) {
        OrgTreeResponse response = new OrgTreeResponse();
        response.setGuid(org.getGuid());
        response.setOrgCode(org.getOrgCode());
        response.setOrgName(org.getOrgName());
        response.setId(org.getId());
        response.setOrgType("1");
        response.setPId(org.getParentId());
        response.setIsLeaf(org.getIsLeaf());
        List<Org> childOrgList = org.getChildList();
        if (!CollectionUtils.isEmpty(childOrgList)) {
            List<OrgTreeResponse> children =
                    childOrgList.stream().map(UserOrgServiceImpl::convert).collect(Collectors.toList());
            response.setChildList(children);
        }
        return response;
    }
}
