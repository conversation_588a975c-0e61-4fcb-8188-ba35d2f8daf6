/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dataobject;

import com.aliyun.gts.utc.core.dataobject.BaseRelateEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("umm_msg_channel")
public class UmmMsgChannelDO extends BaseRelateEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @TableField("id")
    private Long id;

    /**
     * msg主键id
    */
    @TableField("msg_id")
    private Long msgId;

    /**
    * 消息渠道ID
    */
    @TableField("channel_id")
    private Long channelId;

    /**
    * 生效日期
    */
    @TableField("start_date")
    private Date startDate;

    /**
    * 失效日期
    */
    @TableField("end_date")
    private Date endDate;

    /**
    * 租户编码
    */
    @TableField("tenant_code")
    private String tenantCode;

    /**
    * 应用id
    */
    @TableField("app_id")
    private Long appId;

    /**
    * 1启用 2禁用
    */
    @TableField("status")
    private Integer status;

}