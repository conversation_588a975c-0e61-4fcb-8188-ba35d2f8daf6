/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application;

import com.aliyun.gts.utc.api.facade.request.message.MessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.*;
import com.aliyun.gts.utc.api.facade.response.ummmessage.*;
import com.aliyun.gts.utc.dal.dto.PageMessageDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.validation.constraints.NotNull;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessageManageAppService.java
 * source table name: umm_message
 * initial code lines: 67
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
public interface UmmMessageManageAppService {

    /**
     * 创建UmmMessage
     *
     * @param createUmmMessageRequest 创建$UmmMessage请求
     * @return 创建UmmMessage响应
     */
    CreateUmmMessageResponse createUmmMessage(CreateUmmMessageRequest createUmmMessageRequest);

    /**
     * 根据消息推送id发送消息
     * @param msgId
     * @return
     */
    Boolean msgSend( @NotNull Long msgId);
    /**
     * 查询UmmMessage
     *
     * @param id 查询UmmMessage请求
     * @return 查询UmmMessage响应
     */
    QueryUmmMessageResponse queryUmmMessage(Long id);

    /**
     * 分页查询UmmMessage
     *
     * @param pageQueryUmmMessageRequest 查询UmmMessage请求
     * @return UmmMessage查询结果
     */
    PageQueryUmmMessageResponse pageQueryUmmMessage(PageQueryUmmMessageRequest pageQueryUmmMessageRequest);

    /**
     * 删除UmmMessage
     *
     * @param id 删除UmmMessage请求
     * @return UmmMessage删除结果
     */
    DeleteUmmMessageResponse deleteUmmMessage(Long id);

    /**
     * 删除UmmMessage
     *
     * @param delUmmMessageRequest 删除UmmMessage请求
     * @return UmmMessage删除结果
     */
    DeleteUmmMessageResponse deleteUmmMessageOnBatch(DelUmmMessageRequest delUmmMessageRequest);

    /**
     * 更新UmmMessage
     *
     * @param updateUmmMessageRequest 更新UmmMessage请求
     * @return UmmMessage更新结果
     */
    UpdateUmmMessageResponse updateUmmMessage(UpdateUmmMessageRequest updateUmmMessageRequest);


    /**
     * 分页查询UmmMessage
     *
     * @param pageQueryUmmMessageRequest 查询UmmMessage请求
     * @return UmmMessage查询结果
     */
    Page<PageMessageDTO> pageQueryUmmMessage(PageQueryUmmMessageListRequest pageQueryUmmMessageRequest);

    String messageSend(MessageSendRequest messageSendRequest);
}