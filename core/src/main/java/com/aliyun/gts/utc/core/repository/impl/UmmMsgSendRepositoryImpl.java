package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.message.PageQueryMessageSendResultRequest;
import com.aliyun.gts.utc.common.constants.MessageConstant;
import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import com.aliyun.gts.utc.core.repository.UmmMsgSendRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dao.UmmMsgSendMapper;
import com.aliyun.gts.utc.dal.dao.UmmSourceMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgSendDO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.aliyun.gts.utc.common.constants.StatusConstants.SEND_AWAIT_STATUS;


/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendRepositoryImpl.java
 * source table name: umm_msg_send
 * initial code lines: 176
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Repository
public class UmmMsgSendRepositoryImpl implements UmmMsgSendRepository {

    @Autowired
    private UmmMsgSendMapper ummMsgSendMapper;

    @Override
    public UmmMsgSend getById(Long id) {
        LambdaQueryWrapper<UmmMsgSendDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgSendDO>()
                .eq(UmmMsgSendDO::getId, id)
                .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMsgSendDO ummMsgSendDO = ummMsgSendMapper.selectOne(wrapperSelectDetail);

        return UmmMsgSendDOConverter.INSTANCE.convert(ummMsgSendDO);
    }

    @Override
    public List<UmmMsgSend> getByIds(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            LambdaQueryWrapper<UmmMsgSendDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgSendDO>()
                    .in(UmmMsgSendDO::getId, ids)
                    .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
            List<UmmMsgSendDO> UmmMsgSendDOList = ummMsgSendMapper.selectList(wrapperSelectDetail);
            if (CollectionUtils.isNotEmpty(UmmMsgSendDOList)) {
                return UmmMsgSendDOConverter.INSTANCE.convert(UmmMsgSendDOList);
            }
        }
        return null;
    }

    @Override
    public List<UmmMsgSendDTO> queryUmmMsgSendByMsgId(String msgId) {
        LambdaQueryWrapper<UmmMsgSendDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgSendDO>()
                .eq(UmmMsgSendDO::getMsgId,msgId)
                .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        List<UmmMsgSendDO> UmmMsgSendDO = ummMsgSendMapper.selectList(wrapperSelectDetail);
        List<UmmMsgSendDTO> ummMsgSendDTOS=new ArrayList<>();
        for (UmmMsgSendDO ummMsgSendDO : UmmMsgSendDO) {
            UmmMsgSendDTO ummMsgSendDTO = new UmmMsgSendDTO();
            BeanUtils.copyProperties(ummMsgSendDO, ummMsgSendDTO);
            ummMsgSendDTOS.add(ummMsgSendDTO);
        }
        return ummMsgSendDTOS;
    }

    @Override
    public IPage<UmmMsgSend> getByRange(Long pageNum, Long pageSize,
                                        UmmMsgSend ummMsgSend) {

        Page<UmmMsgSendDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        UmmMsgSendDO ummMsgSendDO =
                UmmMsgSendDOConverter.INSTANCE.convert(ummMsgSend);
        ummMsgSendDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        QueryWrapper<UmmMsgSendDO> queryWrapper = new QueryWrapper();
        queryWrapper.setEntity(ummMsgSendDO);
        IPage<UmmMsgSendDO> iPage = ummMsgSendMapper.selectPage(page,
                queryWrapper);
        List<UmmMsgSend> list = iPage.getRecords().stream()
                .map(UmmMsgSendDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
        Page<UmmMsgSend> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setSize(iPage.getSize());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    @Override
    public UmmMsgSend add(UmmMsgSend ummMsgSend) {

        UmmMsgSendDO ummMsgSendDO = UmmMsgSendDOConverter.INSTANCE.convert(ummMsgSend);
        ummMsgSendDO.setTenantCode(RuntimeContextUtil.getTenantCode());
        ummMsgSendMapper.insert(ummMsgSendDO);
        ummMsgSend.setId(ummMsgSendDO.getId());
        return ummMsgSend;
    }

    @Override
    public Boolean batchAdd(List<UmmMsgSendDTO> ummMsgSendDTOS) {
        ummMsgSendMapper.batchAdd(ummMsgSendDTOS);
        return true;
    }

    @Override
    public Boolean batchUpdate(List<UmmMsgSendDTO> ummMsgSendDTOS) {
        ummMsgSendMapper.batchUpdate(ummMsgSendDTOS);
        return true;
    }

    @Override
    public boolean update(UmmMsgSend ummMsgSend) {

        UmmMsgSendDO ummMsgSendDO = UmmMsgSendDOConverter.INSTANCE.convert(ummMsgSend);
        LambdaQueryWrapper<UmmMsgSendDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgSendDO>()
                .eq(UmmMsgSendDO::getId, ummMsgSendDO.getId())
                .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMsgSendDO newUmmMsgSendDO = ummMsgSendMapper.selectOne(wrapperSelectDetail);

        if (newUmmMsgSendDO == null) {
            return false;
        }
        if (ummMsgSendDO.getGuid() != null) {
            newUmmMsgSendDO.setGuid(ummMsgSendDO.getGuid());
        }
        if (ummMsgSendDO.getUserGuid() != null) {
            newUmmMsgSendDO.setUserGuid(ummMsgSendDO.getUserGuid());
        }
        if (ummMsgSendDO.getUserCode() != null) {
            newUmmMsgSendDO.setUserCode(ummMsgSendDO.getUserCode());
        }
        if (ummMsgSendDO.getUserName() != null) {
            newUmmMsgSendDO.setUserName(ummMsgSendDO.getUserName());
        }
        if (ummMsgSendDO.getUserOrg() != null) {
            newUmmMsgSendDO.setUserOrg(ummMsgSendDO.getUserOrg());
        }
        if (ummMsgSendDO.getUserPostion() != null) {
            newUmmMsgSendDO.setUserPostion(ummMsgSendDO.getUserPostion());
        }
        if (ummMsgSendDO.getEmail() != null) {
            newUmmMsgSendDO.setEmail(ummMsgSendDO.getEmail());
        }
        if (ummMsgSendDO.getPhone() != null) {
            newUmmMsgSendDO.setPhone(ummMsgSendDO.getPhone());
        }
        if (ummMsgSendDO.getMsgId() != null) {
            newUmmMsgSendDO.setMsgId(ummMsgSendDO.getMsgId());
        }
        if (ummMsgSendDO.getMsgTitle() != null) {
            newUmmMsgSendDO.setMsgTitle(ummMsgSendDO.getMsgTitle());
        }
        if (ummMsgSendDO.getMsgContent() != null) {
            newUmmMsgSendDO.setMsgContent(ummMsgSendDO.getMsgContent());
        }
        if (ummMsgSendDO.getChannelId() != null) {
            newUmmMsgSendDO.setChannelId(ummMsgSendDO.getChannelId());
        }
        if (ummMsgSendDO.getSendTime() != null) {
            newUmmMsgSendDO.setSendTime(ummMsgSendDO.getSendTime());
        }
        if (ummMsgSendDO.getStatus() != null) {
            newUmmMsgSendDO.setStatus(ummMsgSendDO.getStatus());
        }
        if (ummMsgSendDO.getReadStatus() != null) {
            newUmmMsgSendDO.setReadStatus(ummMsgSendDO.getReadStatus());
        }
        if (ummMsgSendDO.getReadTime() != null) {
            newUmmMsgSendDO.setReadTime(ummMsgSendDO.getReadTime());
        }
        if (ummMsgSendDO.getCreateUserGuid() != null) {
            newUmmMsgSendDO.setCreateUserGuid(ummMsgSendDO.getCreateUserGuid());
        }
        if (ummMsgSendDO.getCreateUserName() != null) {
            newUmmMsgSendDO.setCreateUserName(ummMsgSendDO.getCreateUserName());
        }
        if (ummMsgSendDO.getUpdateUserGuid() != null) {
            newUmmMsgSendDO.setUpdateUserGuid(ummMsgSendDO.getUpdateUserGuid());
        }
        if (ummMsgSendDO.getUpdateUserName() != null) {
            newUmmMsgSendDO.setUpdateUserName(ummMsgSendDO.getUpdateUserName());
        }
        if (ummMsgSendDO.getGmtCreate() != null) {
            newUmmMsgSendDO.setGmtCreate(ummMsgSendDO.getGmtCreate());
        }
        if (ummMsgSendDO.getGmtModify() != null) {
            newUmmMsgSendDO.setGmtModify(ummMsgSendDO.getGmtModify());
        }
        if (ummMsgSendDO.getFeatures() != null) {
            newUmmMsgSendDO.setFeatures(ummMsgSendDO.getFeatures());
        }
        if (ummMsgSendDO.getIsDeleted() != null) {
            newUmmMsgSendDO.setIsDeleted(ummMsgSendDO.getIsDeleted());
        }
        if (ummMsgSendDO.getVersion() != null) {
            newUmmMsgSendDO.setVersion(ummMsgSendDO.getVersion());
        }
        if (ummMsgSendDO.getAppId() != null) {
            newUmmMsgSendDO.setAppId(ummMsgSendDO.getAppId());
        }
        if (ummMsgSendDO.getRetryRule() != null) {
            newUmmMsgSendDO.setRetryRule(ummMsgSendDO.getRetryRule());
        }
        if (ummMsgSendDO.getRetryRule() != null) {
            newUmmMsgSendDO.setRetryRule(ummMsgSendDO.getRetryRule());
        }

        ummMsgSendMapper.updateById(newUmmMsgSendDO);
        return true;
    }

    @Override
    public boolean batchUpdateUmmMsgSendStatus(List<Long> sendMsgIds,Integer status) {
        UmmMsgSendDO ummMsgSendDO = new UmmMsgSendDO();
        ummMsgSendDO.setStatus(status);
        ummMsgSendDO.setGmtModify(new Date());
        Wrapper<UmmMsgSendDO> wrapper = new LambdaUpdateWrapper<UmmMsgSendDO>()
                .in(CollectionUtils.isNotEmpty(sendMsgIds),
                        UmmMsgSendDO::getId, sendMsgIds)
                .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        ummMsgSendMapper.update(ummMsgSendDO, wrapper);
        return true;
    }

    @Override
    public boolean deleteById(Long id) {
        LambdaQueryWrapper<UmmMsgSendDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgSendDO>()
                .eq(UmmMsgSendDO::getId, id)
                .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMsgSendDO ummMsgSendDO = ummMsgSendMapper.selectOne(wrapperSelectDetail);
        if (ummMsgSendDO == null) {
            return false;
        }
        return ummMsgSendMapper.deleteById(id) > 0;
    }

    /**
     * 分页查询消息发送明细
     *
     * @param request 请求体
     * @return 消息发送明细分页结果
     */
    @Override
    public Page<UmmMsgSend> resultPageQuery(PageQueryMessageSendResultRequest request) {
        Page<UmmMsgSendDO> page = new Page<>(request.getPageNum(),
                request.getPageSize());
        LambdaQueryWrapper<UmmMsgSendDO> queryWrapper =
                new QueryWrapper<UmmMsgSendDO>().lambda()
                        .eq(UmmMsgSendDO::getIsDeleted,
                                MessageConstant.NOT_DELETED)
                        .eq(UmmMsgSendDO::getMsgId, request.getMsgId())
                        .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        if (StringUtils.isNotEmpty(request.getNameQuery())) {
            queryWrapper.like(UmmMsgSendDO::getUserName,
                    request.getNameQuery());
        }
        if (StringUtils.isNotEmpty(request.getAccountQuery())) {
            queryWrapper.like(UmmMsgSendDO::getUserCode,
                    request.getAccountQuery() );
        }
        if (StringUtils.isNotEmpty(request.getEmailQuery())) {
            queryWrapper.like(UmmMsgSendDO::getEmail,
                    request.getEmailQuery());
        }
        if (StringUtils.isNotEmpty(request.getPhoneQuery())) {
            queryWrapper.like(UmmMsgSendDO::getPhone,
                    request.getPhoneQuery());
        }
        if (StringUtils.isNotEmpty(request.getOrgQuery())) {
            queryWrapper.like(UmmMsgSendDO::getUserOrg,
                    request.getOrgQuery());
        }
        if (StringUtils.isNotEmpty(request.getOrderField())) {
            switch (request.getOrderField()) {
                case MessageConstant.ACCOUNT:
                    if (null == request.getIsAsc() || !request.getIsAsc()) {
                        queryWrapper.orderByDesc(UmmMsgSendDO::getUserCode);
                    } else {
                        queryWrapper.orderByAsc(UmmMsgSendDO::getUserCode);
                    }
                    break;
                case MessageConstant.NAME:
                    if (null == request.getIsAsc() || !request.getIsAsc()) {
                        queryWrapper.orderByDesc(UmmMsgSendDO::getUserName);
                    } else {
                        queryWrapper.orderByAsc(UmmMsgSendDO::getUserName);
                    }
                    break;
                case MessageConstant.PHONE:
                    if (null == request.getIsAsc() || !request.getIsAsc()) {
                        queryWrapper.orderByDesc(UmmMsgSendDO::getPhone);
                    } else {
                        queryWrapper.orderByAsc(UmmMsgSendDO::getPhone);
                    }
                    break;
                case MessageConstant.POSITION:
                    if (null == request.getIsAsc() || !request.getIsAsc()) {
                        queryWrapper.orderByDesc(UmmMsgSendDO::getUserPostion);
                    } else {
                        queryWrapper.orderByAsc(UmmMsgSendDO::getUserPostion);
                    }
                    break;
                case MessageConstant.SEND_STATUS:
                    if (null == request.getIsAsc() || !request.getIsAsc()) {
                        queryWrapper.orderByDesc(UmmMsgSendDO::getStatus);
                    } else {
                        queryWrapper.orderByAsc(UmmMsgSendDO::getStatus);
                    }
                    break;
                case MessageConstant.SEND_TIME:
                    if (null == request.getIsAsc() || !request.getIsAsc()) {
                        queryWrapper.orderByDesc(UmmMsgSendDO::getSendTime);
                    } else {
                        queryWrapper.orderByAsc(UmmMsgSendDO::getSendTime);
                    }
                    break;
                default:
                    break;
            }
        } else {
            queryWrapper.orderByAsc(UmmMsgSendDO::getUserName);
        }
        if (null != request.getSendStatus()) {
            queryWrapper.eq(UmmMsgSendDO::getStatus, request.getSendStatus());
        }
        if (null != request.getReadStatus()) {
            queryWrapper.eq(UmmMsgSendDO::getReadStatus,
                    request.getReadStatus());
        }
        IPage<UmmMsgSendDO> iPage = ummMsgSendMapper.selectPage(page,
                queryWrapper);
        List<UmmMsgSend> list = iPage.getRecords().stream()
                .map(UmmMsgSendDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
        Page<UmmMsgSend> newPage = new Page<>();
        newPage.setSize(iPage.getSize());
        newPage.setCurrent(iPage.getCurrent());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    /**
     * 获取该消息发送成功数量
     *
     * @param msgId 消息id
     * @return 该消息发送成功数量
     */
    @Override
    public int getSendSuccessCount(Long msgId) {
        LambdaQueryWrapper<UmmMsgSendDO> queryWrapper =
                new QueryWrapper<UmmMsgSendDO>()
                        .lambda().eq(UmmMsgSendDO::getMsgId, msgId)
                        .eq(UmmMsgSendDO::getStatus, 3)
                        .eq(UmmMsgSendDO::getIsDeleted,
                                MessageConstant.NOT_DELETED)
                        .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        return Math.toIntExact(ummMsgSendMapper.selectCount(queryWrapper));
    }

    /**
     * 获取该消息等待数量
     *
     * @param msgId 消息id
     * @return 该消息等待数量
     */
    @Override
    public int getSendAwaitCount(Long msgId) {
        LambdaQueryWrapper<UmmMsgSendDO> queryWrapper =
                new QueryWrapper<UmmMsgSendDO>()
                        .lambda().eq(UmmMsgSendDO::getMsgId, msgId)
                        .eq(UmmMsgSendDO::getStatus, SEND_AWAIT_STATUS)
                        .eq(UmmMsgSendDO::getIsDeleted,
                                MessageConstant.NOT_DELETED)
                        .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        return Math.toIntExact(ummMsgSendMapper.selectCount(queryWrapper));
    }

    /**
     * 获取该消息发送总数量
     *
     * @param msgId 消息id
     * @return 该消息发送总数量
     */
    @Override
    public int getSendCount(Long msgId) {
        LambdaQueryWrapper<UmmMsgSendDO> queryWrapper =
                new QueryWrapper<UmmMsgSendDO>()
                        .lambda().eq(UmmMsgSendDO::getMsgId, msgId)
                        .eq(UmmMsgSendDO::getIsDeleted,
                                MessageConstant.NOT_DELETED)
                        .eq(UmmMsgSendDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        return Math.toIntExact(ummMsgSendMapper.selectCount(queryWrapper));
    }

    @Mapper
    interface UmmMsgSendDOConverter {
        UmmMsgSendDOConverter INSTANCE =
                Mappers.getMapper(UmmMsgSendDOConverter.class);

        UmmMsgSendDO convert(UmmMsgSend ummMsgSend);

        UmmMsgSend convert(UmmMsgSendDO ummMsgSendDO);

        List<UmmMsgSend> convert(List<UmmMsgSendDO> ummMsgSendDO);
    }

}
