package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMessageDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.CreateUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.PageQueryUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.UpdateUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.*;
import com.aliyun.gts.utc.core.domain.UmmMessage;
import com.aliyun.gts.utc.core.repository.UmmMessageRepository;
import com.aliyun.gts.utc.core.service.UmmMessageManageCoreService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.stream.Collectors;

@Service
public class UmmMessageManageCoreServiceImpl implements UmmMessageManageCoreService {
    private final UmmMessageRepository ummMessageRepository;

    public UmmMessageManageCoreServiceImpl(UmmMessageRepository ummMessageRepository) {
        this.ummMessageRepository = ummMessageRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmMessageResponse createUmmMessage(CreateUmmMessageRequest createUmmMessageRequest) {

        UmmMessageDTO ummMessageDTO =
                createUmmMessageRequest.getUmmMessageDTO();

        UmmMessage ummMessage =
                UmmMessageDTOConverter.INSTANCE.convert(ummMessageDTO);
        UmmMessage add = ummMessageRepository.add(ummMessage);
        ummMessageDTO.setId(add.getId());

        CreateUmmMessageResponse response = new CreateUmmMessageResponse();
        response.setUmmMessageDTO(ummMessageDTO);

        return response;
    }

    @Override
    public QueryUmmMessageResponse queryUmmMessage(Long id) {

        UmmMessage ummMessage = ummMessageRepository.getById(id);
        UmmMessageDTO ummMessageDTO = UmmMessageDTOConverter.INSTANCE.convert(ummMessage);
        QueryUmmMessageResponse response = new QueryUmmMessageResponse();
        response.setUmmMessageDTO(ummMessageDTO);
        return response;
    }

    @Override
    public QueryUmmMessageResponse queryUmmMessageByGuid(String guid) {
        UmmMessage ummMessage = ummMessageRepository.getByGuid(guid);
        UmmMessageDTO ummMessageDTO = UmmMessageDTOConverter.INSTANCE.convert(ummMessage);
        QueryUmmMessageResponse response = new QueryUmmMessageResponse();
        response.setUmmMessageDTO(ummMessageDTO);
        return response;
    }

    @Override
    public PageQueryUmmMessageResponse pageQueryUmmMessage(PageQueryUmmMessageRequest request) {

        UmmMessageDTO ummMessageDTO = request.getUmmMessageDTO();
        UmmMessage ummMessage = UmmMessageDTOConverter.INSTANCE.convert(ummMessageDTO);
        IPage<UmmMessage> byRange = ummMessageRepository.getByRange(request.getPageNum(), request.getPageSize(), ummMessage);

        PageQueryUmmMessageResponse response = new PageQueryUmmMessageResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
                .map(UmmMessageDTOConverter.INSTANCE::convert)
                .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmMessageResponse deleteUmmMessage(Long id) {
        DeleteUmmMessageResponse deleteUmmMessageResponse = new DeleteUmmMessageResponse();
        deleteUmmMessageResponse.setIsDelete(ummMessageRepository.deleteById(id));
        return deleteUmmMessageResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long msgId, Integer successNum) {
        return ummMessageRepository.updateStatus(msgId, successNum);
    }

    @Override
    public UpdateUmmMessageResponse updateUmmMessage(UpdateUmmMessageRequest request) {

        UmmMessageDTO ummMessageDTO = request.getUmmMessageDTO();

        UmmMessage ummMessage = UmmMessageDTOConverter.INSTANCE.convert(ummMessageDTO);
        ummMessage.setGmtModify(new Date());
        UpdateUmmMessageResponse updateUmmMessageResponse = new UpdateUmmMessageResponse();
        updateUmmMessageResponse.setIsUpdate(ummMessageRepository.update(ummMessage));
        return updateUmmMessageResponse;
    }

    @Mapper
    interface UmmMessageDTOConverter {
        UmmMessageDTOConverter INSTANCE = Mappers.getMapper(UmmMessageDTOConverter.class);

        UmmMessage convert(UmmMessageDTO ummMessageDTO);

        UmmMessageDTO convert(UmmMessage ummMessage);
    }

}
