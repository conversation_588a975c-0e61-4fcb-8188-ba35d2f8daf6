package com.aliyun.gts.utc.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FileUploadCheckUtil {

    private final static String REGEX="../";

    public static Boolean checkFileName(String fileName){
        Pattern p= Pattern.compile(REGEX);
        Matcher m= p.matcher(fileName);
        if (m.find()){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}
