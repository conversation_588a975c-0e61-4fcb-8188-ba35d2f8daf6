package com.aliyun.gts.utc.integration.client.alisms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Map;


@Slf4j
public class AliSmsClientImpl implements SmsClient {


    /**
     * ak
     */
    private final String accessKeyId;
    /**
     * sk
     */
    private final String accessKeySecret;

    /**
     * 签名
     */
    private final String signName;

    private final String endpoint = "dysmsapi.aliyuncs.com";


    public AliSmsClientImpl(String accessKeyId, String accessKeySecret,
                            String signName) {
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.signName = signName;
    }

    @Override
    public void sendSms(String templateCode, Map<String, String> extParams,
                        String phoneNum) throws Exception {
        Assert.isTrue(StringUtils.hasLength(templateCode), "templateCode is " +
                "blank");
        Assert.isTrue(StringUtils.hasLength(phoneNum), "phoneNums is blank");
        com.aliyun.dysmsapi20170525.Client client = createClient(
                accessKeyId, accessKeySecret);
        SendSmsRequest sendSmsRequest = new SendSmsRequest();
        sendSmsRequest.setPhoneNumbers(phoneNum);
        sendSmsRequest.setTemplateCode(templateCode);
        JSONObject jsonObject = new JSONObject();
        jsonObject.putAll(extParams);
        sendSmsRequest.setTemplateParam(jsonObject.toJSONString());
        sendSmsRequest.setSignName(signName);
        // 复制代码运行请自行打印 API 的返回值
        log.info("send sms to " + JSON.toJSONString(sendSmsRequest));
        SendSmsResponse response = client.sendSms(sendSmsRequest);
        String code = response.getBody().getCode();
        Assert.isTrue("OK".equalsIgnoreCase(code),
                "send sms failed!" + JSON.toJSONString(response));
    }

    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public com.aliyun.dysmsapi20170525.Client createClient(String accessKeyId
            , String accessKeySecret) throws Exception {
        Config config = new Config()
                // 您的AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 您的AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = endpoint;
        return new com.aliyun.dysmsapi20170525.Client(config);
    }
}
