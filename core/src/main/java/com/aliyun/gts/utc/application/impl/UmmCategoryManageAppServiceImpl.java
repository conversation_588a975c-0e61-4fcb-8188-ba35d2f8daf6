package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.AllUmmCategoryDTO;
import com.aliyun.gts.utc.api.dto.UmmCategoryDTO;
import com.aliyun.gts.utc.api.error.ErrorCodeEnum;
import com.aliyun.gts.utc.api.facade.request.ummcategory.CreateUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.request.ummcategory.PageQueryUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.request.ummcategory.UpdateUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.response.ummcategory.*;
import com.aliyun.gts.utc.application.UmmCategoryManageAppService;
import com.aliyun.gts.utc.core.constants.StatusEnum;
import com.aliyun.gts.utc.core.domain.UmmCategory;
import com.aliyun.gts.utc.core.domain.UmmMessage;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.exceptions.BizException;
import com.aliyun.gts.utc.core.repository.UmmCategoryRepository;
import com.aliyun.gts.utc.core.repository.UmmMessageRepository;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmCategoryManageAppServiceImpl.java
 * source table name: umm_category
 * initial code lines: 113
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Service
public class UmmCategoryManageAppServiceImpl implements UmmCategoryManageAppService {
    @Autowired
    private UmmCategoryRepository ummCategoryRepository;
    @Autowired
    private UmmTemplateRepository ummTemplateRepository;
    @Autowired
    private UmmMessageRepository ummMessageRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmCategoryResponse createUmmCategory(CreateUmmCategoryRequest createUmmCategoryRequest) {
        UmmCategoryDTO ummCategoryDTO = createUmmCategoryRequest.getUmmCategoryDTO();
        if (StringUtils.isBlank(ummCategoryDTO.getCategoryCode())){
            ummCategoryDTO.setCategoryCode(System.currentTimeMillis()+"");
        }
        this.checkNameExist(ummCategoryDTO.getCategoryName());
        this.checkCodeExist(ummCategoryDTO.getCategoryCode());
        UmmCategory ummCategory = UmmCategoryDTOConverter.INSTANCE.convert(ummCategoryDTO);
        ummCategory.setCreateUserGuid(RuntimeContextUtil.getUserGuid());
        ummCategory.setCreateUserName(RuntimeContextUtil.getUserName());
        ummCategory.setGmtCreate(new Date());
        ummCategory.setGuid(UUID.randomUUID().toString().replace("-", ""));
        UmmCategory add = ummCategoryRepository.add(ummCategory);
        ummCategoryDTO.setId(add.getId());

        CreateUmmCategoryResponse response = new CreateUmmCategoryResponse();
        response.setUmmCategoryDTO(ummCategoryDTO);

        return response;
    }

    @Override
    public QueryUmmCategoryResponse queryUmmCategory(Long id) {
        ummCategoryRepository.checkCategoryExistInCurrentTenantCode(id);
        UmmCategory ummCategory = ummCategoryRepository.getById(id);
        UmmCategoryDTO ummCategoryDTO = UmmCategoryDTOConverter.INSTANCE.convert(ummCategory);
        QueryUmmCategoryResponse response = new QueryUmmCategoryResponse();
        response.setUmmCategoryDTO(ummCategoryDTO);
        return response;
    }

    @Override
    public PageQueryUmmCategoryResponse pageQueryUmmCategory(PageQueryUmmCategoryRequest request) {

        UmmCategoryDTO ummCategoryDTO = request.getUmmCategoryDTO();
        if (Objects.isNull(request.getPageNum()) || Objects.isNull(request.getPageSize())) {
            request.setPageNum(1L);
            request.setPageSize(1000L);
        }
        if (Objects.isNull(ummCategoryDTO.getStatus())) {
            ummCategoryDTO.setStatus(1);
        }
        UmmCategory ummCategory = UmmCategoryDTOConverter.INSTANCE.convert(ummCategoryDTO);
        IPage<UmmCategory> byRange = ummCategoryRepository.getByRange(request.getPageNum(), request.getPageSize(), ummCategory);

        PageQueryUmmCategoryResponse response = new PageQueryUmmCategoryResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
                .map(UmmCategoryDTOConverter.INSTANCE::convert)
                .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmCategoryResponse deleteUmmCategory(Long id) {
        ummCategoryRepository.checkCategoryExistInCurrentTenantCode(id);
        //确认是否有子节点
        List<UmmCategory> childs = ummCategoryRepository.getByParentId(id);
        if (CollectionUtils.isNotEmpty(childs)) {
            throw new RuntimeException("存在子节点，不能删除");
        }
        //确认是否存在模板
        UmmTemplate templateConditional = new UmmTemplate();
        templateConditional.setCategoryId(id);
        IPage<UmmTemplate> byRange = ummTemplateRepository.getByRange(1, 1,
                templateConditional);
        if (CollectionUtils.isNotEmpty(byRange.getRecords())) {
            throw new RuntimeException("栏目下存在模板，不能删除");
        }
        //确认栏目下是否有消息
        UmmMessage ummMessage = new UmmMessage();
        ummMessage.setCategoryId(id);
        IPage<UmmMessage> byRange1 = ummMessageRepository.getByRange(1L, 1L, ummMessage);
        if (CollectionUtils.isNotEmpty(byRange1.getRecords())) {
            throw new RuntimeException("栏目下存在消息，不能删除");
        }
        DeleteUmmCategoryResponse deleteUmmCategoryResponse = new DeleteUmmCategoryResponse();
        deleteUmmCategoryResponse.setIsDelete(ummCategoryRepository.deleteById(id));
        return deleteUmmCategoryResponse;
    }

    @Override
    public UpdateUmmCategoryResponse updateUmmCategory(UpdateUmmCategoryRequest request) {

        UmmCategoryDTO ummCategoryDTO = request.getUmmCategoryDTO();
        ummCategoryRepository.checkCategoryExistInCurrentTenantCode(ummCategoryDTO.getId());

        UmmCategory ummCategory = UmmCategoryDTOConverter.INSTANCE.convert(ummCategoryDTO);
        ummCategory.setUpdateUserGuid(RuntimeContextUtil.getUserGuid());
        ummCategory.setUpdateUserName(RuntimeContextUtil.getUserName());
        ummCategory.setGmtModify(new Date());
        UpdateUmmCategoryResponse updateUmmCategoryResponse = new UpdateUmmCategoryResponse();
        updateUmmCategoryResponse.setIsUpdate(ummCategoryRepository.update(ummCategory));
        return updateUmmCategoryResponse;
    }

    @Override
    public AllUmmCategoryResponse listAll() {
        AllUmmCategoryResponse allUmmCategoryResponse = new AllUmmCategoryResponse();
        AllUmmCategoryDTO allUmmCategoryDTO = new AllUmmCategoryDTO();
        allUmmCategoryDTO.setId(0L);
        this.getAndSetChild(allUmmCategoryDTO);
        allUmmCategoryDTO.setCategoryName("全部");
        allUmmCategoryResponse.setAllUmmCategoryDTO(allUmmCategoryDTO);
        return allUmmCategoryResponse;
    }

    @Mapper
    interface UmmCategoryDTOConverter {
        UmmCategoryDTOConverter INSTANCE = Mappers.getMapper(UmmCategoryDTOConverter.class);

        UmmCategory convert(UmmCategoryDTO ummCategoryDTO);

        UmmCategoryDTO convert(UmmCategory ummCategory);

        UmmCategory convert(AllUmmCategoryDTO allUmmCategoryDTO);

    }

    private void getAndSetChild(AllUmmCategoryDTO allUmmCategoryDTO) {
        List<UmmCategory> childs = ummCategoryRepository.getByParentId(allUmmCategoryDTO.getId());
        if (CollectionUtils.isEmpty(childs)) {
            return;
        }
        List<AllUmmCategoryDTO> collect = childs.stream().filter(ummCategory -> ummCategory.getStatus() == StatusEnum.ENABLE.getState()).map(tmp -> {
            AllUmmCategoryDTO allUmmCategory = new AllUmmCategoryDTO();
            BeanUtils.copyProperties(tmp, allUmmCategory);
            return allUmmCategory;
        }).collect(Collectors.toList());
        allUmmCategoryDTO.setChild(collect);
        for (AllUmmCategoryDTO ummCategoryDTO : allUmmCategoryDTO.getChild()) {
            this.getAndSetChild(ummCategoryDTO);
        }

    }

    /**
     * 校验id深度
     *
     * @param id
     * @return
     */
    private void checkDepth(Long id) {
        int depth = this.getDepthByParentId(id);
        if (depth > 1) {
            throw new RuntimeException("目录深度已经最大");
        }
    }

    private int getDepthByParentId(Long id) {
        AllUmmCategoryResponse allUmmCategoryResponse = this.listAll();
        int depth = 0;
        this.getDepth(allUmmCategoryResponse.getAllUmmCategoryDTO(), id, depth);
        return depth;
    }

    /**
     * 计算id所在深度
     */
    private int getDepth(AllUmmCategoryDTO allUmmCategoryDTO, Long parentId, int depth) {
        if (Objects.isNull(allUmmCategoryDTO) || Objects.isNull(parentId)) {
            throw new RuntimeException("父级栏目信息不存在");
        }
        List<AllUmmCategoryDTO> child = allUmmCategoryDTO.getChild();
        if (CollectionUtils.isNotEmpty(child)) {
            for (AllUmmCategoryDTO ummCategoryDTO : child) {
                if (ummCategoryDTO.getId().equals(parentId)) {
                    return depth;
                } else {
                    depth++;
                    this.getDepth(allUmmCategoryDTO, parentId, depth);
                }
            }
        }
        return 0;
    }


    private void checkNameExist(String cateGoryName) {
        UmmCategory nameQuery = new UmmCategory();
        nameQuery.setCategoryName(cateGoryName);
        IPage<UmmCategory> byRange = ummCategoryRepository.getByRange(1, 1,
                nameQuery);
        if (CollectionUtils.isNotEmpty(byRange.getRecords())) {
            throw new RuntimeException("栏目名称已存在");
        }
    }

    private void checkCodeExist(String cateGoryCode) {
        UmmCategory nameQuery = new UmmCategory();
        nameQuery.setCategoryCode(cateGoryCode);
        IPage<UmmCategory> byRange = ummCategoryRepository.getByRange(1, 1, nameQuery);
        if (CollectionUtils.isNotEmpty(byRange.getRecords())) {
            throw new RuntimeException("栏目编码已存在");
        }
    }


}