package com.aliyun.gts.utc.integration.client.foura;

import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.aliyun.gts.utc.integration.IntegrationTemplate;
import com.google.common.collect.Lists;
import com.yunqiao.foura.service.api.IClientForOrg;
import com.yunqiao.foura.service.api.IClientForResource;
import com.yunqiao.foura.service.api.IClientForRole;
import com.yunqiao.foura.service.api.IClientForUser;
import com.yunqiao.foura.service.model.OrganizationInfoDTO;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.model.user.UserSimpleInfoDTO;
import com.yunqiao.foura.service.request.user.UserConditionDTO;
import com.yunqiao.foura.service.response.BaseResult;
import com.yunqiao.foura.service.response.user.UserSimplePageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: yaohao.yh
 * @date: 2023/9/10
 */
@Service
public class UserCenterClient {

    @Value("${foura.appId:54}")
    private String fouraAppId;

    @Autowired
    private IntegrationTemplate invokeTemplate;

    @Autowired
    private IClientForOrg iClientForOrg;

    @Autowired
    private IClientForUser iClientForUser;

    @Autowired
    private IClientForRole iClientForRole;

    @Autowired
    private IClientForResource iClientForResource;

    /**
     * 按照id查询用户名称
     *
     * @param userId
     * @return
     */
    public UserInfoDTO getUserDetail(String userId) {
        return invokeTemplate.execute(() -> {
            BaseResult<UserInfoDTO> b = iClientForUser.listUserDetail(userId);
            if (!"200".equals(b.getStatusCode())) {
                return ResultResponse.errorResult(b.getStatusInfo());
            }
            UserInfoDTO u = b.getData();
            return ResultResponse.succResult(u);
        });
    }

    public UserSimplePageResponse listUsersByCondition(UserConditionDTO userConditionDTO) {
        return invokeTemplate.execute(() -> {
            BaseResult<UserSimplePageResponse> b = iClientForUser.listUsersByCondition(userConditionDTO);
            if (!"200".equals(b.getStatusCode())) {
                return ResultResponse.errorResult(b.getStatusInfo());
            }
            UserSimplePageResponse u = b.getData();
            return ResultResponse.succResult(u);
        });
    }

    /**
     * 获取所有组织机构目录树
     *
     * @return
     */
    public List<OrganizationInfoDTO> listRootOrgs() {
        return invokeTemplate.execute(() -> {
            BaseResult<List<OrganizationInfoDTO>> b = iClientForOrg.getOrgTree();
            if (!"200".equals(b.getStatusCode())) {
                return ResultResponse.errorResult(b.getStatusInfo());
            }
            List<OrganizationInfoDTO> orgTree = b.getData();
            return ResultResponse.succResult(orgTree);
        });
    }

    /**
     * 按照parentId查询子组织
     * @param parentId
     * @return
     */
    public List<OrganizationInfoDTO> listOrgsByParentId(String parentId) {
        return invokeTemplate.execute(() ->{
            BaseResult<List<OrganizationInfoDTO>> b = iClientForOrg.getOrgInfoByParentCode(parentId);
            if (!"200".equals(b.getStatusCode())) {
                return ResultResponse.errorResult(b.getStatusInfo());
            }
            List<OrganizationInfoDTO> childOrgTree = b.getData();
            if (childOrgTree == null) {
                return ResultResponse.succResult(Lists.newArrayList());
            }

            return ResultResponse.succResult(childOrgTree);
        });
    }


    public List<UserSimpleInfoDTO> listMembersByOrgCode(String orgCode) {
        return invokeTemplate.execute(() ->{
            BaseResult<List<UserSimpleInfoDTO>> b = iClientForOrg.getMembersByOrgId(orgCode);
            if (!"200".equals(b.getStatusCode())) {
                return ResultResponse.errorResult(b.getStatusInfo());
            }
            List<UserSimpleInfoDTO> members = b.getData();
            if (members == null) {
                return ResultResponse.succResult(Lists.newArrayList());
            }

            return ResultResponse.succResult(members);
        });
    }
}