/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application;

import com.aliyun.gts.utc.api.facade.request.ummmsgparameter.CreateUmmMsgParameterRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgparameter.PageQueryUmmMsgParameterRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgparameter.UpdateUmmMsgParameterRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgparameter.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgParameterManageAppService.java
 * source table name: umm_msg_parameter
 * initial code lines: 67
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
public interface UmmMsgParameterManageAppService {

    /**
     * 创建UmmMsgParameter
     *
     * @param createUmmMsgParameterRequest 创建$UmmMsgParameter请求
     * @return 创建UmmMsgParameter响应
     */
    CreateUmmMsgParameterResponse createUmmMsgParameter(CreateUmmMsgParameterRequest createUmmMsgParameterRequest);

    /**
     * 查询UmmMsgParameter
     *
     * @param id 查询UmmMsgParameter请求
     * @return 查询UmmMsgParameter响应
     */
    QueryUmmMsgParameterResponse queryUmmMsgParameter(Long id);

    /**
     * 分页查询UmmMsgParameter
     *
     * @param pageQueryUmmMsgParameterRequest 查询UmmMsgParameter请求
     * @return UmmMsgParameter查询结果
     */
    PageQueryUmmMsgParameterResponse pageQueryUmmMsgParameter(PageQueryUmmMsgParameterRequest pageQueryUmmMsgParameterRequest);

    /**
     * 删除UmmMsgParameter
     *
     * @param id 删除UmmMsgParameter请求
     * @return UmmMsgParameter删除结果
     */
    DeleteUmmMsgParameterResponse deleteUmmMsgParameter(Long id);

    /**
     * 更新UmmMsgParameter
     *
     * @param updateUmmMsgParameterRequest 更新UmmMsgParameter请求
     * @return UmmMsgParameter更新结果
     */
    UpdateUmmMsgParameterResponse updateUmmMsgParameter(UpdateUmmMsgParameterRequest updateUmmMsgParameterRequest);

}