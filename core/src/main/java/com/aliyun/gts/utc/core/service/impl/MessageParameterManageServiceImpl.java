package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.request.parameter.PageQueryMessageParameterRequest;
import com.aliyun.gts.utc.api.facade.response.parameter.PageQueryMessageParameterResponse;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import com.aliyun.gts.utc.core.factory.MessageParameterFactory;
import com.aliyun.gts.utc.core.repository.UmmMsgParameterRepository;
import com.aliyun.gts.utc.core.service.MessageParameterManageService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.stream.Collectors;


@Service
public class MessageParameterManageServiceImpl implements MessageParameterManageService {

    @Resource
    private UmmMsgParameterRepository ummMsgParameterRepository;


    public MessageParameterManageServiceImpl(UmmMsgParameterRepository ummMsgParameterRepository) {
        this.ummMsgParameterRepository = ummMsgParameterRepository;
    }


    /**
     * 分页查询消息参数列表
     *
     * @param request 请求体
     * @return 分页后的消息参数列表
     */
    @Override
    public Page<PageQueryMessageParameterResponse> pageQuery(PageQueryMessageParameterRequest request) {
        IPage<UmmMsgParameter> byRange = ummMsgParameterRepository.getPageQuery(request.getPageNum().intValue(), request.getPageSize().intValue(), request);

        Page<PageQueryMessageParameterResponse> response = new Page<>();
        response.setTotal(byRange.getTotal());
        response.setCurrent(byRange.getCurrent());
        response.setSize(byRange.getSize());

        response.setRecords(byRange.getRecords().stream()
                .map(MessageParameterFactory::changeReturn)
                .collect(Collectors.toList()));
        return response;

    }


    @Mapper
    interface PageQueryMessageParameterResponseConverter {
        PageQueryMessageParameterResponseConverter INSTANCE = Mappers.getMapper(PageQueryMessageParameterResponseConverter.class);

        UmmMsgParameter convert(PageQueryMessageParameterResponse pageQueryMessageParameterResponse);

        PageQueryMessageParameterResponse convert(UmmMsgParameter ummMsgParameter);
    }
}
