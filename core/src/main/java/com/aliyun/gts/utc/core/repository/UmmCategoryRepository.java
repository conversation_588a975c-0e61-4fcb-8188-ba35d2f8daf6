/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository;

import com.aliyun.gts.utc.core.domain.UmmCategory;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;


public interface UmmCategoryRepository {

    /**
     * 通过 id 获取ummCategory
     *
     * @param id ummCategory id
     * @return ummCategory；null 如果账户不存在
     */
    UmmCategory getById(Long id);

    List<UmmCategory> getByIds(List<Long> ids);

    UmmCategory getByCode(String code);

    /**
     * 获取一个区间内的ummCategory
     *
     * @param pageNum
     * @param pageSize 期望个数
     * @return 区间内的ummCategory列表
     */
    IPage<UmmCategory> getByRange(long pageNum, long pageSize,
                                  UmmCategory ummCategory);

    /**
     * 添加ummCategory
     *
     * @param ummCategory 待添加ummCategory
     */
    UmmCategory add(UmmCategory ummCategory);

    /**
     * 更新ummCategory
     *
     * @param ummCategory 待更新ummCategory对象
     * @return true，更新成功；false，ummCategory不存在
     */
    boolean update(UmmCategory ummCategory);

    /**
     * 通过 id 删除ummCategory
     *
     * @param id 账户 id
     * @return true，删除成功；false，ummCategory不存在
     */
    boolean deleteById(Long id);

    /**
     * 获取此id下级栏目id列表
     * @param id 主键
     * @return 下级栏目id列表
     */
    List<UmmCategory> getByParentId(Long id);

    List<UmmCategory> getByParentIds(List<Long> ids);

    void checkCategoryExistInCurrentTenantCode(Long id);

    /**
     * 获取告警通知栏目id
     * @return 栏目id
     */
    Long getWarningId();
}