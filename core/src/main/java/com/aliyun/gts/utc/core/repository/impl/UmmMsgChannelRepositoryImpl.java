/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import com.aliyun.gts.utc.core.repository.UmmMsgChannelRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dao.UmmMsgChannelMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgChannelDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgChannelRepositoryImpl.java
 * source table name: umm_msg_channel
 * initial code lines: 122
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Repository
public class UmmMsgChannelRepositoryImpl implements UmmMsgChannelRepository {

    @Autowired
    private UmmMsgChannelMapper ummMsgChannelMapper;

    @Override
    public UmmMsgChannel getById(Long id) {
        LambdaQueryWrapper<UmmMsgChannelDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgChannelDO>()
                .eq(UmmMsgChannelDO::getId, id)
                .eq(UmmMsgChannelDO::getTenantCode, RuntimeContextUtil.getTenantCode());

        UmmMsgChannelDO ummMsgChannelDO = ummMsgChannelMapper.selectOne(wrapperSelectDetail);
        return UmmMsgChannelDOConverter.INSTANCE.convert(ummMsgChannelDO);
    }

    @Override
    public List<UmmMsgChannel> getByMsgId(Long msgId) {
        LambdaQueryWrapper<UmmMsgChannelDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgChannelDO>()
                .eq(UmmMsgChannelDO::getMsgId,msgId)
                .eq(UmmMsgChannelDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        List<UmmMsgChannelDO> ummMsgChannelDOS = ummMsgChannelMapper.selectList(wrapperSelectDetail);
        List<UmmMsgChannel> ummMsgChannels=new ArrayList<>();
        for (UmmMsgChannelDO ummMsgChannelDO:ummMsgChannelDOS){
            UmmMsgChannel ummMsgChannel=new UmmMsgChannel();
            BeanUtils.copyProperties(ummMsgChannelDO,ummMsgChannel);
            ummMsgChannels.add(ummMsgChannel);
        }
        return ummMsgChannels;
    }

    @Override
    public IPage<UmmMsgChannel> getByRange(Long pageNum, Long pageSize,
                                           UmmMsgChannel ummMsgChannel) {

        Page<UmmMsgChannelDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        UmmMsgChannelDO ummMsgChannelDO =
                UmmMsgChannelDOConverter.INSTANCE.convert(ummMsgChannel);
        ummMsgChannelDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        QueryWrapper<UmmMsgChannelDO> queryWrapper = new QueryWrapper();
        queryWrapper.setEntity(ummMsgChannelDO);
        IPage<UmmMsgChannelDO> iPage = ummMsgChannelMapper.selectPage(page,
                queryWrapper);
        List<UmmMsgChannel> list = iPage.getRecords().stream()
                .map(UmmMsgChannelDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
        Page<UmmMsgChannel> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setSize(iPage.getSize());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    @Override
    public UmmMsgChannel add(UmmMsgChannel ummMsgChannel) {

        UmmMsgChannelDO ummMsgChannelDO =
                UmmMsgChannelDOConverter.INSTANCE.convert(ummMsgChannel);
        ummMsgChannelDO.setTenantCode(RuntimeContextUtil.getTenantCode());
        ummMsgChannelMapper.insert(ummMsgChannelDO);
        ummMsgChannel.setId(ummMsgChannelDO.getId());
        return ummMsgChannel;
    }

    @Override
    public Boolean batchAdd(List<UmmMsgChannelDO> ummMsgChannels) {
        return ummMsgChannelMapper.batchAdd(ummMsgChannels);
    }

    @Override
    public boolean update(UmmMsgChannel ummMsgChannel) {

        UmmMsgChannelDO ummMsgChannelDO =
                UmmMsgChannelDOConverter.INSTANCE.convert(ummMsgChannel);
        LambdaQueryWrapper<UmmMsgChannelDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgChannelDO>()
                .eq(UmmMsgChannelDO::getId, ummMsgChannelDO.getId())
                .eq(UmmMsgChannelDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMsgChannelDO newUmmMsgChannelDO = ummMsgChannelMapper.selectOne(wrapperSelectDetail);

        if (newUmmMsgChannelDO == null) {
            return false;
        }
        if (ummMsgChannelDO.getMsgId() != null) {
            newUmmMsgChannelDO.setMsgId(ummMsgChannelDO.getMsgId());
        }
        if (ummMsgChannelDO.getChannelId() != null) {
            newUmmMsgChannelDO.setChannelId(ummMsgChannelDO.getChannelId());
        }
        if (ummMsgChannelDO.getStartDate() != null) {
            newUmmMsgChannelDO.setStartDate(ummMsgChannelDO.getStartDate());
        }
        if (ummMsgChannelDO.getEndDate() != null) {
            newUmmMsgChannelDO.setEndDate(ummMsgChannelDO.getEndDate());
        }
        if (ummMsgChannelDO.getTenantCode() != null) {
            newUmmMsgChannelDO.setTenantCode(ummMsgChannelDO.getTenantCode());
        }
        if (ummMsgChannelDO.getAppId() != null) {
            newUmmMsgChannelDO.setAppId(ummMsgChannelDO.getAppId());
        }
        if (ummMsgChannelDO.getStatus() != null) {
            newUmmMsgChannelDO.setStatus(ummMsgChannelDO.getStatus());
        }

        ummMsgChannelMapper.updateById(newUmmMsgChannelDO);
        return true;
    }

    @Override
    public boolean deleteById(Long id) {
        LambdaQueryWrapper<UmmMsgChannelDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmMsgChannelDO>()
                .eq(UmmMsgChannelDO::getId, id)
                .eq(UmmMsgChannelDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMsgChannelDO ummMsgChannelDO = ummMsgChannelMapper.selectOne(wrapperSelectDetail);
        if (ummMsgChannelDO == null) {
            return false;
        }
        return ummMsgChannelMapper.deleteById(id) > 0;
    }

    /**
     * 根据消息id查询消息渠道
     *
     * @param msgId 消息id
     * @return 消息渠道id
     */
    @Override
    public UmmMsgChannel getByMessage(Long msgId) {
        UmmMsgChannel ummMsgChannel = new UmmMsgChannel();
        LambdaQueryWrapper<UmmMsgChannelDO> queryWrapper =
                new QueryWrapper<UmmMsgChannelDO>()
                        .lambda().eq(UmmMsgChannelDO::getMsgId, msgId)
                        .eq(UmmMsgChannelDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        List<UmmMsgChannelDO> ummMsgChannelDOList =
                ummMsgChannelMapper.selectList(queryWrapper);
        if (null == ummMsgChannelDOList || ummMsgChannelDOList.size() == 0) {
            return ummMsgChannel;
        }
        ummMsgChannel =
                UmmMsgChannelDOConverter.INSTANCE.convert(ummMsgChannelDOList.get(0));
        return ummMsgChannel;
    }

    @Override
    public List<UmmMsgChannel> getByMsgId(List<Long> msgIds) {
        if (CollectionUtils.isEmpty(msgIds)) {
            return Arrays.asList();
        }
        LambdaQueryWrapper<UmmMsgChannelDO> queryWrapper =
                new LambdaQueryWrapper<UmmMsgChannelDO>()
                        .in(UmmMsgChannelDO::getMsgId, msgIds)
                        .eq(UmmMsgChannelDO::getTenantCode, RuntimeContextUtil.getTenantCode());

        return ummMsgChannelMapper.selectList(queryWrapper).stream().map(UmmMsgChannelDOConverter.INSTANCE::convert).collect(Collectors.toList());
    }

    @Mapper
    interface UmmMsgChannelDOConverter {
        UmmMsgChannelDOConverter INSTANCE =
                Mappers.getMapper(UmmMsgChannelDOConverter.class);

        UmmMsgChannelDO convert(UmmMsgChannel ummMsgChannel);

        UmmMsgChannel convert(UmmMsgChannelDO ummMsgChannelDO);
    }

}
