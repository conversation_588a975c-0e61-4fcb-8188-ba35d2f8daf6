package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.facade.request.parameter.PageQueryMessageParameterRequest;
import com.aliyun.gts.utc.api.facade.response.parameter.PageQueryMessageParameterResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <AUTHOR>
 * @date 2021/6/4 11:02
 */
public interface MessageParameterManageService {

    /**
     * 分页查询消息参数列表
     * @param request 请求体
     * @return 分页后的消息参数列表
     */
    Page<PageQueryMessageParameterResponse> pageQuery(PageQueryMessageParameterRequest request);
}
