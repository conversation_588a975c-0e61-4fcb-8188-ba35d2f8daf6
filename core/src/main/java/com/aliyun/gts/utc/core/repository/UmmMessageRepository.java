/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository;

import com.aliyun.gts.utc.core.domain.UmmMessage;
import com.aliyun.gts.utc.core.domain.UmmMessageQueryCondition;
import com.aliyun.gts.utc.dal.dataobject.UmmMessageDO;
import com.aliyun.gts.utc.dal.dto.PageMessageDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageQueryDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface UmmMessageRepository {

    /**
     * 通过 id 获取ummMessage
     *
     * @param id ummMessage id
     * @return ummMessage；null 如果账户不存在
     */
    UmmMessage getById(Long id);


    /**
     * 通过 guid 获取ummMessage
     *
     * @param guid ummMessage guid
     * @return ummMessage；null 如果账户不存在
     */
    UmmMessage getByGuid(String guid);

    List<UmmMessage> getByIds(List<Long> ids);

    /**
     * 统计一个模版下指定消息的数量
     * @param templateId
     * @return
     */
    List<UmmMessage> getByTplId(Long templateId);

    /**
     * 获取一个区间内的ummMessage
     *
     * @param pageNum
     * @param pageSize 期望个数
     * @return 区间内的ummMessage列表
     */
    IPage<UmmMessage> getByRange(Long pageNum, Long pageSize,
                                 UmmMessage ummMessage);

    IPage<UmmMessage> getPage(Long pageNum, Long pageSize,
                              UmmMessageQueryCondition ummMessageQueryCondition);

    /**
     * 添加ummMessage
     *
     * @param ummMessage 待添加ummMessage
     */
    UmmMessage add(UmmMessage ummMessage);

    /**
     * 更新ummMessage
     *
     * @param ummMessage 待更新ummMessage对象
     * @return true，更新成功；false，ummMessage不存在
     */
    boolean update(UmmMessage ummMessage);


    /**
     * 更新ummMessage的状态
     *
     * @param msgId
     * @param successNum
     * @return true，更新成功
     */
    boolean updateStatus(Long msgId, Integer successNum);

    /**
     * 通过 id 删除ummMessage
     *
     * @param id 账户 id
     * @return true，删除成功；false，ummMessage不存在
     */
    boolean deleteById(Long id);

    boolean deleteByIdOnBatch(List<Long> ids);

    Page<PageMessageDTO> pageQuery(Page<UmmMessageDO> page,
                                   PageMessageQueryDTO query);

}
