package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.facade.request.template.CreateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.DeleteMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.PageQueryMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.UpdateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.response.template.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/2 15:00
 */
public interface MessageTemplateManageService {

    /**
     * 新增消息模板
     * @param request 新增消息模板请求体
     * @return 新增消息模板返回体
     */
    CreateMessageTemplateResponse add(CreateMessageTemplateRequest request);

    /**
     * 新增校验模板编码或应用名称是否存在重复
     * @param tplCode
     * @return
     */
    public Boolean checkTplCode(String tplCode);


    /**
     * 编辑消息模板
     * @param request 请求体
     * @return 编辑结果
     */
    UpdateMessageTemplateResponse modify(UpdateMessageTemplateRequest request);

    /**
     * 查询消息模板详情
     * @param id 消息模板id
     * @return 消息模板详情
     */
    QueryMessageTemplateResponse detail(Long id);

    /**
     * 批量删除消息模板
     * @param request 请求体，消息模板id列表
     * @return 删除结果
     */
    DeleteMessageTemplateResponse delete(DeleteMessageTemplateRequest request);

    /**
     * 分页查询消息模板
     * @param request 请求体
     * @return 分页后的消息模板列表
     */
    Page<PageQueryMessageTemplateResponse> pageQuery(PageQueryMessageTemplateRequest request);

    /**
     * 获取告警通知消息模板列表
     * @return 消息模板列表
     * @param id 主键
     */
    List<GetWarningListResponse> getWarningList(Long id);
}
