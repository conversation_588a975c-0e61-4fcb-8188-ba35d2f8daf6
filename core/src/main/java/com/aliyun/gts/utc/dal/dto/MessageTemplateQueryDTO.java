package com.aliyun.gts.utc.dal.dto;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/3 10:11
 */
@Data
public class MessageTemplateQueryDTO {

    @ApiParam("消息标题关键字")
    private String titleQuery;

    @ApiParam("提交人关键字")
    private String submitNameQuery;

    @ApiParam("栏目id")
    private Long categoryId;

    @ApiParam("栏目树的栏目id列表")
    private List<Long> categoryIdList;

    @ApiParam("排序字段 title|category|gmtModify")
    private String orderField;

    @ApiParam("是否升序")
    private Boolean isAsc;

    private String tenantCode;
}
