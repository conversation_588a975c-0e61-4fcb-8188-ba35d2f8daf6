package com.aliyun.gts.utc.application.impl;

import com.alibaba.fastjson.JSONArray;
import com.aliyun.gts.utc.api.auto.dto.*;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.UpdateUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.QueryUmmMessageResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgreceiver.QueryUmmMsgReceiverResponse;
import com.aliyun.gts.utc.api.enums.ReceiverTypeEnum;
import com.aliyun.gts.utc.api.enums.SystemParamEnum;
import com.aliyun.gts.utc.api.enums.UserTypeEnum;
import com.aliyun.gts.utc.api.facade.request.MailMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.SmsMessageSendRequest;
import com.aliyun.gts.utc.application.MessageSendAppService;
import com.aliyun.gts.utc.common.utils.ProcessTemplateUtil;
import com.aliyun.gts.utc.core.domain.UmmMessage;
import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import com.aliyun.gts.utc.core.exceptions.BizException;
import com.aliyun.gts.utc.core.factory.ListenerChannelFactory;
import com.aliyun.gts.utc.core.repository.UmmMessageRepository;
import com.aliyun.gts.utc.core.service.*;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.integration.client.MessageClientUtils;
import com.aliyun.gts.utc.spi.UserServiceSpi;
import com.aliyun.gts.utc.spi.dto.User;
import com.aliyun.gts.utc.spi.request.UserQueryRequest;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.gts.utc.common.constants.StatusConstants.*;

@Service
@Slf4j
public class MessageSendAppServiceImpl implements MessageSendAppService {

    @Autowired
    private UmmMsgSendManageCoreService ummMsgSendManageCoreService;

    @Autowired
    private UmmMessageManageCoreService ummMessageManageCoreService;

    @Autowired
    private UserServiceSpi userServiceSpi;

    /**
     * 邮件发送服务
     */
    @Autowired
    private MailService mailService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private ListenerChannelFactory channelFactory;

    @Autowired
    private UmmMsgReceiverManageCoreService ummMsgReceiverManageCoreService;

    @Autowired
    private UmmMsgChannelManageCoreService channelManageAppService;

    @Autowired
    private UmmMsgSendLogManageCoreService ummMsgSendLogManageCoreService;

    @Autowired
    private UmmMessageRepository ummMessageRepository;

    private List<User> findSendUser(UmmMsgReceiverDTO ummMsgReceiverDTO) {
        String receiverType = ummMsgReceiverDTO.getReceiverType();

        UserQueryRequest userQueryRequest = new UserQueryRequest();
        if (ReceiverTypeEnum.ALL.name().equals(receiverType)) {
            // 发送所有用户
            userQueryRequest.setPageSize(Integer.MAX_VALUE);
            userQueryRequest.setPageNum(1);
            List<User> users = userServiceSpi.queryUser(userQueryRequest);
            return users;
        } else if (ReceiverTypeEnum.ORG.name().equals(receiverType)
                || ReceiverTypeEnum.POS.name().equals(receiverType)) {
            List<String> orgGuidList = Arrays.asList(ummMsgReceiverDTO.getReceiverGuid().split(","));
            userQueryRequest.setOrgGuidList(orgGuidList);
            List<User> users = userServiceSpi.listMembersByOrgCode(orgGuidList);
            return users;
        } else if (ReceiverTypeEnum.USR.name().equals(receiverType)) {
            List<String> userCodeList = Arrays.asList(ummMsgReceiverDTO.getReceiverGuid().split(","));
            return userServiceSpi.queryUser(userCodeList);
        } else {
            // 非法ReceiverType处理
            return new ArrayList<>();
        }
    }

    /**
     *
     * @param ummMsgSendDTOS
     * @param isUpdate 是否跟新状态
     * @return
     */
    private List<UmmMsgSendDTO> batchSaveSendMsg(List<UmmMsgSendDTO> ummMsgSendDTOS,Boolean isRealTime) {
        if (ummMsgSendDTOS == null || ummMsgSendDTOS.size() == 0) {
            return Collections.EMPTY_LIST;
        }
        Long msgId = Long.parseLong(ummMsgSendDTOS.get(0).getMsgId());
        List<UmmMsgSendDTO> successSendList = ummMsgSendDTOS.stream().filter(o -> SEND_SUCCESS_STATUS.equals(o.getStatus())).collect(Collectors.toList());
        UmmMessageDTO ummMessageDTO = ummMessageManageCoreService.queryUmmMessage(msgId).getUmmMessageDTO();
        int successNum = ummMessageDTO.getSuccessNum() == null? 0 : ummMessageDTO.getSuccessNum();
        if (successNum + successSendList.size() > ummMessageDTO.getTotalNum()) {
            throw new BizException("发送成功次数超过总数");
        }
        UpdateUmmMessageRequest request = new UpdateUmmMessageRequest();
        UmmMessageDTO ummMessageUpdate = new UmmMessageDTO();
        ummMessageUpdate.setId(msgId);
        ummMessageUpdate.setSuccessNum(successNum + successSendList.size());
        request.setUmmMessageDTO(ummMessageUpdate);
        ummMessageManageCoreService.updateUmmMessage(request);
        if(isRealTime){
            dealRealTime(msgId, successSendList, successNum, ummMsgSendDTOS);
        }

        Long sendId = ummMsgSendDTOS.get(0).getId();

        ummMsgSendDTOS.parallelStream().forEach(item -> item.setTenantCode(RuntimeContextUtil.getTenantCode()));
        if (sendId != null) {
            ummMsgSendDTOS.forEach(item -> item.setSendTime(new Date()));
            ummMsgSendManageCoreService.batchUpdateUmmMsgSend(ummMsgSendDTOS);
        } else {
            ummMsgSendManageCoreService.batchCreateUmmMsgSend(ummMsgSendDTOS);
        }
        return ummMsgSendDTOS;
    }
    /**
     * 处理实时发送消息
     * @param msgId 消息id
     * @param successSendList 发送成功的消息
     * @param successNum 发送成功的数量
     * @param ummMsgSendDTOS 所有消息
     */
    private void dealRealTime(Long msgId, List<UmmMsgSendDTO> successSendList,int successNum,List<UmmMsgSendDTO> ummMsgSendDTOS){
        ummMessageManageCoreService.updateStatus(msgId, successNum + successSendList.size());
        //如果是实时发送消息重新发送成功，这跟新message_send表状态
        List<UmmMsgSendDTO> ummMsgSendDTOList = ummMsgSendManageCoreService.queryUmmMsgSendByMsgId(String.valueOf(msgId));
        //用户姓名对应id map
        if(!CollectionUtils.isEmpty(ummMsgSendDTOList)) {
            Map<String, Long> UserCodeMap = ummMsgSendDTOList.stream().collect(Collectors.toMap(UmmMsgSendDTO::getUserCode, UmmMsgSendDTO::getId));
            for (UmmMsgSendDTO ummMsgSendDTO : ummMsgSendDTOS) {
                ummMsgSendDTO.setId(UserCodeMap.get(ummMsgSendDTO.getUserCode()));
            }
        }
    }

    private List<UmmMsgSendDTO> saveSend(List<User> sendUser, UmmMessageDTO ummMessageDTO, Long channelId) {
        //查询消息渠道
        Long msgId = ummMessageDTO.getId();
        List<UmmMsgSendDTO> ummMsgSendDTOS = new ArrayList<>();
        String param = ummMessageDTO.getParam();
        List<CustomParamDTO> customParamDTOS = JSONArray.parseArray(param, CustomParamDTO.class);
        Map<String, Object> rootMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(customParamDTOS)) {
            for (CustomParamDTO customParamDTO : customParamDTOS) {
                String name = customParamDTO.getName();
                String value = customParamDTO.getValue();
                rootMap.put(name, StringUtils.isNotBlank(value) ? value : "");
            }
        }
        if (!CollectionUtils.isEmpty(sendUser)) {
            for (User user : sendUser) {
                String guid = UUID.randomUUID().toString().replace("-", "");
                UmmMsgSendDTO ummMsgSendDTO = new UmmMsgSendDTO();
                ummMsgSendDTO.setGuid(guid);
                ummMsgSendDTO.setUserGuid(user.getUserId());
                ummMsgSendDTO.setUserName(user.getUserName());
                ummMsgSendDTO.setUserCode(user.getUserId());
                ummMsgSendDTO.setUserOrg(user.getOrg());
                ummMsgSendDTO.setUserPostion(user.getPosition());
                ummMsgSendDTO.setEmail(user.getEmail());
                ummMsgSendDTO.setPhone(user.getPhone());
                ummMsgSendDTO.setMsgId(String.valueOf(msgId));
                if (StringUtils.isNotBlank(ummMessageDTO.getMsgTitle())) {
                    ummMsgSendDTO.setMsgTitle(messageSystemParse(ummMessageDTO.getMsgTitle(), user, rootMap));
                }
                if (StringUtils.isNotBlank(ummMessageDTO.getMsgContent())) {
                    ummMsgSendDTO.setMsgContent(messageSystemParse(ummMessageDTO.getMsgContent(), user, rootMap));
                }
                ummMsgSendDTO.setChannelId(channelId);
                ummMsgSendDTO.setSendTime(new Date());
                ummMsgSendDTO.setStatus(SEND_LOADING_STATUS);
                ummMsgSendDTO.setCreateUserGuid(ummMessageDTO.getUpdateUserGuid());
                ummMsgSendDTO.setCreateUserName(ummMessageDTO.getUpdateUserName());
                ummMsgSendDTO.setUpdateUserGuid(ummMessageDTO.getUpdateUserGuid());
                ummMsgSendDTO.setUpdateUserName(ummMessageDTO.getUpdateUserName());
                ummMsgSendDTO.setFeatures(ummMessageDTO.getFeatures());
                ummMsgSendDTO.setGmtCreate(new Date());
                ummMsgSendDTOS.add(ummMsgSendDTO);
            }
        }
        return ummMsgSendDTOS;
    }

    public static String messageSystemParse(String msgContent, User user, Map<String, Object> rootMap) {
        Map<String, Object> map = new HashMap<>();
        map.put(SystemParamEnum.userCode.name(), StringUtils.isNotBlank(user.getUserId()) ? user.getUserId() : "");
        map.put(SystemParamEnum.userName.name(), StringUtils.isNotBlank(user.getUserName()) ? user.getUserName() : "");
        map.put(SystemParamEnum.email.name(), StringUtils.isNotBlank(user.getEmail()) ? user.getEmail() : "");
        map.put(SystemParamEnum.phone.name(), StringUtils.isNotBlank(user.getPhone()) ? user.getPhone() : "");
        map.put(SystemParamEnum.nickName.name(), StringUtils.isNotBlank(user.getNickName()) ? user.getNickName() : "");
        map.put(SystemParamEnum.type.name(), UserTypeEnum.IN_USER);
        map.putAll(rootMap);
        try {
            return ProcessTemplateUtil.processTemplate(msgContent, map);
        } catch (IOException e) {
            log.error("messageSystemParse={}", e.getMessage(), e);
            return msgContent;
        } catch (TemplateException e) {
            log.error("messageSystemParse TemplateException={}", e.getMessage(), e);
            return msgContent;
        }


    }

    @Override
    public String sendEmail(MailMessageSendRequest request) {
        mailService.sendMail(request);
        return null;
    }

    @Override
    public String sendSms(SmsMessageSendRequest request) {
        smsService.sendSms(request);
        return null;
    }


    @Override
    public Boolean sendMessageByMsgSend(List<UmmMsgSendDTO> ummMsgSendDTOList) {
        //目前仅支持重发信息属于同一个消息推送
        String msgId = ummMsgSendDTOList.get(0).getMsgId();
        if (StringUtils.isBlank(msgId)) {
            return false;
        }
        //获取渠道类型
        List<UmmMsgChannel> ummMsgChannels = channelManageAppService.queryUmmMsgChannelByMsgId(Long.valueOf(msgId));
        Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(ummMsgChannels), "msgId " + msgId + " queryUmmMsgChannelByMsgId channel is " + "empty");
        Map<String, Object> extend = new HashMap<>();
        for (UmmMsgChannel ummMsgChannel : ummMsgChannels) {
            Long channelId = ummMsgChannel.getChannelId();
            //判断该渠道是否是关闭的
            if (!MessageClientUtils.checkClientEnable(channelId)) {
                ummMsgSendDTOList.forEach(o -> o.setStatus(SEND_FAIL_STATUS));
                this.batchSaveSendMsg(ummMsgSendDTOList,true);
                saveChannelEnableSendLog(Long.valueOf(msgId), "渠道关闭,请开启渠道再发送");
                continue;
            }
            UmmMessage ummMessage = ummMessageRepository.getById(Long.valueOf(msgId));
            if (ummMessage != null && ummMessage.getTplId() != null) {
                extend.put(TEMPLATE_KEY, ummMessage.getTplId());
            }
            //跟新实际发送时间
            if(ummMessage != null){
                UmmMessage ummMessageUpdateMsg = new UmmMessage();
                ummMessageUpdateMsg.setId(Long.valueOf(msgId));
                ummMessageUpdateMsg.setActualSendTime(new Date());
                ummMessageRepository.update(ummMessageUpdateMsg);
            }

            SendMsgListenerService channel = channelFactory.getChannel(channelId);
            List<UmmMsgSendDTO> notice = channel.notice(ummMsgSendDTOList, extend);
            //插入send表和变更message表状态
            this.batchSaveSendMsg(notice,true);

           return notice.stream().allMatch(o -> SEND_SUCCESS_STATUS.equals(o.getStatus()));
        }
        return false;
    }


    /**
     *
     * @param msgId
     * @param tenantId
     * @param tplId
     * @param isRealTime 是否为实时消息
     */
    @Override
    public void sendMessage(long msgId, Long tenantId, Long tplId,Boolean isRealTime) {
        //message表查询是否使用了模板以及发送消息所必备的数据
        QueryUmmMessageResponse queryUmmMessageResponse = ummMessageManageCoreService.queryUmmMessage(msgId);
        UmmMessageDTO ummMessageDTO = queryUmmMessageResponse.getUmmMessageDTO();
        Assert.notNull(ummMessageDTO, "msgId " + msgId + " getUmmMessageDTO " + "failed!");
        //receiver 表查询需要发送的人的范围
        QueryUmmMsgReceiverResponse queryUmmMsgReceiverResponse = ummMsgReceiverManageCoreService.queryUmmMsgReceiverByMsgId(msgId);
        UmmMsgReceiverDTO ummMsgReceiverDTO = queryUmmMsgReceiverResponse.getUmmMsgReceiverDTO();
        List<User> sendUserList = new ArrayList<>();
        if (ummMsgReceiverDTO != null) {
            sendUserList = this.findSendUser(ummMsgReceiverDTO);
        }
        //获取渠道类型
        List<UmmMsgChannel> ummMsgChannels = channelManageAppService.queryUmmMsgChannelByMsgId(msgId);
        Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(ummMsgChannels), "msgId " + msgId + " queryUmmMsgChannelByMsgId channel is " + "empty");
        //message表更新totalNum的值
        Integer totalNum = sendUserList.size() * ummMsgChannels.size();
        UpdateUmmMessageRequest request = new UpdateUmmMessageRequest();
        UmmMessageDTO ummMessageUpdate = new UmmMessageDTO();
        ummMessageUpdate.setId(msgId);
        ummMessageUpdate.setTotalNum(totalNum);
        request.setUmmMessageDTO(ummMessageUpdate);
        ummMessageManageCoreService.updateUmmMessage(request);

        Map<String, Object> extend = new HashMap<>();
        if (tenantId != null) {
            extend.put(TENANTID_KEY, tenantId);
        }
        if (tplId != null) {
            extend.put(TEMPLATE_KEY, tplId);
        }
        for (UmmMsgChannel ummMsgChannel : ummMsgChannels) {
            Long channelId = ummMsgChannel.getChannelId();
            //组装消息待入send表
            List<UmmMsgSendDTO> ummMsgSendDTOS = this.saveSend(sendUserList, ummMessageDTO, channelId);
            //判断该渠道是否是关闭的
            if (!MessageClientUtils.checkClientEnable(channelId)) {
                ummMsgSendDTOS.forEach(o -> o.setStatus(SEND_FAIL_STATUS));
                this.batchSaveSendMsg(ummMsgSendDTOS,true);
                saveChannelEnableSendLog(msgId, "渠道关闭,请开启渠道再发送");
                continue;
            }
            List<UmmMsgSendDTO> notice = new ArrayList<>();
            if(isRealTime){
            SendMsgListenerService channel = channelFactory.getChannel(channelId);
            notice = channel.notice(ummMsgSendDTOS, extend);
            }else {
                for (UmmMsgSendDTO ummMsgSendDTO : ummMsgSendDTOS) {
                    ummMsgSendDTO.setStatus(SEND_AWAIT_STATUS);
                    ummMsgSendDTO.setSendTime(ummMessageDTO.getSendTime());
                }
                notice = ummMsgSendDTOS;
            }
            //插入send表和变更message表状态
            this.batchSaveSendMsg(notice,isRealTime);
        }
    }

    private void saveChannelEnableSendLog(Long msgId, String msg) {
        List<com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO> ummMsgSendDTOS = ummMsgSendManageCoreService.queryUmmMsgSendByMsgId(String.valueOf(msgId));
        List<UmmMsgSendLogDTO> ummMsgSendLogDTOS = new ArrayList<>();
        for (UmmMsgSendDTO ummMsgSendDTO : ummMsgSendDTOS) {
            UmmMsgSendLogDTO ummMsgSendLogDTO = new UmmMsgSendLogDTO();
            ummMsgSendLogDTO.setMsgSendId(ummMsgSendDTO.getId());
            ummMsgSendLogDTO.setStatus(SEND_LOG_FAIL_STATUS);
            ummMsgSendLogDTO.setMessageIn(ummMsgSendDTO.getMsgContent());
            ummMsgSendLogDTO.setMessageOut(msg);
            ummMsgSendLogDTO.setSendTime(new Date());
            ummMsgSendLogDTO.setGmtCreate(new Date());
            ummMsgSendLogDTO.setTenantCode(RuntimeContextUtil.getTenantCode());
            ummMsgSendLogDTOS.add(ummMsgSendLogDTO);
        }
        if (!CollectionUtils.isEmpty(ummMsgSendLogDTOS)) {
            ummMsgSendLogManageCoreService.batchCreateUmmMsgSendLog(ummMsgSendLogDTOS);
        }
    }
}
