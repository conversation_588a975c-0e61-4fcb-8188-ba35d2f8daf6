package com.aliyun.gts.utc.core.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.dto.SourceDTO;
import com.aliyun.gts.utc.api.dto.UmmMessageDTO;
import com.aliyun.gts.utc.api.facade.request.message.MessageReplayRequest;
import com.aliyun.gts.utc.api.facade.request.message.PageQueryMessageSendResultRequest;
import com.aliyun.gts.utc.api.facade.response.message.MessageReplayResponse;
import com.aliyun.gts.utc.api.facade.response.message.PageQueryMessageSendResultResponse;
import com.aliyun.gts.utc.api.facade.response.message.QueryMessageResponse;
import com.aliyun.gts.utc.application.MessageSendAppService;
import com.aliyun.gts.utc.common.constants.MessageConstant;
import com.aliyun.gts.utc.common.constants.MessageTemplateConstant;
import com.aliyun.gts.utc.common.constants.StatusConstants;
import com.aliyun.gts.utc.core.domain.*;
import com.aliyun.gts.utc.core.exceptions.BizException;
import com.aliyun.gts.utc.core.factory.MessageFactory;
import com.aliyun.gts.utc.core.repository.*;
import com.aliyun.gts.utc.core.service.MessageManageService;
import com.aliyun.gts.utc.core.utils.MailUtils;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgSendDO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.aliyun.gts.utc.common.constants.MessageConstant.SEND_SUCCESS_STATUS;

@Service
public class MessageManageServiceImpl implements MessageManageService {

    @Resource
    UmmMessageRepository ummMessageRepository;

    @Resource
    UmmMsgSendRepository ummMsgSendRepository;

    @Resource
    UmmMsgChannelRepository ummMsgChannelRepository;

    @Resource
    UmmCategoryRepository ummCategoryRepository;

    @Resource
    UmmMsgReceiverRepository ummMsgReceiverRepository;

    @Resource
    UmmTemplateRepository ummTemplateRepository;

    @Autowired
    private MessageSendAppService messageSendAppService;

    @Autowired
    private UmmSourceRepository ummSourceRepository;

    @Value("${dingtalk.tenantId:4247}")
    private Long tenantId;

    /**
     * 分页查询消息发送明细
     *
     * @param request 请求体
     * @return 分页后的发送明细
     */
    @Override
    public Page<PageQueryMessageSendResultResponse> sendResultPage(PageQueryMessageSendResultRequest request) {
        Page<UmmMsgSend> page = ummMsgSendRepository.resultPageQuery(request);
        return MessageFactory.createResultPageResponse(page);
    }

    /**
     * 获取消息详情
     *
     * @param id 主键
     * @return 消息想
     */
    @Override
    public QueryMessageResponse getDetail(Long id) {
        UmmMessage ummMessage = ummMessageRepository.getById(id);
        if (null == ummMessage || null == ummMessage.getId() || !Objects.equals(ummMessage.getTenantCode(), RuntimeContextUtil.getTenantCode())) {
            throw new BizException("消息不存在");
        }
        UmmMessageDTO ummMessageDTO =
                UmmMessageDTOConverter.INSTANCE.convert(ummMessage);
        UmmMsgChannel ummMsgChannel = ummMsgChannelRepository.getByMessage(id);
        UmmCategory ummCategory =
                ummCategoryRepository.getById(ummMessage.getCategoryId());
        UmmMsgReceiver ummMsgReceiver = ummMsgReceiverRepository.getByMsgId(id);
        UmmTemplate ummTemplate =
                ummTemplateRepository.getById(ummMessage.getTplId());
        QueryMessageResponse response =
                MessageFactory.createDetailResponse(ummMessageDTO,
                        ummMsgChannel.getChannelId());
        if (null != ummCategory) {
            response.setCategoryId(ummCategory.getId());
            response.setCategoryName(ummCategory.getCategoryName());
        }
        if (Objects.nonNull(ummTemplate)) {
            response.setTplCode(ummTemplate.getTplCode());
            if (ummTemplate.getSourceId() != null) {
                UmmSource ummSource = ummSourceRepository.getById(ummTemplate.getSourceId());
                if (ummSource != null) {
                    SourceDTO source = new SourceDTO();
                    source.setLabel(ummSource.getSourceName());
                    source.setValue(ummSource.getId());
                    response.setSource(source);
                }
            }
        }
        if (null != ummMsgReceiver) {
            response.setReceiverType(ummMsgReceiver.getReceiverType());
            if (1 == response.getSendType()) {
                response.setSendTypeName(MessageTemplateConstant.REAL_TIME_SEND);
            } else {
                response.setSendTypeName(MessageTemplateConstant.TIMING_SEND);
            }
            if (StringUtils.isNotEmpty(ummMsgReceiver.getReceiverGuid())) {
                String receiverGuid = ummMsgReceiver.getReceiverGuid();
                String[] receiverGuidArray = receiverGuid.split(",");
                List<String> receiverGuidArrayList =  Arrays.asList(receiverGuidArray);
                response.setReceiverGuidList(receiverGuidArrayList);

            }
        }
        //处理发送数量
        response.setSendCount(ummMsgSendRepository.getSendCount(id));
        response.setSendSuccessCount(ummMsgSendRepository.getSendSuccessCount(id));
        response.setAwaitCount(ummMsgSendRepository.getSendAwaitCount(id));
        return response;
    }

    /**
     * 消息重发
     *
     * @param request 请求体
     * @return 重复结果
     */
    @Override
    public MessageReplayResponse replay(MessageReplayRequest request) {
        //目前仅支持重发信息属于同一个消息推送
        List<Long> idList = request.getIdList();
        List<UmmMsgSend> ummMsgSendList = ummMsgSendRepository.getByIds(idList);
        if (ummMsgSendList.size() == 0) {
            throw new BizException("消息不存在");
        }
        for (UmmMsgSend ummMsgSend : ummMsgSendList) {
            if (SEND_SUCCESS_STATUS == ummMsgSend.getStatus()) {
                throw new BizException("发送成功消息不可重发");
            }
            if (ummMsgSend.getChannelId() == null) {
                throw new BizException("消息渠道不存在");
            }
            if (StringUtils.isBlank(ummMsgSend.getEmail())) {
                throw new BizException("用户邮箱不存在");
            }
            if (!MailUtils.isValidEmail(ummMsgSend.getEmail())) {
                throw new BizException("用户邮箱非法");
            }
        }
        List<UmmMsgSendDTO> ummMsgSendDTOList = ummMsgSendList.stream()
                .map(UmmMsgSendDTOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
        messageSendAppService.sendMessageByMsgSend(ummMsgSendDTOList);
        MessageReplayResponse messageReplayResponse = new MessageReplayResponse();

        if (ummMsgSendDTOList.stream().allMatch(item -> StatusConstants.SEND_SUCCESS_STATUS.equals(item.getStatus()))) {
            messageReplayResponse.setResult(true);
        } else {
            throw new BizException("重复发送失败！");
        }

        return messageReplayResponse;
    }

    @Override
    public Boolean sendMsgByUmmMessageId(Long ummMessageId) {
        QueryWrapper<UmmMsgSendDO> mq = new QueryWrapper<>();
        mq.lambda().eq(UmmMsgSendDO::getMsgId,ummMessageId);
        List<UmmMsgSendDTO> ummMsgSendDTOS = ummMsgSendRepository.queryUmmMsgSendByMsgId(String.valueOf(ummMessageId));
       if(CollectionUtil.isNotEmpty(ummMsgSendDTOS)){
         return  messageSendAppService.sendMessageByMsgSend(ummMsgSendDTOS);
       }
       return false;
    }

    @Mapper
    interface UmmMessageDTOConverter {
        UmmMessageDTOConverter INSTANCE =
                Mappers.getMapper(UmmMessageDTOConverter.class);

        UmmMessage convert(UmmMessageDTO ummMessageDTO);

        UmmMessageDTO convert(UmmMessage ummMessage);
    }

    @Mapper
    interface UmmMsgSendDTOConverter {
        UmmMsgSendDTOConverter INSTANCE =
                Mappers.getMapper(UmmMsgSendDTOConverter.class);

        UmmMsgSend convert(UmmMsgSendDTO ummMessageSendDTO);

        UmmMsgSendDTO convert(UmmMsgSend ummMessage);
    }


}
