package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.NoticeRollbackRequest;

import java.util.List;
import java.util.Map;

public interface SendMsgListenerService {

    /**
     * 获取通道类型
     *
     * @return
     */
    Long getChannelId();

    /**
     * 通知
     *
     * @return
     */
    List<UmmMsgSendDTO> notice(List<UmmMsgSendDTO> ummMsgSendDTOS,Map<String,Object> extend);

    /**
     * 通知撤回
     *
     * @return
     */
    void noticeRollback(Object obj, Map<String, Object> extend);
}
