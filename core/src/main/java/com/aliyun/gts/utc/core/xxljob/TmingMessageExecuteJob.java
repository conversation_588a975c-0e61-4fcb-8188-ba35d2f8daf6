package com.aliyun.gts.utc.core.xxljob;

import com.aliyun.gts.utc.core.service.MessageManageService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * xxl-job定时任务执行
 *
 * @author: guoshengLi
 * @create: 2025-02-27 15:18
 * @Description:
 */

@Slf4j
@Component
public class TmingMessageExecuteJob {
  @Autowired
  private MessageManageService mailService;


  @XxlJob("tmingMessageJobHandler")
  public void execute() {
    // 业务逻辑参数
    String param = XxlJobHelper.getJobParam();
    log.info("定时任务执行，参数：{}", param);
    mailService.sendMsgByUmmMessageId(Long.parseLong(param));

  }

}
