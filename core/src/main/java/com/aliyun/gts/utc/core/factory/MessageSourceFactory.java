package com.aliyun.gts.utc.core.factory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.gts.utc.api.dto.UmmSourceDTO;
import com.aliyun.gts.utc.api.facade.request.source.*;
import com.aliyun.gts.utc.api.facade.response.source.QueryMessageSourceResponse;
import com.aliyun.gts.utc.common.constants.MessageSourceConstant;
import com.aliyun.gts.utc.common.constants.MessageTemplateConstant;
import com.aliyun.gts.utc.core.base.enums.MessageSourceEnum;
import com.aliyun.gts.utc.core.utils.AES256Util;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dto.MessageSourceQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Date;


/**
 * <AUTHOR>
 * @version 2023/12/5
 */
@Slf4j
public class MessageSourceFactory {

    /**
     * 组装新建消息源
     * @param request 新建消息源请求体
     * @return 消息源
     */
    public static UmmSourceDTO createSource(CreateSourceSourceRequest request) {
        UmmSourceDTO ummSourceDTO = new UmmSourceDTO();
        BeanUtils.copyProperties(request, ummSourceDTO);
            if(MessageSourceEnum.MAIL.getType().equals(request.getSourceType()) && null != request.getMail()){
                MailSourceRequest mail = request.getMail();
                mail.setMailPassWord(AES256Util.encryptAes(mail.getMailPassWord()));
                ummSourceDTO.setSourceParam(JSON.toJSONString(mail));
            }else if(MessageSourceEnum.SMS.getType().equals(request.getSourceType()) && null != request.getSms()){
                SmsSourceRequest sms = request.getSms();
                ummSourceDTO.setSourceParam(JSON.toJSONString(sms));
            }else if(MessageSourceEnum.SXXY.getType().equals(request.getSourceType()) && null != request.getSxxy()){
                 SxxySourceRequest sxxy = request.getSxxy();
                 ummSourceDTO.setSourceParam(JSON.toJSONString(sxxy));
            }else {
                throw new IllegalArgumentException("参数有误");
            }
        ummSourceDTO.setTenantCode(RuntimeContextUtil.getTenantCode());
        ummSourceDTO.setIsDeleted(MessageTemplateConstant.NOT_DELETED);
        ummSourceDTO.setGmtCreate(new Date());
        ummSourceDTO.setGmtModify(new Date());
        return ummSourceDTO;
    }

    public static QueryMessageSourceResponse createSourceDetailResponse(UmmSourceDTO ummSourceDTO) {
        QueryMessageSourceResponse queryMessageSourceResponse = new QueryMessageSourceResponse();
        BeanUtils.copyProperties(ummSourceDTO, queryMessageSourceResponse);
        if(MessageSourceEnum.MAIL.getType().equals(ummSourceDTO.getSourceType())){
            MailSourceRequest mail = JSONObject.parseObject(ummSourceDTO.getSourceParam(), MailSourceRequest.class);
            mail.setMailPassWord(MessageSourceConstant.IDENTIFICATIONPASSWORD);
            queryMessageSourceResponse.setMail(mail);
        }else if(MessageSourceEnum.SMS.getType().equals(ummSourceDTO.getSourceType())){
            SmsSourceRequest sms = JSONObject.parseObject(ummSourceDTO.getSourceParam(), SmsSourceRequest.class);
            queryMessageSourceResponse.setSms(sms);
        }else if(MessageSourceEnum.SXXY.getType().equals(ummSourceDTO.getSourceType())){
            SxxySourceRequest sxxy = JSONObject.parseObject(ummSourceDTO.getSourceParam(), SxxySourceRequest.class);
            queryMessageSourceResponse.setSxxy(sxxy);
        }
//        UmmSourceDTO.SourceParam sourceParam = JSON.parseObject(ummSourceDTO.getSourceParam(), UmmSourceDTO.SourceParam.class);
//        queryMessageSourceResponse.setMailHost(sourceParam.getMailHost());
//        queryMessageSourceResponse.setMailUserName(sourceParam.getMailUserName());
//        queryMessageSourceResponse.setMailPassWord(MessageSourceConstant.IDENTIFICATIONPASSWORD);
//        queryMessageSourceResponse.setMailPort(sourceParam.getMailPort());
//        queryMessageSourceResponse.setMailSSL(sourceParam.getMailSSL());
//        queryMessageSourceResponse.setSubmitter(sourceParam.getSubmitter());
        return queryMessageSourceResponse;
    }

    public static MessageSourceQueryDTO createQuery(PageQueryMessageSourceRequest request) {
        MessageSourceQueryDTO queryDTO = new MessageSourceQueryDTO();
        BeanUtils.copyProperties(request, queryDTO);
        //处理关键字查询条件
        if(StringUtils.isNotEmpty(request.getSourceName())){
            queryDTO.setSourceName("%" + request.getSourceName() + "%");
        }
        if(StringUtils.isNotEmpty(request.getUpdateUserName())){
            queryDTO.setUpdateUserName("%" + request.getUpdateUserName() + "%");
        }
        //处理排序
        if (StringUtils.isNotEmpty(request.getOrderField())) {
            switch (request.getOrderField()) {
                case MessageSourceConstant.SOURCE_NAME:
                    queryDTO.setOrderField(MessageSourceConstant.SOURCE_NAME_TABLE);
                    break;
                case MessageSourceConstant.UPDATE_USER_NAME:
                    queryDTO.setOrderField(MessageSourceConstant.UPDATE_USER_NAME_TABLE);
                    break;
                case MessageSourceConstant.SOURCE_TYPE:
                    queryDTO.setOrderField(MessageSourceConstant.SOURCE_TYPE_TABLE);
                    break;
                case MessageSourceConstant.GMT_MODIFY:
                    queryDTO.setOrderField(MessageSourceConstant.GMT_MODIFY_TABLE);
                    break;
                default:
                    queryDTO.setOrderField(null);
                    break;
            }
        }
        return queryDTO;
    }
}
