package com.aliyun.gts.utc.core.factory;

import com.aliyun.gts.utc.core.service.SendMsgListenerService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ListenerChannelFactory implements InitializingBean {

    @Autowired
    private List<SendMsgListenerService> channelList;

    private Map<Long, SendMsgListenerService> channelMap = new HashMap<>();

    public SendMsgListenerService getChannel(Long channelType) {
        SendMsgListenerService channel = channelMap.get(channelType);
        if (channel == null) {
        }
        return channel;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        channelList.forEach(channel -> {
            channelMap.put(channel.getChannelId(), channel);
        });
    }
}
