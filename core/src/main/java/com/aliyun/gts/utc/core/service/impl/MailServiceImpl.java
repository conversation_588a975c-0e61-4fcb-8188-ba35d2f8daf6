package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.request.MailMessageSendRequest;
import com.aliyun.gts.utc.core.service.MailService;
import com.aliyun.gts.utc.core.utils.EnvUtils;
import com.aliyun.gts.utc.integration.client.mail.MailClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;


@Slf4j
@Service
@Validated
public class MailServiceImpl implements MailService {

    private final MailClient mailClient;

    @Value("${spring.mail.username:}")
    private String sender;

    public MailServiceImpl(MailClient mailClient) {
        this.mailClient = mailClient;
    }

    @Override
    public void sendMail(@Valid MailMessageSendRequest request) {
        if (EnvUtils.isMockSendMail()) {
            log.info("send mail message is mocked");
            return;
        }
        Assert.hasText(sender, " sender is null");
        mailClient.sendMail(request.getSubject(), request.getContent(), request.getEmail(), sender, request.getHtmlContent());
    }

    public void setSender(String sender) {
        this.sender = sender;
    }
}
