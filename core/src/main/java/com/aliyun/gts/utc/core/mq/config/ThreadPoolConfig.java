package com.aliyun.gts.utc.core.mq.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "threadpool")
public class ThreadPoolConfig {

    private  Integer corePoolSize;

    private  Integer maximumPoolSize;

    private  Long keepAliveTime;

    private  Integer workQueueSize;
}
