package com.aliyun.gts.utc.core.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by 鸿度 on 2023-06-14 17:10:56
 */
public class EnvUtils {

    public static boolean isMockSendSms() {
        String mock = System.getenv("MOCK_SEND_MSG");
        return "1".equalsIgnoreCase(mock) || "true".equalsIgnoreCase(mock);
    }

    public static boolean isMockSend() {
        String mock = System.getenv("MOCK_SEND");
        return "1".equalsIgnoreCase(mock) || "true".equalsIgnoreCase(mock);
    }

    public static boolean isMockUser() {
        String mock = System.getenv("MSG_FOURA_PLAT");
        return "mock".equalsIgnoreCase(mock);
    }

    public static boolean isMockSendMail() {
        String mock = System.getenv("MOCK_SEND_MAIL");
        return "1".equalsIgnoreCase(mock) || "true".equalsIgnoreCase(mock);
    }
}
