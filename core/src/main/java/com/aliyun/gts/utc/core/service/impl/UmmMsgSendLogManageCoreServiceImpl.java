/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendLogDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsendlog.PageQueryUmmMsgSendLogRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsendlog.UpdateUmmMsgSendLogRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsendlog.*;
import com.aliyun.gts.utc.core.domain.UmmMsgSendLog;
import com.aliyun.gts.utc.core.repository.UmmMsgSendLogRepository;
import com.aliyun.gts.utc.core.service.UmmMsgSendLogManageCoreService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;


@Service
public class UmmMsgSendLogManageCoreServiceImpl implements UmmMsgSendLogManageCoreService {

    private final UmmMsgSendLogRepository ummMsgSendLogRepository;

    public UmmMsgSendLogManageCoreServiceImpl(UmmMsgSendLogRepository ummMsgSendLogRepository) {
        this.ummMsgSendLogRepository = ummMsgSendLogRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmMsgSendLogResponse createUmmMsgSendLog(UmmMsgSendLogDTO ummMsgSendLogDTO) {


        UmmMsgSendLog ummMsgSendLog =
                UmmMsgSendLogDTOConverter.INSTANCE.convert(ummMsgSendLogDTO);
        UmmMsgSendLog add = ummMsgSendLogRepository.add(ummMsgSendLog);
        ummMsgSendLogDTO.setId(add.getId());

        CreateUmmMsgSendLogResponse response = new CreateUmmMsgSendLogResponse();
        response.setUmmMsgSendLogDTO(ummMsgSendLogDTO);

        return response;
    }

    @Override
    public QueryUmmMsgSendLogResponse queryUmmMsgSendLog(Long id) {

        UmmMsgSendLog ummMsgSendLog = ummMsgSendLogRepository.getById(id);
        UmmMsgSendLogDTO ummMsgSendLogDTO = UmmMsgSendLogDTOConverter.INSTANCE.convert(ummMsgSendLog);
        QueryUmmMsgSendLogResponse response = new QueryUmmMsgSendLogResponse();
        response.setUmmMsgSendLogDTO(ummMsgSendLogDTO);
        return response;
    }

    @Override
    public Boolean batchCreateUmmMsgSendLog(List<UmmMsgSendLogDTO> ummMsgSendLogDTOS) {
        return ummMsgSendLogRepository.batchCreateUmmMsgSendLog(ummMsgSendLogDTOS);
    }

    @Override
    public PageQueryUmmMsgSendLogResponse pageQueryUmmMsgSendLog(PageQueryUmmMsgSendLogRequest request) {

        UmmMsgSendLogDTO ummMsgSendLogDTO = request.getUmmMsgSendLogDTO();
        UmmMsgSendLog ummMsgSendLog = UmmMsgSendLogDTOConverter.INSTANCE.convert(ummMsgSendLogDTO);
        IPage<UmmMsgSendLog> byRange = ummMsgSendLogRepository.getByRange(request.getPageNum(), request.getPageSize(), ummMsgSendLog);

        PageQueryUmmMsgSendLogResponse response = new PageQueryUmmMsgSendLogResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
            .map(UmmMsgSendLogDTOConverter.INSTANCE::convert)
            .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmMsgSendLogResponse deleteUmmMsgSendLog(Long id) {
        DeleteUmmMsgSendLogResponse deleteUmmMsgSendLogResponse = new DeleteUmmMsgSendLogResponse();
        deleteUmmMsgSendLogResponse.setIsDelete(ummMsgSendLogRepository.deleteById(id));
        return deleteUmmMsgSendLogResponse;
    }

    @Override
    public UpdateUmmMsgSendLogResponse updateUmmMsgSendLog(UpdateUmmMsgSendLogRequest request) {

        UmmMsgSendLogDTO ummMsgSendLogDTO = request.getUmmMsgSendLogDTO();

        UmmMsgSendLog ummMsgSendLog = UmmMsgSendLogDTOConverter.INSTANCE.convert(ummMsgSendLogDTO);
        UpdateUmmMsgSendLogResponse updateUmmMsgSendLogResponse = new UpdateUmmMsgSendLogResponse();
        updateUmmMsgSendLogResponse.setIsUpdate(ummMsgSendLogRepository.update(ummMsgSendLog));
        return updateUmmMsgSendLogResponse;
    }

    @Mapper
    interface UmmMsgSendLogDTOConverter {
        UmmMsgSendLogDTOConverter INSTANCE = Mappers.getMapper(UmmMsgSendLogDTOConverter.class);

        UmmMsgSendLog convert(UmmMsgSendLogDTO ummMsgSendLogDTO);

        UmmMsgSendLogDTO convert(UmmMsgSendLog ummMsgSendLog);
    }

}