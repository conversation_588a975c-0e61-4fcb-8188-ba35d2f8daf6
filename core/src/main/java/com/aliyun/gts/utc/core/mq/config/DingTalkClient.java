package com.aliyun.gts.utc.core.mq.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.xxpt.gateway.shared.api.request.OapiMessageRevokeRequest;
import com.alibaba.xxpt.gateway.shared.api.request.OapiMessageWorkNotificationRequest;
import com.alibaba.xxpt.gateway.shared.api.request.OapiMoziEmployeeListEmployeeAccountIdsRequest;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMessageRevokeResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMessageWorkNotificationResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziEmployeeListEmployeeAccountIdsResponse;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.IntelligentGetClient;
import com.alibaba.xxpt.gateway.shared.client.http.IntelligentPostClient;
import com.aliyun.gts.utc.api.enums.ReceiverTypeEnum;
import com.aliyun.gts.utc.api.facade.request.NoticeRollbackRequest;
import com.aliyun.gts.utc.core.domain.OapiMoziEmployeeListEmployeeAccountIdsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/24
 **/

@Component
public class DingTalkClient {
    private static ExecutableClient executableClient;

    @Autowired
    private DingTalkClientConfiguration dingTalkClientConfiguration;

    @PostConstruct
    public void init(){
        executableClient = ExecutableClient.getInstance();
        executableClient.setAccessKey(dingTalkClientConfiguration.getAccessKey());
        executableClient.setSecretKey(dingTalkClientConfiguration.getSecretKey());
        executableClient.setDomainName(dingTalkClientConfiguration.getDomainName());
        executableClient.setProtocal(dingTalkClientConfiguration.getProtocol());
        executableClient.init();
    }


    /**
     * 发送钉钉工作通知
     * @return
     */
    public Boolean sendDingWorkNotice(String receiverType,String receiverGuids,String msgContent,String tenantId,String bizMsgId) {
        OapiMessageWorkNotificationRequest oapiMessageWorkNotificationRequest = new OapiMessageWorkNotificationRequest();
        //executableClient保证单例
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient(oapiMessageWorkNotificationRequest.getApiUrl());
        //接收者的部门id列表
        if (ReceiverTypeEnum.ORG.name().equals(receiverType)){
            oapiMessageWorkNotificationRequest.setOrganizationCodes(receiverGuids);
        }
        //接收人用户ID
        if (ReceiverTypeEnum.USR.name().equals(receiverType)){
            oapiMessageWorkNotificationRequest.setReceiverIds(receiverGuids);
        }
        //租户ID
        oapiMessageWorkNotificationRequest.setTenantId(tenantId);
        //业务消息id
        oapiMessageWorkNotificationRequest.setBizMsgId(bizMsgId);
        //消息对象
        oapiMessageWorkNotificationRequest.setMsg(msgContent);
        //获取结果
        OapiMessageWorkNotificationResponse apiResult = intelligentGetClient.get(oapiMessageWorkNotificationRequest);
        return apiResult.getSuccess();
    }


    public Boolean  noticeRollback(NoticeRollbackRequest noticeRollbackRequest){
        OapiMessageRevokeRequest oapiMessageRevokeRequest=new OapiMessageRevokeRequest();
        //executableClient保证单例
        IntelligentGetClient intelligentGetClient = executableClient.newIntelligentGetClient(oapiMessageRevokeRequest.getApiUrl());
        String bizMsgId = noticeRollbackRequest.getBizMsgId();
        String msgApp = noticeRollbackRequest.getMsgApp();
        String msgType = noticeRollbackRequest.getMsgType();
        String tenantId = noticeRollbackRequest.getTenantId();
        oapiMessageRevokeRequest.setBizMsgId(bizMsgId);
        oapiMessageRevokeRequest.setTenantId(tenantId);
        oapiMessageRevokeRequest.setMsgApp(msgApp);
        oapiMessageRevokeRequest.setMsgType(msgType);
        OapiMessageRevokeResponse apiResult = intelligentGetClient.get(oapiMessageRevokeRequest);
        return apiResult.getContent().getSuccess();
    }

    /**
     * 批量根据员工Code获取员⼯账号ID
     */
    public List<OapiMoziEmployeeListEmployeeAccountIdsDTO> listEmployeeAccountIds(List<String> employeeCodes, Long tenantId){
        OapiMoziEmployeeListEmployeeAccountIdsRequest request=new
                OapiMoziEmployeeListEmployeeAccountIdsRequest();
        request.setEmployeeCodes(employeeCodes);
        request.setTenantId(tenantId);
        IntelligentPostClient intelligentPostClient = executableClient.newIntelligentPostClient(request.getApiUrl());
        OapiMoziEmployeeListEmployeeAccountIdsResponse apiResult = intelligentPostClient.post(request);
        return JSON.parseArray(apiResult.getContent().getData(), OapiMoziEmployeeListEmployeeAccountIdsDTO.class);
    }
}
