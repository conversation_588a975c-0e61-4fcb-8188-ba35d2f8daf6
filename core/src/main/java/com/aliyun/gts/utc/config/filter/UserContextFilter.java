package com.aliyun.gts.utc.config.filter;

import com.alibaba.fastjson.JSON;
import com.aliyun.gts.utc.common.constants.MessageConstant;
import com.aliyun.gts.utc.core.context.RuntimeContext;
import com.aliyun.gts.utc.core.utils.EnvUtils;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.integration.client.foura.UserCenterClient;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.model.user.BaseUserInfoDTO;
import com.yunqiao.foura.service.sdk.service.UserContext;
import com.yunqiao.foura.sso.conf.Conf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;


@Slf4j
@Component
public class UserContextFilter extends OncePerRequestFilter {
    private static final ThreadLocal<AntPathMatcher> ANT_PATH_MATCHER_THREAD_LOCAL = ThreadLocal.withInitial(AntPathMatcher::new);
    private List<String> ignorePatterns = ImmutableList.of(MessageConstant.API_PATH_PREFIX + "/**");

    @Value("${global.system.admin-user-id}")
    private String adminUserId;

    @Autowired
    private UserContext userContext;

    @Autowired
    private UserCenterClient userCenterClient;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (EnvUtils.isMockUser()) {
            UserInfoDTO userInfo = new UserInfoDTO();
            BaseUserInfoDTO baseUserInfo = new BaseUserInfoDTO();
            baseUserInfo.setId(1000000L);
            baseUserInfo.setUserId("1000000");
            baseUserInfo.setUserName("系统测试账号");
            baseUserInfo.setAdminType("1");
            baseUserInfo.setUserCode("1000000");
            userInfo.setBaseInfo(baseUserInfo);
            RuntimeContextUtil.setTenantCode("default");
            RuntimeContext.setUserDetail(userInfo);
        } else {
            if (validateAndIgnoreApi(request, response, filterChain)) {
                filterChain.doFilter(request, response);
                return;
            }
            String userId = com.yunqiao.foura.context.UserContext.getUserId();
            if (Strings.isNullOrEmpty(userId)) {
                // 未取到登录信息，返回异常信息
                PrintWriter out = response.getWriter();
                out.print(JSON.toJSONString(Conf.SSO_LOGIN_FAIL_RESULT));
                return;
            }
            userContext.setCurrentUserId(request, userId);
            UserInfoDTO userInfoDTO = userCenterClient.getUserDetail(userId);
            String tenantCode = request.getHeader(RuntimeContextUtil.X_TENANT_CODE);
            if (StringUtils.isNotBlank(tenantCode)) {
                RuntimeContextUtil.setTenantCode(tenantCode);
            }
            RuntimeContext.setUserDetail(userInfoDTO);
        }
        filterChain.doFilter(request, response);
    }

    private boolean validateAndIgnoreApi(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        String uri = request.getRequestURI();
        boolean ignore = ignorePatterns.stream().anyMatch(p -> {
            return p.equals(uri) || ANT_PATH_MATCHER_THREAD_LOCAL.get().match(p, uri);
        });
        if (ignore) {
            return true;
        }
        return false;
    }
}
