package com.aliyun.gts.utc.core.redis.config;

/**
 * @author: guosheng<PERSON>i
 * @create: 2025-02-28 10:52
 * @Description:
 */
import lombok.Data;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.redisson.config.Config;


/**
 * @author: guoshengLi
 * @create: 2025-02-29
 * @Description: Redisson配置类
 */
@Data
@Configuration
public class RedissonConfig {
  @Value("${spring.redis.host}")
  private String host;
  @Value("${spring.redis.port}")
  private String port;
  @Value("${spring.redis.password}")
  private String password;

  @Bean
  public RedissonClient getRedisSon() {
    Config config = new Config();
    String address = new StringBuilder("redis://").append(host).append(":").append(port).toString();
    config.useSingleServer().setAddress(address);
    if (null != password && !"".equals(password.trim())) {
      config.useSingleServer().setPassword(password);
    }
    return Redisson.create(config);
  }
}
