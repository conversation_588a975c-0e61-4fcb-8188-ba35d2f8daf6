package com.aliyun.gts.utc.core.dataobject;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
public class BaseRelateEntity {
    @TableField(
            value = "app_id",
            fill = FieldFill.INSERT
    )
    private Long appId;
    @TableField(
            value = "tenant_code",
            fill = FieldFill.INSERT
    )
    private String tenantCode;
}