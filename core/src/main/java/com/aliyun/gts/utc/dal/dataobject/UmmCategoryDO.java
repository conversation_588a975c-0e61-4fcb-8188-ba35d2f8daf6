/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dataobject;

import com.aliyun.gts.utc.core.dataobject.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmCategoryDO.java
 * source table name: umm_category
 * initial code lines: 125
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Data
@TableName("umm_category")
public class UmmCategoryDO extends BaseEntity {

    /**
    * 栏目id
    */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
    * 栏目名称
    */
    @TableField("category_name")
    private String categoryName;

    /**
    * 栏目编码
    */
    @TableField("category_code")
    private String categoryCode;

    /**
    * 上级栏目id
    */
    @TableField("parent_id")
    private Long parentId;

    /**
    * 状态 0禁用 1启用
    */
    @TableField("status")
    private Integer status;


    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    @TableField("features")
    private String features;
}