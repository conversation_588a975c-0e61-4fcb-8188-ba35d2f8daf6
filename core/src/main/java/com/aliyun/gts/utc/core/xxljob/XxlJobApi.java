package com.aliyun.gts.utc.core.xxljob;


import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import java.io.IOException;
import java.net.HttpCookie;
import java.util.*;


/**
 * @author: guoshengLi
 * @create: 2025-02-29 13:18
 * @Description: xxljob api调用类
 */
@Component
@Slf4j
public class XxlJobApi {
  //该MAP主要用于缓存Xxl-Job的Cookie
  private Map<String, String> loginCookie = new HashMap<>();

  @Value("${xxl.job.userName}")
  private String userName;

  @Value("${xxl.job.password}")
  private String password;

  @Value("${xxl.job.client.jobGroupId}")
  private String jobGroupId;

  @Value("${xxl.job.client.addUrl}")
  private String addUrl;

  @Value("${xxl.job.client.removeUrl}")
  private String removeUrl;

  @Value("${xxl.job.client.startJobUrl}")
  private String startJobUrl;

  @Value("${xxl.job.client.stopJobUrl}")
  private String stopJobUrl;

  @Value("${xxl.job.client.pageListUrl}")
  private String pageListUrl;

  @Value("${xxl.job.client.loginUrl}")
  private String loginUrl;


  /**
   *
   * @param jobDesc 任务名称
   * @param cron  cron表达式
   * @param jobName 处理程序
   * @param param 参数
   * @return
   */
  public Integer addJob(String jobDesc, String cron, String jobName, String param) {
    Map<String, Object> paramMap = new HashMap<>();
    paramMap.put("jobDesc", jobDesc);
    paramMap.put("scheduleConf", cron);
    paramMap.put("cronGen_display", cron);
    paramMap.put("schedule_conf_CRON", cron);
    paramMap.put("executorHandler", jobName);
    paramMap.put("executorParam", param);
    paramMap.put("jobGroup", jobGroupId);
    paramMap.put("author", "admin");
    paramMap.put("scheduleType", "CRON");
    paramMap.put("glueType", "BEAN");
    paramMap.put("executorRouteStrategy", "RANDOM");
    paramMap.put("misfireStrategy", "DO_NOTHING");
    paramMap.put("executorBlockStrategy", "SERIAL_EXECUTION");
    paramMap.put("executorTimeout", "0");
    paramMap.put("executorFailRetryCount", "0");
    paramMap.put("glueRemark", "GLUE代码初始化");
    HttpRequest request = HttpRequest.post(addUrl)
            .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
            //每次请求都需要带上Cookie,getCookie方法在后面
            .header("Cookie", getCookie()).form(paramMap);
      HttpResponse response = request.execute();
      String results = response.body();
      if (StrUtil.isNotBlank(results) && HttpStatus.OK.value() == response.getStatus()) {
        //定时任务创建成功后拿到任务id
        JSONObject jsonResponse = JSON.parseObject(results);
        int code = (int) jsonResponse.get("code");
        if (HttpStatus.OK.value() != code) {
          log.error("定时任务创建失败：{}", jsonResponse.get("msg"));
          throw new RuntimeException("定时任务创建失败：" + jsonResponse.get("msg"));
        }
        String contentStr = jsonResponse.getString("content");
        if(StrUtil.isNotBlank(contentStr)){
          Integer content = Integer.parseInt(contentStr); // 将字符串转换为Integer
          log.info("定时任务创建成功，任务ID为：" + content);
          //启动定时任务
          startJob(content);
          return content;
        }
      } else {
        JSONObject jsonResponse = JSON.parseObject(results);
        log.error("定时任务创建失败：{}",jsonResponse.getString("msg"));
        throw new RuntimeException("定时任务创建失败：" + jsonResponse.get("msg"));
      }
    return null;
  }

  //启动定时任务
  public void startJob(Integer jobId) {
    //创建后的定时任务默认是STOP状态,所以我们还要通过定时任务id调度任务启动接口
    HttpRequest requests = HttpRequest.post(startJobUrl)
            .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
            .header("Cookie", getCookie())
            .form("id", jobId);
    HttpResponse responses = requests.execute();
    log.info("启动定时任务xxl-job 结果 {}", JSON.toJSONString(responses));
    String results = responses.body();
    if (StrUtil.isNotBlank(results) && HttpStatus.OK.value() == responses.getStatus()) {
      log.info("定时任务{}启动成功。", jobId);
    } else {
      log.error("定时任务{}启动失败。", jobId);
      JSONObject jsonResponse = JSON.parseObject(results);
      throw new RuntimeException("定时任务启动失败：" + jsonResponse.get("msg"));

    }
  }

  //停止定时任务
  public void stopJob(Integer jobId) {
    //通过定时任务id调度任务停止接口
    HttpRequest requests = HttpRequest.post(stopJobUrl)
            .header("Content-Type", "multipart/form-data")
            .header("Cookie", getCookie())
            .form("id", jobId);
    //通过HTTP请求停止定时任务
    HttpResponse responses = requests.execute();
    String results = responses.body();
    if (StrUtil.isNotBlank(results) && HttpStatus.OK.value() == responses.getStatus()) {
      log.info("定时任务{}停止成功。", jobId);
    } else {
      log.error("定时任务{}停止失败。", jobId);
    }

  }

  public List<Long> selectJob(Integer jobGroup) {
    HttpRequest request = HttpRequest.post(pageListUrl).header("Content-Type", "multipart/form-data").header("Cookie", getCookie()).form("jobGroup", jobGroup).form("triggerStatus", 0).form("start", 0);
    HttpResponse response = request.execute();
    // 解析响应体
    ObjectMapper mapper = new ObjectMapper();
    List<Long> idList = new LinkedList<>();
    try {
      JsonNode responseNode = mapper.readTree(response.body());
      JsonNode dataNode = responseNode.get("data");
      //遍历删除id对应的定时任务
      if (dataNode.isArray()) {
        for (JsonNode node : dataNode) {
          Long id = node.get("id").asLong();
          idList.add(id);
        }
      }
    } catch (IOException e) {
      System.out.println("解析响应体时发生异常：" + e.getMessage());
    }
    return idList;
  }
//3.删除定时任务:
//Xxl-job目前没有没有直接批量进行删除定时任务的,所以我们使用遍历去挨个删除,如果考虑到性能问题,单独创建一个定时来调用该删除方法即可,每天凌晨去执行该删除清理的定时任务。
  public void removalJob(List<Long> idList) {
    for (Long id : idList) {
      HttpRequest requests = HttpRequest.post(removeUrl).header("Content-Type", "multipart/form-data").header("Cookie", getCookie()).form("id", id);
      //执行HTTP请求删除定时任务
      HttpResponse response = requests.execute();
      if (StrUtil.isNotBlank(response.body()) && HttpStatus.OK.value() == response.getStatus()) {
        log.info("定时任务{}删除成功。", id);
      } else {
        log.error("定时任务{}删除失败。", id);
      }
    }
  }

//下面是获取Cookie的方法
  public String getCookie() {
    for (int i = 0; i < 3; i++) {
      String cookieStr = loginCookie.get("XXL_JOB_LOGIN_IDENTITY");
      if (cookieStr != null) {
        return "XXL_JOB_LOGIN_IDENTITY=" + cookieStr;
      }
      login();
    }
    throw new RuntimeException("获取 xxl-job cookie 失败!");
  }

  //优先到MAP缓存中获取,如果没有获取到则会请求xxljob的登录来获取Cookie,这里提供三次失败可重试。
  public void login() {
    log.info("登录名：{}密码：{}",userName,password);
    HttpResponse response = HttpRequest.post(loginUrl)
            .form("userName", userName)
            .form("password", password)
            .execute();
    log.info("登录xxl-job 结果 {}", JSON.toJSONString(response));
    List<HttpCookie> cookies = response.getCookies();
    Optional<HttpCookie> cookieOpt = cookies.stream().filter(cookie -> cookie.getName().equals("XXL_JOB_LOGIN_IDENTITY")).findFirst();
    if (!cookieOpt.isPresent()) throw new RuntimeException("获取 xxl-job cookie 失败!");

    String value = cookieOpt.get().getValue();
    loginCookie.put("XXL_JOB_LOGIN_IDENTITY", value);
  }


}
