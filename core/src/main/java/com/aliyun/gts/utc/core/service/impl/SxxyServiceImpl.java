package com.aliyun.gts.utc.core.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.source.SxxySourceRequest;
import com.aliyun.gts.utc.common.constants.HttpConstant;
import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.core.utils.RedisUtils;
import com.aliyun.gts.utc.core.service.SxxyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: guoshengLi
 * @create: 2025-02-26 18:46
 * @Description:
 */
@Slf4j
@Service
public class SxxyServiceImpl  implements SxxyService {

  @Value("${third.sxxyUrl}")
  private String sxxyUrl;

  private final String  sxxyTokenKey = "sxxy:accesstoken" ;

  @Autowired
  RedisUtils redisUtils;

  /**
   *
   * 获取access_token
   * @param corpid 企业id
   * @param corpsecret 企业秘钥
   */
  @Override
  public String getToken(String corpid, String corpsecret) {
    Object keyValue = redisUtils.getKeyValue(sxxyTokenKey);
    if (keyValue != null){
      return keyValue.toString();
    }
    HttpRequest request = HttpRequest.post(sxxyUrl+"/gettoken")
            .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
            .form("corpid",corpid)
            .form("corpsecret",corpsecret);
    request.setConnectionTimeout(HttpConstant.CONNECTION_TIMEOUT);
    request.setReadTimeout(HttpConstant.READ_TIMEOUT);
    HttpResponse execute = request.execute();
    String result = execute.body();
    log.info("获取token结果：{}", result);
    if (StrUtil.isNotBlank(result) && HttpStatus.OK.value() == execute.getStatus()) {
      //定时任务创建成功后拿到任务id
      JSONObject jsonResponse = JSON.parseObject(result);
      String authToken = jsonResponse.getString("access_token");
      String expiresIn = jsonResponse.getString("expires_in");
      log.info("获取token结果：{}", authToken);
      if(StrUtil.isBlank(authToken) || StrUtil.isBlank(expiresIn)){
        log.error("获取token失败：{}", jsonResponse.get("errmsg"));
       return null;
      }
      redisUtils.setKeyValueWithTTL(sxxyTokenKey, authToken, Long.parseLong(expiresIn) - 30, TimeUnit.SECONDS);
      return authToken;
    }else {
      log.error("获取token失败：{}", result);
      return null;
    }
  }

  /**
   * 发送消息
   */
  @Override
  public Boolean sendMessage(UmmSource ummSource, UmmMsgSendDTO ummMsgSendDTO) {
    SxxySourceRequest sourceParamSxxy = JSONObject.parseObject(ummSource.getSourceParam(), SxxySourceRequest.class);
    Map<String, String> formDataJson = new HashMap<>();
    //成员ID
    formDataJson.put("touser", ummMsgSendDTO.getUserCode().toLowerCase());
    //消息类型
    formDataJson.put("msgtype", "text");
    //企业应用的id，整型。可在应用的设置页面查看
    formDataJson.put("agentid", sourceParamSxxy.getCorpId());
    //消息内容，最长不超过2048个字节
    Map<String, String> text = new HashMap<>();
    text.put("content", ummMsgSendDTO.getMsgContent());
    formDataJson.put("text", JSONObject.toJSONString(text));
    String gettoken = getToken(sourceParamSxxy.getCorpId(), sourceParamSxxy.getCorpSecret());
    if(StrUtil.isBlank(gettoken)){
      return false;
    }
    String sendUrl = sxxyUrl+"/message/send" + "?access_token=" + gettoken;
    HttpRequest request = HttpRequest.post(sendUrl)
            .header("Content-Type", "application/json")
            .body(JSONObject.toJSONString(formDataJson));
    request.setConnectionTimeout(HttpConstant.CONNECTION_TIMEOUT);
    request.setReadTimeout(HttpConstant.READ_TIMEOUT);
    HttpResponse execute = request.execute();
    String result = execute.body();
    log.info("获取token结果：{}", result);
    if (StrUtil.isNotBlank(result) && HttpStatus.OK.value() == execute.getStatus()) {
      JSONObject jsonResponse = JSON.parseObject(result);
      String errcode = jsonResponse.get("errcode").toString();
      if ("0".equals(errcode)){
        return true;
      }else {
        log.error("发送消息失败：{}", jsonResponse.get("errmsg"));
        return false;
      }
    }
    return true;
  }
}
