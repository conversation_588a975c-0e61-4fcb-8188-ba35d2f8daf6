package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.core.repository.UmmMsgParameterRepository;
import com.aliyun.gts.utc.core.service.UmmMsgParameterService;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgParameterDO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UmmMsgParameterServiceImpl implements UmmMsgParameterService {

    private final UmmMsgParameterRepository ummMsgParameterRepository;

    public UmmMsgParameterServiceImpl(UmmMsgParameterRepository ummMsgParameterRepository) {
        this.ummMsgParameterRepository = ummMsgParameterRepository;
    }

    @Override
    public List<UmmMsgParameterDO> query() {
        return ummMsgParameterRepository.query();
    }
}
