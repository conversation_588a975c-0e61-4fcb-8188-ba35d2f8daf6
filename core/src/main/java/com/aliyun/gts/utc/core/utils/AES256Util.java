package com.aliyun.gts.utc.core.utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class AES256Util {
    public static final String KEY = "z2uvH26DIJ7R4DLXQUyzmko2UVZt3EJo";
    private static final String AES = "AES";
    private static final byte[] KEY_VI = "58raCZ8PI610UPNj".getBytes();
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";
    private static Base64.Encoder base64Encoder = Base64.getEncoder();
    private static Base64.Decoder base64Decoder = Base64.getDecoder();

    public AES256Util() {
    }

    public static String encryptAes(String content) {
        try {
            SecretKey secretKey = new SecretKeySpec(KEY.getBytes(), AES);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(1, secretKey, new IvParameterSpec(KEY_VI));
            byte[] byteEncode = content.getBytes(StandardCharsets.UTF_8);
            byte[] byteAES = cipher.doFinal(byteEncode);
            return base64Encoder.encodeToString(byteAES);
        } catch (Exception var6) {
            var6.printStackTrace();
            return null;
        }
    }

    public static String decryptAES(String content) {
        try {
            SecretKey secretKey = new SecretKeySpec(KEY.getBytes(), AES);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(2, secretKey, new IvParameterSpec(KEY_VI));
            byte[] byteContent = base64Decoder.decode(content);
            byte[] byteDecode = cipher.doFinal(byteContent);
            return new String(byteDecode, StandardCharsets.UTF_8);
        } catch (Exception var6) {
            var6.printStackTrace();
            return null;
        }
    }
}
