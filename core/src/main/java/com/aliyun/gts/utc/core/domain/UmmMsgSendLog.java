/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.domain;

import lombok.Data;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendLog.java
 * source table name: umm_msg_send_log
 * initial code lines: 63
 * initial generated date: 2021-06-03 22:51:27
 *
 * @date 2021-06-03 22:51:27
 */
@Data
public class UmmMsgSendLog {

    /**
    * 主键id
    */
    private Long id;

    /**
    * 消息发送数据id
    */
    private Long msgSendId;

    /**
    * 消息原文
    */
    private String messageIn;

    /**
    * 返回的内容
    */
    private String messageOut;

    /**
    * 发送时间
    */
    private Date sendTime;

    /**
    * 3.发送成功，4.发送失败
    */
    private Integer status;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModify;

    private String tenantCode;

}