package com.aliyun.gts.utc.core.constants;

public enum ErrorCodeEnum {
    SUCCESS(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.INFO, "0000", "successful"),
    UNKNOWN(ErrorTypeEnum.SYSTEM, ErrorLevelEnum.ERROR, "0001", "unknown error"),
    THIRD_PARTY_ERROR(ErrorTypeEnum.SYSTEM, ErrorLevelEnum.ERROR, "0002", "third party error"),
    PARAM_ILLEGAL(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.WARN, "0004", "parameter illegal"),
    UNKNOWN_ERROR(ErrorTypeEnum.SYSTEM, ErrorLevelEnum.ERROR, "10000", "unknown error"),
    MISSINGPARAMETER_ERROR(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.WARN, "10001", "missing parameter error : "),
    HTTPMESSAGENOTREADABLE_ERROR(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.WARN, "10002", "参数不正确"),
    ARGUMENTNOTVALID_ERROR(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.WARN, "10003", "method argument is not valid : "),
    PARAMETERBIND_ERROR(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.WARN, "10004", "bind parameter error : "),
    CONSTRAINTVIOLATION_ERROR(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.WARN, "10005", "constraint violation error : "),
    VALIDATION_ERROR(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.WARN, "10006", "validation error"),
    UPLOAD_ERROR(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.WARN, "10007", "文件大小超过系统限制"),
    DATABASE_ERROR(ErrorTypeEnum.SYSTEM, ErrorLevelEnum.ERROR, "10008", "数据库错误");

    private static final String SYSTEM = "001";
    private final ErrorTypeEnum type;
    private final ErrorLevelEnum level;
    private final String detailCode;
    private final String description;

    private ErrorCodeEnum(ErrorTypeEnum type, ErrorLevelEnum level, String detailCode, String description) {
        this.type = type;
        this.level = level;
        this.detailCode = detailCode;
        this.description = description;
    }

    public ErrorTypeEnum getType() {
        return this.type;
    }

    public ErrorLevelEnum getLevel() {
        return this.level;
    }

    public String getSystem() {
        return "001";
    }

    public String getDetailCode() {
        return this.detailCode;
    }

    public String getDescription() {
        return this.description;
    }

    public String toCode() {
        return String.format("%d%d%s%s", this.getType().getCode(), this.getLevel().getCode(), this.getSystem(), this.getDetailCode());
    }

    public static enum ErrorTypeEnum {
        BUSINESS(1),
        SYSTEM(3),
        THIRD_PARTY(5);

        private final int code;

        private ErrorTypeEnum(int code) {
            this.code = code;
        }

        public int getCode() {
            return this.code;
        }
    }

    public static enum ErrorLevelEnum {
        INFO(1),
        WARN(3),
        ERROR(5);

        private final int code;

        private ErrorLevelEnum(int code) {
            this.code = code;
        }

        public int getCode() {
            return this.code;
        }
    }
}
