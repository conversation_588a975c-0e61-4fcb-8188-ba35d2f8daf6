package com.aliyun.gts.utc.core.service.impl;


import com.aliyun.gts.utc.api.dto.CustomParamDTO;
import com.aliyun.gts.utc.api.dto.ReceiverDTO;
import com.aliyun.gts.utc.api.dto.SourceDTO;
import com.aliyun.gts.utc.api.dto.UmmTemplateDTO;
import com.aliyun.gts.utc.api.error.ErrorCodeEnum;
import com.aliyun.gts.utc.api.facade.request.template.CreateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.DeleteMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.PageQueryMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.UpdateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.response.template.*;
import com.aliyun.gts.utc.common.constants.MessageTemplateConstant;
import com.aliyun.gts.utc.common.enums.UmmMsgStatusEnum;
import com.aliyun.gts.utc.core.domain.*;
import com.aliyun.gts.utc.core.exceptions.BizException;
import com.aliyun.gts.utc.core.factory.MessageTemplateFactory;
import com.aliyun.gts.utc.core.repository.*;
import com.aliyun.gts.utc.core.service.MessageTemplateManageService;
import com.aliyun.gts.utc.core.utils.EnvUtils;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dto.MessageTemplateQueryDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageTemplateDTO;
import com.aliyun.gts.utc.integration.client.MessageClientTypeEnum;
import com.aliyun.gts.utc.integration.client.MessageClientUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.sdk.service.UserContext;
import io.jsonwebtoken.lang.Assert;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 消息模板服务层
 */
@Service
public class MessageTemplateManageServiceImpl implements MessageTemplateManageService {

    private final UmmTemplateRepository ummTemplateRepository;

    private final UmmCategoryRepository ummCategoryRepository;

    private final UmmMsgParameterRepository ummMsgParameterRepository;

    private final UmmMessageRepository ummMessageRepository;

    private final UmmSourceRepository ummSourceRepository;

    @Autowired
    private UserContext userContext;


    public MessageTemplateManageServiceImpl(UmmTemplateRepository ummTemplateRepository, UmmCategoryRepository ummCategoryRepository, UmmMsgParameterRepository ummMsgParameterRepository, UmmMessageRepository ummMessageRepository,UmmSourceRepository ummSourceRepository) {
        this.ummTemplateRepository = ummTemplateRepository;
        this.ummCategoryRepository = ummCategoryRepository;
        this.ummMsgParameterRepository = ummMsgParameterRepository;
        this.ummMessageRepository = ummMessageRepository;
        this.ummSourceRepository = ummSourceRepository;
    }


    /**
     * 新增消息模板
     *
     * @param request 新增消息模板请求体
     * @return 新增消息模板返回体
     */
    @Override
    public CreateMessageTemplateResponse add(CreateMessageTemplateRequest request) {
        //校验参数名称是否重复，是否与系统参数重复
        if (null != request.getParamDTO()) {
            checkParameter(request.getParamDTO().getCustomParamList());
        }
        checkTitleExist(request.getMsgTitle(), null);
        UmmTemplateDTO ummTemplateDTO = MessageTemplateFactory.createTemplate(request);

        UmmTemplate ummTemplate = UmmTemplateDTOConverter.INSTANCE.convert(ummTemplateDTO);
        if (EnvUtils.isMockUser()) {
            ummTemplate.setCreateUserGuid(RuntimeContextUtil.getUserGuid());
            ummTemplate.setCreateUserName(RuntimeContextUtil.getUserName());
            ummTemplate.setUpdateUserGuid(RuntimeContextUtil.getUserGuid());
            ummTemplate.setUpdateUserName(RuntimeContextUtil.getUserName());
        } else {
            UserInfoDTO currentUser = userContext.currentUserInfo(false);
            ummTemplate.setCreateUserGuid(currentUser.getBaseInfo().getUserCode());
            ummTemplate.setCreateUserName(currentUser.getBaseInfo().getUserName());
            ummTemplate.setUpdateUserGuid(currentUser.getBaseInfo().getUserCode());
            ummTemplate.setUpdateUserName(currentUser.getBaseInfo().getUserName());
        }
        UmmTemplate add = ummTemplateRepository.add(ummTemplate);

        CreateMessageTemplateResponse response = new CreateMessageTemplateResponse();
        response.setId(add.getId());

        return response;
    }

    @Override
    public Boolean checkTplCode(String tplCode) {
        return ummTemplateRepository.checkTplCode(tplCode);
    }

    /**
     * 编辑消息模板
     *
     * @param request 请求体
     * @return 编辑结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdateMessageTemplateResponse modify(UpdateMessageTemplateRequest request) {
        //校验模板是否存在
        checkMessageTemplateExist(request.getId());
        //校验消息标题是否重复
        checkTitleExist(request.getMsgTitle(), request.getId());
        //校验参数名称是否重复，是否与系统参数重复
        if (null != request.getParamDTO()) {
            checkParameter(request.getParamDTO().getCustomParamList());
        }
        //校验栏目是否有权限
        ummCategoryRepository.checkCategoryExistInCurrentTenantCode(request.getCategoryId());

        UmmTemplateDTO ummTemplateDTO = MessageTemplateFactory.createUpdateTemplate(request);

        UmmTemplate ummTemplate = UmmTemplateDTOConverter.INSTANCE.convert(ummTemplateDTO);
        if (EnvUtils.isMockUser()) {
            ummTemplate.setUpdateUserGuid(RuntimeContextUtil.getUserGuid());
            ummTemplate.setUpdateUserName(RuntimeContextUtil.getUserName());
        } else {
            UserInfoDTO currentUser = userContext.currentUserInfo(false);
            ummTemplate.setUpdateUserGuid(currentUser.getBaseInfo().getUserCode());
            ummTemplate.setUpdateUserName(currentUser.getBaseInfo().getUserName());
        }

        UpdateMessageTemplateResponse response = new UpdateMessageTemplateResponse();
        response.setResult(ummTemplateRepository.update(ummTemplate));
        return response;
    }

    private void checkTitleExist(String msgTitle, Long msgId) {
        if (ummTemplateRepository.countTitle(msgTitle, msgId) > 0) {
            throw new BizException("消息标题已存在");
        }
    }


    private void checkParameter(List<CustomParamDTO> customParamList) {
        if (null == customParamList || customParamList.isEmpty()) {
            return;
        }
        List<UmmMsgParameter> parameterList = ummMsgParameterRepository.getAll();
        List<String> alreadyExist = parameterList.stream().map(UmmMsgParameter::getParamName).collect(Collectors.toList());
        for (CustomParamDTO customParamDTO : customParamList) {
            if (alreadyExist.contains(customParamDTO.getName())) {
                throw new BizException("参数名称重复");
            } else {
                alreadyExist.add(customParamDTO.getName());
            }
        }
    }

    private void checkMessageTemplateExist(Long id) {
        UmmTemplate ummTemplate = ummTemplateRepository.getById(id);
        if (null == ummTemplate || MessageTemplateConstant.NOT_DELETED != ummTemplate.getIsDeleted() || !Objects.equals(ummTemplate.getTenantCode(), RuntimeContextUtil.getTenantCode())) {
            throw new BizException("消息模板不存在");
        }
    }

    /**
     * 查询消息模板详情
     *
     * @param id 消息模板id
     * @return 消息模板详情
     */
    @Override
    public QueryMessageTemplateResponse detail(Long id) {
        QueryMessageTemplateResponse response;
        UmmTemplate ummTemplate = ummTemplateRepository.getById(id);
        checkMessageTemplateExist(id);

        UmmCategory ummCategory = ummCategoryRepository.getById(ummTemplate.getCategoryId());
        response = MessageTemplateFactory.createTemplateDetailResponse(ummTemplate);
        if (ummTemplate.getSourceId() != null) {
            UmmSource ummSource = ummSourceRepository.getById(ummTemplate.getSourceId());
            if (ummSource != null) {
                SourceDTO source = new SourceDTO();
                source.setLabel(ummSource.getSourceName());
                source.setValue(ummSource.getId());
                response.setSource(source);
            }
        }
        if (null != response.getChannelId()) {
            response.setChannelName(MessageClientUtils.getById(response.getChannelId()).getClientName());
        }
        if (null != ummCategory) {
            response.setCategoryId(ummCategory.getId());
            response.setCategoryName(ummCategory.getCategoryName());
        }
        if (null == response.getReceiverGuidList() && CollectionUtils.isNotEmpty(response.getReceiverList())) {
            List<String> receiverGuidList = response.getReceiverList()
                    .stream().map(ReceiverDTO::getId).collect(Collectors.toList());
            response.setReceiverGuidList(receiverGuidList);
        }
        return response;
    }

    /**
     * 批量删除消息模板
     *
     * @param request 请求体，消息模板id列表
     * @return 删除结果
     */
    @Override
    public DeleteMessageTemplateResponse delete(DeleteMessageTemplateRequest request) {
        Assert.notNull(request, ErrorCodeEnum.PARAM_ILLEGAL.getDescription());
        //校验模版是否存在有权限
        List<Long> idList = request.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            throw new BizException(ErrorCodeEnum.PARAM_ILLEGAL.getDetailCode(), ErrorCodeEnum.PARAM_ILLEGAL.getDescription());
        }
        idList.parallelStream().forEach(this::checkMessageTemplateExist);

        idList.parallelStream().forEach(this::checkExistNotSuccessMsgInTemplate);

        DeleteMessageTemplateResponse response = new DeleteMessageTemplateResponse();
        response.setResult(ummTemplateRepository.batchDelete(idList));
        return response;
    }

    /**
     * 分页查询消息模板
     *
     * @param request 请求体
     * @return 分页后的消息模板列表
     */
    @Override
    public Page<PageQueryMessageTemplateResponse> pageQuery(PageQueryMessageTemplateRequest request) {
        Page<PageMessageTemplateDTO> page = new Page<>(request.getPageNum(), request.getPageSize());
        List<Long> categoryIdList = request.getCategoryIdList();
        Map<Long, String> channelMap = MessageClientTypeEnum.getClientIdToClientNameMap();
        if (null != categoryIdList && categoryIdList.size() > 0) {
            Long categoryParentId = request.getCategoryIdList().get(0);
            List<UmmCategory> categoryList = ummCategoryRepository.getByParentId(categoryParentId);
            List<Long> newCategoryIdList = categoryList.stream().map(UmmCategory::getId).collect(Collectors.toList());
            if (newCategoryIdList.size() > 0) {
                categoryIdList.addAll(newCategoryIdList);
            }
            request.setCategoryIdList(categoryIdList);
        }
        MessageTemplateQueryDTO query = MessageTemplateFactory.createQuery(request);
        page = ummTemplateRepository.pageQuery(page, query);
        return MessageTemplateFactory.createQueryResponse(page, channelMap);
    }

    /**
     * 获取告警通知消息模板列表
     *
     * @return 消息模板列表
     */
    @Override
    public List<GetWarningListResponse> getWarningList(Long id) {
        ummCategoryRepository.checkCategoryExistInCurrentTenantCode(id);

        return ummTemplateRepository.getByCategoryId(id).stream().map(o -> {
            GetWarningListResponse response = new GetWarningListResponse();
            response.setGuid(o.getGuid());
            response.setId(o.getId());
            response.setTitle(o.getTplTitle());
            response.setCode(o.getTplCode());
            return response;
        }).collect(Collectors.toList());
    }

    @Mapper
    interface UmmTemplateDTOConverter {
        UmmTemplateDTOConverter INSTANCE = Mappers.getMapper(UmmTemplateDTOConverter.class);

        UmmTemplate convert(UmmTemplateDTO ummTemplateDTO);

        UmmTemplateDTO convert(UmmTemplate ummTemplate);
    }

    private void checkExistNotSuccessMsgInTemplate(Long id) {
        List<UmmMessage> allMsgList= ummMessageRepository.getByTplId(id);
        if (CollectionUtils.isNotEmpty(allMsgList) && allMsgList.stream().anyMatch(item -> !UmmMsgStatusEnum.SEND_ALL_SUCESS.getStatus().equals(item.getStatus()))) {
            throw new RuntimeException("模版下存在未完成的消息，不能删除");
        }
    }
}
