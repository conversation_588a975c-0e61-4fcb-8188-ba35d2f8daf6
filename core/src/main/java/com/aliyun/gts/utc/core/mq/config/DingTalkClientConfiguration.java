package com.aliyun.gts.utc.core.mq.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/9/1
 **/

@Data
@Component
@ConfigurationProperties(prefix = "dingtalk")
public class DingTalkClientConfiguration {
    private String domainName = "openplatform.dg-work.cn";
    private String accessKey;
    private String secretKey;
    private String protocol = "https";
    /**
     * 钉钉用户code需要转用户ID?
     */
    private Boolean codeConvertId = Boolean.TRUE;
}
