package com.aliyun.gts.utc.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.dto.MailHostPropsDTO;
import com.aliyun.gts.utc.api.facade.request.MailMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.source.MailSourceRequest;
import com.aliyun.gts.utc.application.impl.MailMessageSenderService;
import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.repository.UmmSourceRepository;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.aliyun.gts.utc.core.utils.AES256Util;
import com.aliyun.gts.utc.core.utils.MailUtils;
import com.aliyun.gts.utc.integration.client.MessageClientTypeEnum;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import java.util.Map;

import static com.aliyun.gts.utc.common.constants.StatusConstants.*;

@Service
@Slf4j
public class MailNoticeMsgListenerServiceImpl extends AbstractSendMsgListenerService {
    @Autowired
    private UmmTemplateRepository ummTemplateRepository;

    @Autowired
    private MailMessageSenderService messageSenderService;

    @Autowired
    private UmmSourceRepository ummSourceRepository;

    @Override
    public Long getChannelId() {
        return MessageClientTypeEnum.MAIL.getClientID();
    }

    @Override
    protected boolean notice(UmmMsgSendDTO ummMsgSendDTO, Map<String, Object> extend) {
        if(Strings.isNullOrEmpty(ummMsgSendDTO.getEmail())) {
            log.info("用户邮箱为空，跳过发送步骤。用户id："+ummMsgSendDTO.getUserCode()+"，用户姓名："+ummMsgSendDTO.getUserName());
            return false;
        }
        if (!MailUtils.isValidEmail(ummMsgSendDTO.getEmail())) {
            log.info("用户邮箱非法，跳过发送步骤。用户id："+ummMsgSendDTO.getUserCode()
                    +"，用户姓名："+ummMsgSendDTO.getUserName()+"，用户邮箱："+ummMsgSendDTO.getEmail());
            return false;
        }
        UmmTemplate ummTemplate = ummTemplateRepository.getById((Long) extend.get(TEMPLATE_KEY));
        if (ummTemplate != null && ummTemplate.getSourceId() != null) {
            UmmSource ummSource = ummSourceRepository.getById(ummTemplate.getSourceId());
            if(ummSource != null){
                MailMessageSendRequest mailMessageSendRequest = constructMailMessageSendRequest(ummSource);
                JavaMailSender javaMailSender = messageSenderService.customizeMailSender(mailMessageSendRequest);
                //使用自定义sender发送邮件
                mailMessageSendRequest.setContent(ummMsgSendDTO.getMsgContent());
                mailMessageSendRequest.setEmail(ummMsgSendDTO.getEmail());
                mailMessageSendRequest.setSubject(ummMsgSendDTO.getMsgTitle());
                mailMessageSendRequest.setHtmlContent(false);
                return messageSenderService.sendCustomizeMailSenderMail(javaMailSender, mailMessageSendRequest);
            }
        }
        return false;
    }

    @Override
    public void noticeRollback(Object obj, Map<String, Object> extend) {

    }

    private MailMessageSendRequest constructMailMessageSendRequest(UmmSource ummSource) {
        MailSourceRequest emailSourceParam = JSON.parseObject(ummSource.getSourceParam(), MailSourceRequest.class);
        MailMessageSendRequest mailMessageSendRequest = new MailMessageSendRequest();
        MailHostPropsDTO mailHostPropsDTO = new MailHostPropsDTO();
        mailHostPropsDTO.setHost(emailSourceParam.getMailHost());
        mailHostPropsDTO.setProtocol("smtp");
        mailHostPropsDTO.setUsername(emailSourceParam.getMailUserName());
        mailHostPropsDTO.setPassword(AES256Util.decryptAES(emailSourceParam.getMailPassWord()));
        mailHostPropsDTO.setPort(emailSourceParam.getMailPort() == null ? 465 : emailSourceParam.getMailPort());
        mailHostPropsDTO.setSslEnable(emailSourceParam.getMailSSL() == null || emailSourceParam.getMailSSL());
        mailHostPropsDTO.setSubmitter(emailSourceParam.getSubmitter());
        mailMessageSendRequest.setMailHostProps(mailHostPropsDTO);
        return mailMessageSendRequest;
    }
}
