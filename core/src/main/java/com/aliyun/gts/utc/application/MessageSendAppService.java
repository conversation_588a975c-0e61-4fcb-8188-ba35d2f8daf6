package com.aliyun.gts.utc.application;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.facade.request.MailMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.SmsMessageSendRequest;

import java.util.List;

/**
 * @description: 消息发送
 * @author: zhangningning
 * @create: 2021/6/2
 * @version: 1.0
 **/
public interface MessageSendAppService {


    void sendMessage(long msgId, Long tenantId, Long tplId,Boolean isRealTime);

    /**
     * 发送邮件
     *
     * @param request
     * @return
     */
    String sendEmail(MailMessageSendRequest request);

    /**
     * 发送短信
     *
     * @param request
     * @return
     */
    String sendSms(SmsMessageSendRequest request);

    Boolean sendMessageByMsgSend(List<UmmMsgSendDTO> ummMsgSendDTOList);

}
