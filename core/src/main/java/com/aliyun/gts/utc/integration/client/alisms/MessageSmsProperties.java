package com.aliyun.gts.utc.integration.client.alisms;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "msg.client.sms")
public class MessageSmsProperties {
    /**
     * 短信平台,可选值为 ali
     */
    private String plat;
    /**
     * accesskey
     */
    private String akid;
    /**
     * accessKey sercite
     */
    private String aksk;
    /**
     * 签名
     */
    private String sign;

    /**
     * 阿里短信平台
     */
    public static String PLAT_ALI = "ali";

    public String getPlat() {
        return plat;
    }

    public MessageSmsProperties setPlat(String plat) {
        this.plat = plat;
        return this;
    }

    public String getAkid() {
        return akid;
    }

    public MessageSmsProperties setAkid(String akid) {
        this.akid = akid;
        return this;
    }

    public String getAksk() {
        return aksk;
    }

    public MessageSmsProperties setAksk(String aksk) {
        this.aksk = aksk;
        return this;
    }

    public String getSign() {
        return sign;
    }

    public MessageSmsProperties setSign(String sign) {
        this.sign = sign;
        return this;
    }
}
