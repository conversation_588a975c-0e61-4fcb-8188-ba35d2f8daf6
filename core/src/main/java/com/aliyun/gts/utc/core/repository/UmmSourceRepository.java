package com.aliyun.gts.utc.core.repository;

import com.aliyun.gts.utc.api.dto.UmmSourceDTO;
import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.dal.dto.MessageSourceQueryDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
public interface UmmSourceRepository {
    UmmSource add(UmmSource ummSource);

    int countSourceName(String sourceName);

    Boolean checkSourceCode(String sourceCode);

    UmmSource getBySourceCode(String sourceCode);

    Boolean update(UmmSource ummSource);

    UmmSource getById(Long id);

    Boolean batchDelete(List<Long> idList);

    Page<UmmSourceDTO> pageQuery(Page<UmmSourceDTO> page, MessageSourceQueryDTO query);

    List<UmmSource> getByRange(int i, int i1);
}

