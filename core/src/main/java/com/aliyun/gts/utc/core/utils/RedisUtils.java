package com.aliyun.gts.utc.core.utils;

/**
 * @author: guosheng<PERSON>i
 * @create: 2025-02-28 11:24
 * @Description:
 */
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RedisUtils {

  @Autowired
  private RedissonClient redissonClient;

  /**
   * 存储键值对
   * @param key 键
   * @param value 值
   */
  public void setKeyValue(String key, Object value) {
    RBucket<Object> bucket = redissonClient.getBucket(key);
    bucket.set(value);
  }

  /**
   * 存储键值对并设置过期时间（支持任意时间单位）
   * @param key 键
   * @param value 值
   * @param ttl 过期时间
   * @param timeUnit 时间单位（秒、毫秒等）
   */
  public void setKeyValueWithTTL(String key, Object value, long ttl, java.util.concurrent.TimeUnit timeUnit) {
    RBucket<Object> bucket = redissonClient.getBucket(key);
    bucket.set(value, ttl, timeUnit); // 设置值并指定过期时间
  }

  /**
   * 获取键对应的值
   * @param key 键
   * @return 值
   */
  public Object getKeyValue(String key) {
    RBucket<Object> bucket = redissonClient.getBucket(key);
    return bucket.get(); // 如果键不存在，返回 null
  }
}
