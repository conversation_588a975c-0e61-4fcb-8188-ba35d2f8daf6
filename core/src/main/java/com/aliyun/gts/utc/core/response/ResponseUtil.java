package com.aliyun.gts.utc.core.response;

import com.aliyun.gts.utc.api.base.Page;
import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;

public class ResponseUtil {
    public ResponseUtil() {
    }

    public static <T> ResultObject<T> getResult() {
        return new ResultObject();
    }

    public static <T> ResultObject<T> getResult(String returnCode, String returnMsg, Boolean success) {
        return (new ResultObject()).setErrCode(returnCode).setErrMsg(returnMsg).setSuccess(success);
    }

    public static <T> ResultObject<T> getResult(String returnCode, String returnMsg, T data, Boolean success) {
        return (new ResultObject()).setErrCode(returnCode).setErrMsg(returnMsg).setData(data).setSuccess(success);
    }

    public static <T> ResultObject<T> getSuccessResult(T data) {
        return (new ResultObject()).setErrCode("0").setErrMsg("success").setData(data).setSuccess(Boolean.TRUE);
    }

    public static <T> ResultObject<T> getFailureResult(T data) {
        return (new ResultObject()).setErrCode("-1").setErrMsg("failure").setData(data).setSuccess(Boolean.FALSE);
    }

    public static <T> ResultObject<T> getFailureResult(String errorMsg) {
        return (new ResultObject()).setErrCode("-1").setErrMsg(errorMsg).setSuccess(Boolean.FALSE);
    }

    public static Object convertResult(Object source) {
        if (source instanceof ResultObject) {
            return source;
        } else {
            Page pageInfo;
            if (source instanceof com.github.pagehelper.Page) {
                com.github.pagehelper.Page<?> page = (com.github.pagehelper.Page)source;
                pageInfo = new Page();
                pageInfo.setPageNum((long)page.getPageNum());
                pageInfo.setPageSize((long)page.getPageSize());
                pageInfo.setTotalCount(page.getTotal());
                pageInfo.setList(page.getResult());
                return getSuccessResult(pageInfo);
            } else if (source instanceof IPage) {
                IPage<?> page = (IPage)source;
                pageInfo = new Page();
                pageInfo.setPageNum(page.getCurrent());
                pageInfo.setPageSize(page.getSize());
                pageInfo.setTotalCount(page.getTotal());
                pageInfo.setList(page.getRecords());
                return getSuccessResult(pageInfo);
            } else {
                return source instanceof ResultResponse ? source : getSuccessResult(source);
            }
        }
    }
}