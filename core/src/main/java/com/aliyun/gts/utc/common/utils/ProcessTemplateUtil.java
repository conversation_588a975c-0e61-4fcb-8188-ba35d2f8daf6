package com.aliyun.gts.utc.common.utils;


import freemarker.cache.StringTemplateLoader;
import freemarker.core.TemplateClassResolver;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;
import java.util.UUID;

public class ProcessTemplateUtil {


    /**
     * 配置 freemarker configuration
     *
     * @return
     */
    private static Configuration configuration() {
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
        StringTemplateLoader templateLoader = new StringTemplateLoader();
        configuration.setTemplateLoader(templateLoader);
        configuration.setDefaultEncoding("UTF-8");
        configuration.setNewBuiltinClassResolver(TemplateClassResolver.SAFER_RESOLVER);
        return configuration;
    }

    /**
     * 解析模板
     *
     * @param templateValue
     * @param root
     * @throws IOException
     * @throws TemplateException
     */
    public static String processTemplate(String templateValue,Map<String, Object> root) throws IOException, TemplateException {
        StringWriter stringWriter = new StringWriter();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        Template template = new Template(uuid, templateValue, configuration());
        template.process(root, stringWriter);
        return stringWriter.toString();
    }
}
