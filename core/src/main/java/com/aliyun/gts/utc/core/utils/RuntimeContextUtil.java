package com.aliyun.gts.utc.core.utils;

import com.aliyun.gts.utc.core.base.enums.AdminTypeEnum;
import com.aliyun.gts.utc.core.context.RuntimeContext;
import com.yunqiao.foura.service.model.UserInfoDTO;
import org.apache.commons.lang3.StringUtils;

public class RuntimeContextUtil {
    public static String X_TENANT_CODE = "X-TENANT-CODE";
    public RuntimeContextUtil() {
    }

    public static UserInfoDTO getUserInfo() {
        return RuntimeContext.get().getUserInfo();
    }

    public static Long getUserId() {
        UserInfoDTO userInfo = getUserInfo();
        return null != userInfo ? userInfo.getBaseInfo().getId() : null;
    }

    public static String getUserGuid() {
        UserInfoDTO userInfo = getUserInfo();
        return null != userInfo ? userInfo.getBaseInfo().getUserId() : null;
    }

    public static String getUserCode() {
        UserInfoDTO userInfo = getUserInfo();
        return null != userInfo ? userInfo.getBaseInfo().getUserCode() : null;
    }

    public static String getUserName() {
        UserInfoDTO userInfo = getUserInfo();
        return null != userInfo ? userInfo.getBaseInfo().getUserName() : null;
    }

    public static Boolean isSuperAdmin() {
        AdminTypeEnum adminType = getAdminType();
        return AdminTypeEnum.SUPER_ADMIN.equals(adminType);
    }

    public static AdminTypeEnum getAdminType() {
        UserInfoDTO userInfo = getUserInfo();
        return userInfo == null ? AdminTypeEnum.NORMAL_USER : AdminTypeEnum.getByType(userInfo.getBaseInfo().getAdminType());
    }

    public static String getTenantCode() {
        String tenantCode = (String)RuntimeContext.getMap().get(X_TENANT_CODE);
        return StringUtils.isNotBlank(tenantCode) ? tenantCode : "default";
    }

    public static String getAppId() {
        return (String)RuntimeContext.getMap().get("appId");
    }

    public static void setTenantCode(String tenantCode) {
        RuntimeContext.set(X_TENANT_CODE, tenantCode);
    }
}
