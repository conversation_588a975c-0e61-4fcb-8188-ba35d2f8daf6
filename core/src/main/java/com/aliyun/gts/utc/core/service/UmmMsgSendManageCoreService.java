/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsend.PageQueryUmmMsgSendRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsend.*;

import java.util.List;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendManageAppService.java
 * source table name: umm_msg_send
 * initial code lines: 67
 * initial generated date: 2021-06-03 22:51:27
 *
 * @date 2021-06-03 22:51:27
 */
public interface UmmMsgSendManageCoreService {

    /**
     * 创建UmmMsgSend
     *
     * @param ummMsgSendDTO 创建$UmmMsgSend请求
     * @return 创建UmmMsgSend响应
     */
    CreateUmmMsgSendResponse createUmmMsgSend(UmmMsgSendDTO ummMsgSendDTO);

    /**
     * 批量创建UmmMsgSend
     *
     * @param ummMsgSendDTOS 创建$UmmMsgSend请求
     * @return 创建UmmMsgSend响应
     */
    Boolean batchCreateUmmMsgSend(List<UmmMsgSendDTO> ummMsgSendDTOS);
    /**
     * 批量修改UmmMsgSend
     *
     * @param ummMsgSendDTOS 创建$UmmMsgSend请求
     * @return 创建UmmMsgSend响应
     */
    Boolean batchUpdateUmmMsgSend(List<UmmMsgSendDTO> ummMsgSendDTOS);

    /**
     * 查询UmmMsgSend
     *
     * @param id 查询UmmMsgSend请求
     * @return 查询UmmMsgSend响应
     */
    QueryUmmMsgSendResponse queryUmmMsgSend(Long id);

    List<UmmMsgSendDTO> queryUmmMsgSendByMsgId(String msgId);

    /**
     * 分页查询UmmMsgSend
     *
     * @param pageQueryUmmMsgSendRequest 查询UmmMsgSend请求
     * @return UmmMsgSend查询结果
     */
    PageQueryUmmMsgSendResponse pageQueryUmmMsgSend(PageQueryUmmMsgSendRequest pageQueryUmmMsgSendRequest);

    /**
     * 删除UmmMsgSend
     *
     * @param id 删除UmmMsgSend请求
     * @return UmmMsgSend删除结果
     */
    DeleteUmmMsgSendResponse deleteUmmMsgSend(Long id);

    /**
     * 更新UmmMsgSend
     *
     * @param ummMsgSendDTO 更新UmmMsgSend请求
     * @return UmmMsgSend更新结果
     */
    UpdateUmmMsgSendResponse updateUmmMsgSend(UmmMsgSendDTO ummMsgSendDTO);

    Boolean batchUpdateUmmMsgSendStatus(List<Long> sendMsgIds,Integer status);

}
