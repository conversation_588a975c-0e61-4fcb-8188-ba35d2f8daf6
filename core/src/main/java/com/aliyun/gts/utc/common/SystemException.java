package com.aliyun.gts.utc.common;

import com.aliyun.gts.utc.api.error.ErrorCodeEnum;

/**
 * 系统异常定义
 */
public class SystemException extends RuntimeException {
    private final ErrorCodeEnum errorCode;

    public SystemException(ErrorCodeEnum errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
    }

    public SystemException(ErrorCodeEnum errorCode, Throwable t) {
        super(t);
        this.errorCode = errorCode;
    }

    /**
     * Getter method for property <tt>errorCode</tt>.
     *
     * @return property value of errorCode
     */
    public ErrorCodeEnum getErrorCode() {
        return errorCode;
    }
}