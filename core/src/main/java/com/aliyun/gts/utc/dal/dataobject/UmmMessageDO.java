/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dataobject;

import com.aliyun.gts.utc.core.dataobject.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessageDO.java
 * source table name: umm_message
 * initial code lines: 161
 * initial generated date: 2021-06-05 14:10:07
 *
 * @date 2021-06-05 14:10:07
 */
@Data
@TableName("umm_message")
public class UmmMessageDO extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模板id
     */
    @TableField("tpl_id")
    private Long tplId;

    /**
    *
    */
    @TableField("guid")
    private String guid;

    /**
    * 消息标题
    */
    @TableField("msg_title")
    private String msgTitle;

    /**
    * 消息内容
    */
    @TableField("msg_content")
    private String msgContent;

    /**
    * 发送参数,json存储
    */
    @TableField("param")
    private String param;

    /**
    * 1.实时发送|2.定时发送
    */
    @TableField("send_method")
    private Integer sendMethod;

    /**
    * 分发时间，定时发送时的时间
    */
    @TableField("send_time")
    private Date sendTime;

    /**
    * 实际发送时间
    */
    @TableField("actual_send_time")
    private Date actualSendTime;

    /**
    * 栏目ID
    */
    @TableField("category_id")
    private Long categoryId;

    /**
    * 状态（0草稿；1发送中；2部分成功；3全部成功；4发送失败；5待审批；6审批未通过；7已撤销）
    */
    @TableField("status")
    private Integer status;

    /**
    * 创建人guid
    */
    @TableField("create_user_guid")
    private String createUserGuid;

    /**
    * 创建人姓名
    */
    @TableField("create_user_name")
    private String createUserName;

    /**
    * 修改人guid
    */
    @TableField("update_user_guid")
    private String updateUserGuid;

    /**
    * 修改人姓名
    */
    @TableField("update_user_name")
    private String updateUserName;

    /**
    * 创建时间
    */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
    * 修改时间
    */
    @TableField("gmt_modify")
    private Date gmtModify;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    @TableField("features")
    private String features;

    /**
    * 逻辑删除
    */
    @TableField("is_deleted")
    private Long isDeleted;

    /**
    * 版本号
    */
    @TableField("version")
    private Integer version;

    /**
    * 应用id
    */
    @TableField("app_id")
    private Long appId;

    /**
    * 租户编码
    */
    @TableField("tenant_code")
    private String tenantCode;


    /**
     * 消息总数
     */
    @TableField("total_num")
    private Integer totalNum;

    /**
     * 发送消息成功总数
     */
    @TableField("success_num")
    private Integer successNum;
}
