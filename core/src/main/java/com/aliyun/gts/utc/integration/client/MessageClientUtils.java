package com.aliyun.gts.utc.integration.client;

import org.springframework.util.Assert;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public class MessageClientUtils {
    /**
     * 在当前环境中能支持的消息渠道
     */
    private static Set<MessageClientTypeEnum> SUPPORT_CLIENTS =
            new HashSet<>();

    public static Set<MessageClientTypeEnum> getSupportClients() {
        return SUPPORT_CLIENTS;
    }

    public static MessageClientTypeEnum getById(long id) {
        MessageClientTypeEnum res = MessageClientTypeEnum.getById(id);
        Assert.notNull(res, "id " + id + " getMessageClientType failed!!");
        return res;
    }


    public static MessageClientTypeEnum getByCode(String code) {
        MessageClientTypeEnum res = MessageClientTypeEnum.getByCode(code);
        Assert.notNull(res, "code " + code + " getMessageClientType failed!!");
        return res;
    }

    public static boolean checkClientEnable(Long id) {
        MessageClientTypeEnum res = MessageClientTypeEnum.getById(id);
        return res != null;
    }

    /**
     * 注册可支持的渠道信息
     *
     * @param type
     */
    static void registerSupportClientType(MessageClientTypeEnum type) {
        Set<MessageClientTypeEnum> supportClients =
                new HashSet<>(SUPPORT_CLIENTS);
        supportClients.add(type);
        SUPPORT_CLIENTS = Collections.unmodifiableSet(supportClients);
    }

}
