package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.facade.response.statistics.QueryStatisticsResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 消息统计服务
 */
public interface MessageStatisticsService {

  /**
   * 消息推送请求数
   *
   * @return 消息推送请求数返回实体类
   */
  public QueryStatisticsResponse queryMessageRequest(int days);


  /**
   * 消息推送次数
   *
   * @return 消息推送次数返回实体类
   */
  QueryStatisticsResponse queryMessagePush(int days);

}
