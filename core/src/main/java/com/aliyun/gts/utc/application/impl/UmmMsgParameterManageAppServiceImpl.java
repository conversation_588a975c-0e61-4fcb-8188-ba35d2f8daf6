/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgParameterDTO;
import com.aliyun.gts.utc.api.facade.request.ummmsgparameter.CreateUmmMsgParameterRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgparameter.PageQueryUmmMsgParameterRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgparameter.UpdateUmmMsgParameterRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgparameter.*;
import com.aliyun.gts.utc.application.UmmMsgParameterManageAppService;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import com.aliyun.gts.utc.core.repository.UmmMsgParameterRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgParameterManageAppServiceImpl.java
 * source table name: umm_msg_parameter
 * initial code lines: 113
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Service
public class UmmMsgParameterManageAppServiceImpl implements UmmMsgParameterManageAppService {
    @Autowired
    private UmmMsgParameterRepository ummMsgParameterRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmMsgParameterResponse createUmmMsgParameter(CreateUmmMsgParameterRequest createUmmMsgParameterRequest){

        UmmMsgParameterDTO ummMsgParameterDTO = createUmmMsgParameterRequest.getUmmMsgParameterDTO();

        UmmMsgParameter ummMsgParameter = UmmMsgParameterDTOConverter.INSTANCE.convert(ummMsgParameterDTO);
        UmmMsgParameter add = ummMsgParameterRepository.add(ummMsgParameter);
        ummMsgParameterDTO.setId(add.getId());

        CreateUmmMsgParameterResponse response = new CreateUmmMsgParameterResponse();
        response.setUmmMsgParameterDTO(ummMsgParameterDTO);

        return response;
    }

    @Override
    public QueryUmmMsgParameterResponse queryUmmMsgParameter(Long id) {

        UmmMsgParameter ummMsgParameter = ummMsgParameterRepository.getById(id);
        UmmMsgParameterDTO ummMsgParameterDTO = UmmMsgParameterDTOConverter.INSTANCE.convert(ummMsgParameter);
        QueryUmmMsgParameterResponse response = new QueryUmmMsgParameterResponse();
        response.setUmmMsgParameterDTO(ummMsgParameterDTO);
        return response;
    }

    @Override
    public PageQueryUmmMsgParameterResponse pageQueryUmmMsgParameter(PageQueryUmmMsgParameterRequest request) {

        UmmMsgParameterDTO ummMsgParameterDTO = request.getUmmMsgParameterDTO();
        UmmMsgParameter ummMsgParameter = UmmMsgParameterDTOConverter.INSTANCE.convert(ummMsgParameterDTO);
        IPage<UmmMsgParameter> byRange = ummMsgParameterRepository.getByRange(request.getPageNum().intValue(), request.getPageSize().intValue(), ummMsgParameter);

        PageQueryUmmMsgParameterResponse response = new PageQueryUmmMsgParameterResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
            .map(UmmMsgParameterDTOConverter.INSTANCE::convert)
            .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmMsgParameterResponse deleteUmmMsgParameter(Long id) {
        DeleteUmmMsgParameterResponse deleteUmmMsgParameterResponse = new DeleteUmmMsgParameterResponse();
        deleteUmmMsgParameterResponse.setIsDelete(ummMsgParameterRepository.deleteById(id));
        return deleteUmmMsgParameterResponse;
    }

    @Override
    public UpdateUmmMsgParameterResponse updateUmmMsgParameter(UpdateUmmMsgParameterRequest request) {

        UmmMsgParameterDTO ummMsgParameterDTO = request.getUmmMsgParameterDTO();

        UmmMsgParameter ummMsgParameter = UmmMsgParameterDTOConverter.INSTANCE.convert(ummMsgParameterDTO);
        UpdateUmmMsgParameterResponse updateUmmMsgParameterResponse = new UpdateUmmMsgParameterResponse();
        updateUmmMsgParameterResponse.setIsUpdate(ummMsgParameterRepository.update(ummMsgParameter));
        return updateUmmMsgParameterResponse;
    }

    @Mapper
    interface UmmMsgParameterDTOConverter {
        UmmMsgParameterDTOConverter INSTANCE = Mappers.getMapper(UmmMsgParameterDTOConverter.class);

        UmmMsgParameter convert(UmmMsgParameterDTO ummMsgParameterDTO);

        UmmMsgParameterDTO convert(UmmMsgParameter ummMsgParameter);
    }

}