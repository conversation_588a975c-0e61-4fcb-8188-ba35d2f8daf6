package com.aliyun.gts.utc.core.utils;

import lombok.extern.slf4j.Slf4j;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Slf4j
public class CronUtils {

    final static  String DATE_FORMAT="ss mm HH dd MM ? yyyy";

    /**
     * 将Date类型转换成cron表达式
     * @param date
     * @return
     */
    public static String convertDateToSingleCron(Date  date){
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("Asia/Shanghai"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
        String formattedDateTime = zonedDateTime.format(formatter);
        return formattedDateTime;
    }
}
