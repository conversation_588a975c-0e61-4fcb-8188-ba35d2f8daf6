/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessage.java
 * source table name: umm_message
 * initial code lines: 133
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Data
public class UmmMessage {

    /**
    * 主键id
    */

    @TableId(value = "id",type = IdType.INPUT)
    private Long id;

    /**
    * 模板id
    */
    private Long tplId;

    /**
    * 
    */
    private String guid;

    /**
    * 消息标题
    */
    private String msgTitle;

    /**
    * 消息内容
    */
    private String msgContent;

    /**
    * 发送参数,json存储
    */
    private String param;

    /**
    * 1.实时发送|2.定时发送
    */
    private Integer sendMethod;

    /**
    * 分发时间，定时发送时的时间
    */
    private Date sendTime;

    /**
    * 实际发送时间
    */
    private Date actualSendTime;

    /**
    * 栏目ID
    */
    private Long categoryId;

    /**
    * 状态  1待审批 2待发送 3发送成功 4审批未通过 5.发送失败  6已撤销
    */
    private Integer status;

    /**
    * 创建人guid
    */
    private String createUserGuid;

    /**
    * 创建人姓名
    */
    private String createUserName;

    /**
    * 修改人guid
    */
    private String updateUserGuid;

    /**
    * 修改人姓名
    */
    private String updateUserName;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModify;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    private String features;

    /**
    * 逻辑删除
    */
    private Long isDeleted;

    /**
    * 版本号
    */
    private Integer version;

    /**
    * 应用id
    */
    private Long appId;

    /**
    * 租户编码
    */
    private String tenantCode;

    /**
     * 发送总数量
     */
    private Integer totalNum;
    /**
     * 发送成功数量
     */
    private Integer successNum;

}