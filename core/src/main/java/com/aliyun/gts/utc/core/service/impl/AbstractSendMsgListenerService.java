package com.aliyun.gts.utc.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendLogDTO;
import com.aliyun.gts.utc.api.dto.MailHostPropsDTO;
import com.aliyun.gts.utc.api.dto.UmmSourceDTO;
import com.aliyun.gts.utc.api.facade.request.MailMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.source.MailSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.SmsSourceRequest;
import com.aliyun.gts.utc.application.impl.MailMessageSenderService;
import com.aliyun.gts.utc.core.base.enums.MessageSourceEnum;
import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.repository.UmmSourceRepository;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.aliyun.gts.utc.core.service.*;
import com.aliyun.gts.utc.core.utils.AES256Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.aliyun.gts.utc.common.constants.StatusConstants.*;

public abstract class AbstractSendMsgListenerService implements SendMsgListenerService {

    @Autowired
    private UmmMsgSendManageCoreService ummMsgSendManageCoreService;

    @Autowired
    private UmmMsgSendLogManageCoreService ummMsgSendLogManageCoreService;

    @Autowired
    private UmmTemplateRepository ummTemplateRepository;

    @Autowired
    private UmmSourceRepository ummSourceRepository;

    @Autowired
    private MailMessageSenderService messageSenderService;

    @Autowired
    private ThreeGorgesService threeGorgesService;

    @Autowired
    private  SxxyService sxxyService;


    @Override
    public final List<UmmMsgSendDTO> notice(List<UmmMsgSendDTO> ummMsgSendDTOS,
                                            Map<String, Object> extend) {
        ummMsgSendDTOS.forEach(msg -> {
            String msgId = msg.getMsgId();
            boolean result = false;
            try {
                result = this.notice(msg, extend);
                msg.setStatus(result ? SEND_SUCCESS_STATUS :
                        SEND_FAIL_STATUS);
            } finally {
                this.saveSendLog(Long.valueOf(msgId), String.valueOf(result));
            }

        });
        return ummMsgSendDTOS;
    }

    private MailMessageSendRequest constructMailMessageSendRequest(UmmSource ummSource) {
        MailSourceRequest emailSourceParam = JSON.parseObject(ummSource.getSourceParam(), MailSourceRequest.class);
        MailMessageSendRequest mailMessageSendRequest = new MailMessageSendRequest();
        MailHostPropsDTO mailHostPropsDTO = new MailHostPropsDTO();
        mailHostPropsDTO.setHost(emailSourceParam.getMailHost());
        mailHostPropsDTO.setProtocol("smtp");
        mailHostPropsDTO.setUsername(emailSourceParam.getMailUserName());
        mailHostPropsDTO.setPassword(AES256Util.decryptAES(emailSourceParam.getMailPassWord()));
        mailHostPropsDTO.setPort(emailSourceParam.getMailPort() == null ? 465 : emailSourceParam.getMailPort());
        mailHostPropsDTO.setSslEnable(emailSourceParam.getMailSSL() == null || emailSourceParam.getMailSSL());
        mailHostPropsDTO.setSubmitter(emailSourceParam.getSubmitter());
        mailMessageSendRequest.setMailHostProps(mailHostPropsDTO);
        return mailMessageSendRequest;
    }



    protected abstract boolean notice(UmmMsgSendDTO ummMsgSendDTO, Map<String
            , Object> extend);

    protected void saveSendLog(Long msgId, String result) {
        List<UmmMsgSendDTO> ummMsgSendDTOS =
                ummMsgSendManageCoreService.queryUmmMsgSendByMsgId(String.valueOf(msgId));
        List<UmmMsgSendLogDTO> ummMsgSendLogDTOS = new ArrayList<>();
        for (UmmMsgSendDTO ummMsgSendDTO : ummMsgSendDTOS) {
            UmmMsgSendLogDTO ummMsgSendLogDTO = new UmmMsgSendLogDTO();
            ummMsgSendLogDTO.setMsgSendId(ummMsgSendDTO.getId());
            ummMsgSendLogDTO.setStatus(SEND_LOG_FAIL_STATUS);
            ummMsgSendLogDTO.setMessageIn(ummMsgSendDTO.getMsgContent());
            ummMsgSendLogDTO.setMessageOut(result);
            ummMsgSendLogDTO.setSendTime(new Date());
            ummMsgSendLogDTO.setGmtCreate(new Date());
            ummMsgSendLogDTOS.add(ummMsgSendLogDTO);
        }
        if (!CollectionUtils.isEmpty(ummMsgSendLogDTOS)) {
            ummMsgSendLogManageCoreService.batchCreateUmmMsgSendLog(ummMsgSendLogDTOS);
        }
    }


}
