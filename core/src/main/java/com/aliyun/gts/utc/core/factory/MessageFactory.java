package com.aliyun.gts.utc.core.factory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.gts.utc.api.dto.*;
import com.aliyun.gts.utc.api.facade.response.message.PageQueryMessageSendResultResponse;
import com.aliyun.gts.utc.api.facade.response.message.QueryMessageResponse;
import com.aliyun.gts.utc.common.constants.MessageTemplateConstant;
import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import com.aliyun.gts.utc.integration.client.MessageClientUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/3 14:45
 */
@Slf4j
public class MessageFactory {


    /**
     * 组装分页返回
     *
     * @param page 分页后的发送明细
     * @return 分页查询消息发送明细返回体
     */
    public static Page<PageQueryMessageSendResultResponse> createResultPageResponse(Page<UmmMsgSend> page) {
        Page<PageQueryMessageSendResultResponse> response =
                new Page<>(page.getCurrent(), page.getSize());
        response.setTotal(page.getTotal());
        List<UmmMsgSend> ummMsgSendList = page.getRecords();
        if (null == ummMsgSendList || ummMsgSendList.size() == 0) {
            return response;
        }
        List<PageQueryMessageSendResultResponse> resultResponseList =
                new ArrayList<>();
        for (UmmMsgSend ummMsgSend : ummMsgSendList) {
            PageQueryMessageSendResultResponse resultResponse =
                    new PageQueryMessageSendResultResponse();
            resultResponse.setAccount(ummMsgSend.getUserCode());
            resultResponse.setContent(ummMsgSend.getMsgContent());
            resultResponse.setId(ummMsgSend.getId());
            resultResponse.setName(ummMsgSend.getUserName());
            resultResponse.setOrg(ummMsgSend.getUserOrg());
            resultResponse.setPosition(ummMsgSend.getUserPostion());
            resultResponse.setPhone(ummMsgSend.getPhone());
            resultResponse.setEmail(ummMsgSend.getEmail());
            resultResponse.setSendStatus(ummMsgSend.getStatus());
            resultResponse.setSendTime(ummMsgSend.getSendTime());
            resultResponse.setReadStatus(ummMsgSend.getReadStatus());
            resultResponse.setReadTime(ummMsgSend.getReadTime());
            resultResponse.setUserGuid(ummMsgSend.getUserGuid());
            resultResponseList.add(resultResponse);
        }
        response.setRecords(resultResponseList);
        return response;
    }


    public static QueryMessageResponse createDetailResponse(UmmMessageDTO ummMessageDTO, long msgChannelId) {
        QueryMessageResponse response = new QueryMessageResponse();
        BeanUtils.copyProperties(ummMessageDTO, response);
        response.setTemplateId(ummMessageDTO.getTplId());
        response.setMsgTitle(ummMessageDTO.getMsgTitle());
        response.setMsgContent(ummMessageDTO.getMsgContent());
        response.setSendType(ummMessageDTO.getSendMethod());
        response.setSubmitName(ummMessageDTO.getUpdateUserName());
        response.setSubmitTime(ummMessageDTO.getGmtCreate());
        response.setChannelId(msgChannelId);
        response.setChannelName(MessageClientUtils.getById(msgChannelId).getClientName());
        ObjectMapper objectMapper = new ObjectMapper();
        ParamDTO paramDTO = new ParamDTO();
        if (StringUtils.isNotEmpty(ummMessageDTO.getParam())) {
            try {
                JSONArray jsonArray = JSON.parseArray(ummMessageDTO.getParam());
                List<CustomParamDTO> customParamDTOList = new ArrayList<>();
                for (Object object : jsonArray) {
                    CustomParamDTO customParamDTO =
                            objectMapper.convertValue(object,
                                    CustomParamDTO.class);
                    customParamDTOList.add(customParamDTO);
                }
                paramDTO.setCustomParamList(customParamDTOList);
            } catch (Exception e) {
                log.info("{}消息存在脏数据，自定义参数无法转换", ummMessageDTO.getId());
            }
        }
        if (StringUtils.isNotEmpty(ummMessageDTO.getFeatures())) {
            try {
                JSONObject jsonObject =
                        JSON.parseObject(ummMessageDTO.getFeatures());
                if (null != jsonObject.get(MessageTemplateConstant.SYSTEM_PARAM_LIST)) {
                    JSONArray jsonArray =
                            jsonObject.getJSONArray(MessageTemplateConstant.SYSTEM_PARAM_LIST);
                    List<SystemParamDTO> systemParamDTOList = new ArrayList<>();
                    for (Object object : jsonArray) {
                        SystemParamDTO systemParamDTO =
                                objectMapper.convertValue(object,
                                        SystemParamDTO.class);
                        systemParamDTOList.add(systemParamDTO);
                    }
                    paramDTO.setSystemParamList(systemParamDTOList);
                }
                if (null != jsonObject.get(MessageTemplateConstant.RECEIVER_LIST)) {
                    JSONArray jsonArray =
                            jsonObject.getJSONArray(MessageTemplateConstant.RECEIVER_LIST);
                    List<ReceiverDTO> receiverList = new ArrayList<>();
                    for (Object object : jsonArray) {
                        ReceiverDTO receiverDTO =
                                objectMapper.convertValue(object,
                                        ReceiverDTO.class);
                        receiverList.add(receiverDTO);
                    }
                    response.setReceiverList(receiverList);
                }
            } catch (Exception e) {
                log.info("{}消息存在脏数据，系统参数无法转换", ummMessageDTO.getId());
            }
        }
        response.setParamDTO(paramDTO);
        return response;
    }
}
