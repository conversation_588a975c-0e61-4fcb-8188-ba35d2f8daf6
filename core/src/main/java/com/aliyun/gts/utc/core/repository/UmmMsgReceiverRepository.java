/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository;

import com.aliyun.gts.utc.core.domain.UmmMsgReceiver;
import com.baomidou.mybatisplus.core.metadata.IPage;


public interface UmmMsgReceiverRepository {

    /**
     * 通过 id 获取ummMsgReceiver
     *
     * @param id ummMsgReceiver id
     * @return ummMsgReceiver；null 如果账户不存在
     */
    UmmMsgReceiver getById(Long id);

    /**
     * 通过 id 获取ummMsgReceiver
     *
     * @param msgId ummMsgReceiver id
     * @return ummMsgReceiver；null 如果账户不存在
     */
    UmmMsgReceiver getByMsgId(Long msgId);

    /**
     * 获取一个区间内的ummMsgReceiver
     *
     * @param pageNum
     * @param pageSize 期望个数
     * @return 区间内的ummMsgReceiver列表
     */
    IPage<UmmMsgReceiver> getByRange(Long pageNum, Long pageSize,
                                     UmmMsgReceiver ummMsgReceiver);

    /**
     * 添加ummMsgReceiver
     *
     * @param ummMsgReceiver 待添加ummMsgReceiver
     */
    UmmMsgReceiver add(UmmMsgReceiver ummMsgReceiver);

    /**
     * 更新ummMsgReceiver
     *
     * @param ummMsgReceiver 待更新ummMsgReceiver对象
     * @return true，更新成功；false，ummMsgReceiver不存在
     */
    boolean update(UmmMsgReceiver ummMsgReceiver);

    /**
     * 通过 id 删除ummMsgReceiver
     *
     * @param id 账户 id
     * @return true，删除成功；false，ummMsgReceiver不存在
     */
    boolean deleteById(Long id);

}