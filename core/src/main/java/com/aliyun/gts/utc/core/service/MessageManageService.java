package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.facade.request.message.MessageReplayRequest;
import com.aliyun.gts.utc.api.facade.request.message.PageQueryMessageSendResultRequest;
import com.aliyun.gts.utc.api.facade.response.message.MessageReplayResponse;
import com.aliyun.gts.utc.api.facade.response.message.PageQueryMessageSendResultResponse;
import com.aliyun.gts.utc.api.facade.response.message.QueryMessageResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <AUTHOR>
 * @date 2021/6/3 14:41
 */
public interface MessageManageService {

    /**
     * 分页查询消息发送明细
     * @param request 请求体
     * @return 分页后的发送明细
     */
    Page<PageQueryMessageSendResultResponse> sendResultPage(PageQueryMessageSendResultRequest request);

    /**
     * 获取消息详情
     * @param id 主键
     * @return 消息想
     */
    QueryMessageResponse getDetail(Long id);


    /**
     * 消息重发
     * @param request 请求体
     * @return 重复结果
     */
    MessageReplayResponse replay(MessageReplayRequest request);

    /**
     * 根据umm_message主键发送执行xxl-job的定时任务消息
     */
    Boolean sendMsgByUmmMessageId(Long ummMessageId);
}
