/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendLogDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsendlog.PageQueryUmmMsgSendLogRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsendlog.UpdateUmmMsgSendLogRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsendlog.*;

import java.util.List;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendLogManageAppService.java
 * source table name: umm_msg_send_log
 * initial code lines: 67
 * initial generated date: 2021-06-03 22:51:27
 *
 * @date 2021-06-03 22:51:27
 */
public interface UmmMsgSendLogManageCoreService {

    /**
     * 创建UmmMsgSendLog
     *
     * @param createUmmMsgSendLogRequest 创建$UmmMsgSendLog请求
     * @return 创建UmmMsgSendLog响应
     */
    CreateUmmMsgSendLogResponse createUmmMsgSendLog(UmmMsgSendLogDTO ummMsgSendLogDTO);

    /**
     * 查询UmmMsgSendLog
     *
     * @param id 查询UmmMsgSendLog请求
     * @return 查询UmmMsgSendLog响应
     */
    QueryUmmMsgSendLogResponse queryUmmMsgSendLog(Long id);


    Boolean batchCreateUmmMsgSendLog(List<UmmMsgSendLogDTO> ummMsgSendLogDTOS);

    /**
     * 分页查询UmmMsgSendLog
     *
     * @param pageQueryUmmMsgSendLogRequest 查询UmmMsgSendLog请求
     * @return UmmMsgSendLog查询结果
     */
    PageQueryUmmMsgSendLogResponse pageQueryUmmMsgSendLog(PageQueryUmmMsgSendLogRequest pageQueryUmmMsgSendLogRequest);

    /**
     * 删除UmmMsgSendLog
     *
     * @param id 删除UmmMsgSendLog请求
     * @return UmmMsgSendLog删除结果
     */
    DeleteUmmMsgSendLogResponse deleteUmmMsgSendLog(Long id);

    /**
     * 更新UmmMsgSendLog
     *
     * @param updateUmmMsgSendLogRequest 更新UmmMsgSendLog请求
     * @return UmmMsgSendLog更新结果
     */
    UpdateUmmMsgSendLogResponse updateUmmMsgSendLog(UpdateUmmMsgSendLogRequest updateUmmMsgSendLogRequest);

}