package com.aliyun.gts.utc.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.core.repository.UmmSourceRepository;
import com.aliyun.gts.utc.core.repository.UmmTemplateRepository;
import com.aliyun.gts.utc.core.service.MailService;
import com.aliyun.gts.utc.core.service.SmsService;
import com.aliyun.gts.utc.integration.client.MessageClientTypeEnum;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.aliyun.gts.utc.common.constants.StatusConstants.TEMPLATE_KEY;

/**
 * @author: guoshengLi
 * @create: 2025-03-03 16:23
 * @Description:
 */
@Service
@Slf4j
public class SmsNoticeMsgListenerServiceImpl  extends AbstractSendMsgListenerService{

  @Autowired
  private  ThreeGorgesServiceImpl threeGorgesService;

  @Autowired
  private UmmTemplateRepository ummTemplateRepository;

  @Autowired
  private UmmSourceRepository ummSourceRepository;

  @Override
  protected boolean notice(UmmMsgSendDTO ummMsgSendDTO, Map<String, Object> extend) {
    if(Strings.isNullOrEmpty(ummMsgSendDTO.getPhone())) {
      log.info("用户手机号为空，跳过发送步骤。用户id："+ummMsgSendDTO.getUserCode()+"，用户姓名："+ummMsgSendDTO.getUserName());
      return false;
    }
    UmmTemplate ummTemplate = ummTemplateRepository.getById((Long) extend.get(TEMPLATE_KEY));
    if (ummTemplate != null && ummTemplate.getSourceId() != null) {
      UmmSource ummSource = ummSourceRepository.getById(ummTemplate.getSourceId());
     return threeGorgesService.sendSms(ummSource, ummMsgSendDTO);
    }
    return false;
  }

  @Override
  public Long getChannelId() {
      return MessageClientTypeEnum.SMS.getClientID();
  }

  @Override
  public void noticeRollback(Object obj, Map<String, Object> extend) {

  }
}
