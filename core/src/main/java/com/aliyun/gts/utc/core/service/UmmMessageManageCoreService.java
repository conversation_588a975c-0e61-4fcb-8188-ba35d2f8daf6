/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.service;

import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.CreateUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.PageQueryUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.UpdateUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessageManageAppService.java
 * source table name: umm_message
 * initial code lines: 67
 * initial generated date: 2021-06-05 14:10:07
 *
 * @date 2021-06-05 14:10:07
 */
public interface UmmMessageManageCoreService {

    /**
     * 创建UmmMessage
     *
     * @param createUmmMessageRequest 创建$UmmMessage请求
     * @return 创建UmmMessage响应
     */
    CreateUmmMessageResponse createUmmMessage(CreateUmmMessageRequest createUmmMessageRequest);

    /**
     * 查询UmmMessage
     *
     * @param id 查询UmmMessage请求
     * @return 查询UmmMessage响应
     */
    QueryUmmMessageResponse queryUmmMessage(Long id);

    /**
     * 查询UmmMessage
     *
     * @param guid 查询UmmMessage请求
     * @return 查询UmmMessage响应
     */
    QueryUmmMessageResponse queryUmmMessageByGuid(String guid);

    /**
     * 分页查询UmmMessage
     *
     * @param pageQueryUmmMessageRequest 查询UmmMessage请求
     * @return UmmMessage查询结果
     */
    PageQueryUmmMessageResponse pageQueryUmmMessage(PageQueryUmmMessageRequest pageQueryUmmMessageRequest);

    /**
     * 删除UmmMessage
     *
     * @param id 删除UmmMessage请求
     * @return UmmMessage删除结果
     */
    DeleteUmmMessageResponse deleteUmmMessage(Long id);

    /**
     * 更新ummMessage的状态
     *
     * @param msgId
     * @param successNum
     * @return true，更新成功
     */
    boolean updateStatus(Long msgId, Integer successNum);

    /**
     * 更新UmmMessage
     *
     * @param updateUmmMessageRequest 更新UmmMessage请求
     * @return UmmMessage更新结果
     */
    UpdateUmmMessageResponse updateUmmMessage(UpdateUmmMessageRequest updateUmmMessageRequest);

}
