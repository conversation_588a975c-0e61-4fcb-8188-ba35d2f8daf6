package com.aliyun.gts.utc.core.dataobject;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;

@Data
public class BaseEntity {
    @TableField(
            value = "guid",
            fill = FieldFill.INSERT
    )
    private String guid;
    @TableField(
            value = "create_user_guid",
            fill = FieldFill.INSERT
    )
    private String createUserGuid;
    @TableField(
            value = "create_user_name",
            fill = FieldFill.INSERT
    )
    private String createUserName;
    @TableField(
            value = "update_user_guid",
            fill = FieldFill.INSERT_UPDATE
    )
    private String updateUserGuid;
    @TableField(
            value = "update_user_name",
            fill = FieldFill.INSERT_UPDATE
    )
    private String updateUserName;
    @TableField(
            value = "gmt_create",
            fill = FieldFill.INSERT
    )
    private Date gmtCreate;
    @TableField(
            value = "gmt_modify",
            fill = FieldFill.INSERT_UPDATE
    )
    private Date gmtModify;
    @TableField("features")
    private String features;
    @TableLogic(
            value = "0",
            delval = "#{id}"
    )
    @TableField("is_deleted")
    private Long isDeleted;
    @TableField("version")
    private Integer version;
    @TableField(
            value = "app_id",
            fill = FieldFill.INSERT
    )
    private Long appId;
    @TableField(
            value = "tenant_code",
            fill = FieldFill.INSERT
    )
    private String tenantCode;
}