/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application;

import com.aliyun.gts.utc.api.facade.request.ummmsgsend.CreateUmmMsgSendRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgsend.PageQueryUmmMsgSendRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgsend.UpdateUmmMsgSendRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgsend.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendManageAppService.java
 * source table name: umm_msg_send
 * initial code lines: 67
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
public interface UmmMsgSendManageAppService {

    /**
     * 创建UmmMsgSend
     *
     * @param createUmmMsgSendRequest 创建$UmmMsgSend请求
     * @return 创建UmmMsgSend响应
     */
    CreateUmmMsgSendResponse createUmmMsgSend(CreateUmmMsgSendRequest createUmmMsgSendRequest);

    /**
     * 查询UmmMsgSend
     *
     * @param id 查询UmmMsgSend请求
     * @return 查询UmmMsgSend响应
     */
    QueryUmmMsgSendResponse queryUmmMsgSend(Long id);

    /**
     * 分页查询UmmMsgSend
     *
     * @param pageQueryUmmMsgSendRequest 查询UmmMsgSend请求
     * @return UmmMsgSend查询结果
     */
    PageQueryUmmMsgSendResponse pageQueryUmmMsgSend(PageQueryUmmMsgSendRequest pageQueryUmmMsgSendRequest);

    /**
     * 删除UmmMsgSend
     *
     * @param id 删除UmmMsgSend请求
     * @return UmmMsgSend删除结果
     */
    DeleteUmmMsgSendResponse deleteUmmMsgSend(Long id);

    /**
     * 更新UmmMsgSend
     *
     * @param updateUmmMsgSendRequest 更新UmmMsgSend请求
     * @return UmmMsgSend更新结果
     */
    UpdateUmmMsgSendResponse updateUmmMsgSend(UpdateUmmMsgSendRequest updateUmmMsgSendRequest);

}