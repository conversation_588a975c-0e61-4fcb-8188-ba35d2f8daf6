/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application;

import com.aliyun.gts.utc.api.facade.request.ummtemplate.CreateUmmTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.ummtemplate.PageQueryUmmTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.ummtemplate.UpdateUmmTemplateRequest;
import com.aliyun.gts.utc.api.facade.response.ummtemplate.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmTemplateManageAppService.java
 * source table name: umm_template
 * initial code lines: 67
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
public interface UmmTemplateManageAppService {

    /**
     * 创建UmmTemplate
     *
     * @param createUmmTemplateRequest 创建$UmmTemplate请求
     * @return 创建UmmTemplate响应
     */
    CreateUmmTemplateResponse createUmmTemplate(CreateUmmTemplateRequest createUmmTemplateRequest);

    /**
     * 查询UmmTemplate
     *
     * @param id 查询UmmTemplate请求
     * @return 查询UmmTemplate响应
     */
    QueryUmmTemplateResponse queryUmmTemplate(Long id);


    QueryUmmTemplateResponse queryUmmTemplateByCode(String code);

    /**
     * 分页查询UmmTemplate
     *
     * @param pageQueryUmmTemplateRequest 查询UmmTemplate请求
     * @return UmmTemplate查询结果
     */
    PageQueryUmmTemplateResponse pageQueryUmmTemplate(PageQueryUmmTemplateRequest pageQueryUmmTemplateRequest);

    /**
     * 删除UmmTemplate
     *
     * @param id 删除UmmTemplate请求
     * @return UmmTemplate删除结果
     */
    DeleteUmmTemplateResponse deleteUmmTemplate(Long id);

    /**
     * 更新UmmTemplate
     *
     * @param updateUmmTemplateRequest 更新UmmTemplate请求
     * @return UmmTemplate更新结果
     */
    UpdateUmmTemplateResponse updateUmmTemplate(UpdateUmmTemplateRequest updateUmmTemplateRequest);


    PageQueryUmmTemplateResponse queryAllUmmTemplate();
}