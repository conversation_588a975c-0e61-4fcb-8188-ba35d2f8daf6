package com.aliyun.gts.utc.core.base.enums;

public enum AdminTypeEnum {
    NORMAL_USER("0"),
    SUPER_ADMIN("1");

    private String type;

    private AdminTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return this.type;
    }

    public static AdminTypeEnum getByType(String type) {
        AdminTypeEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            AdminTypeEnum e = var1[var3];
            if (e.type.equals(type)) {
                return e;
            }
        }

        return NORMAL_USER;
    }
}
