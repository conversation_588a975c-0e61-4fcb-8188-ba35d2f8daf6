package com.aliyun.gts.utc.integration.client;

import com.aliyun.gts.utc.integration.client.alisms.AliSmsClientImpl;
import com.aliyun.gts.utc.integration.client.alisms.MessageSmsProperties;
import com.aliyun.gts.utc.integration.client.alisms.SmsClient;
import com.aliyun.gts.utc.integration.client.mail.MailClient;
import com.aliyun.gts.utc.integration.client.mail.MailClientImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.MailSender;

import java.util.Map;

@Configuration
public class MessageClientAutoConfiguration {

    /**
     * 项目中通过配置启用了spring-boot-mail-starter的时候构造MailClient.否则返回mockMailCLient,
     * 被调用的时候都抛出错误
     *
     * @param mailSender
     * @return
     */
    @Bean
    public MailClient mailClient(@Autowired(required = false) MailSender mailSender) {
        if (mailSender != null) {
            //注册渠道
            MessageClientUtils.registerSupportClientType(MessageClientTypeEnum.MAIL);
            return new MailClientImpl(mailSender);
        } else {
            return mockMailClient();
        }
    }


    public MailClient mockMailClient() {
        //for mock
        return new MailClient() {
            @Override
            public void sendMail(String subject, String content,
                                 String receiverAddress, String sender,
                                 boolean html) {
                throw new UnsupportedOperationException("this is mock mail " +
                        "client！");
            }
        };
    }


    @Bean
    public MessageSmsProperties messageSmsProperties() {
        return new MessageSmsProperties();
    }

    @Bean
    public SmsClient smsClient(MessageSmsProperties messageSmsProperties) {
        if (MessageSmsProperties.PLAT_ALI.equalsIgnoreCase(messageSmsProperties.getPlat())) {
            MessageClientUtils.registerSupportClientType(MessageClientTypeEnum.ALISMS);
            return new AliSmsClientImpl(messageSmsProperties.getAkid(),
                    messageSmsProperties.getAksk(),
                    messageSmsProperties.getSign());
        } else {
            //mock
            return new SmsClient() {
                @Override
                public void sendSms(String templateCode,
                                    Map<String, String> extParams,
                                    String phoneNum) throws Exception {
                    throw new UnsupportedOperationException("this is mock sms" +
                            " client!");
                }
            };
        }
    }

}
