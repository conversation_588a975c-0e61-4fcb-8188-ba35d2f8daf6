/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dataobject;

import com.aliyun.gts.utc.core.dataobject.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName("umm_source")
public class UmmSourceDO extends BaseEntity {

    /**
    * 主键id
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * 消息源名称
    */
    @TableField("source_name")
    private String sourceName;

    /**
    * 消息源编码
    */
    @TableField("source_code")
    private String sourceCode;

    /**
     * 消息源类型
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 消息源参数json存储
    */
    @TableField("source_param")
    private String sourceParam;
}