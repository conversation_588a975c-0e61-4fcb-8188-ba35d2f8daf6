/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmMessage;
import com.aliyun.gts.utc.core.domain.UmmMessageQueryCondition;
import com.aliyun.gts.utc.core.repository.UmmMessageRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dao.UmmMessageMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmMessageDO;
import com.aliyun.gts.utc.dal.dto.PageMessageDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageQueryDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessageRepositoryImpl.java
 * source table name: umm_message
 * initial code lines: 164
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Repository
public class UmmMessageRepositoryImpl implements UmmMessageRepository {

    @Autowired
    private UmmMessageMapper ummMessageMapper;

    @Override
    public UmmMessage getById(Long id) {
        LambdaQueryWrapper<UmmMessageDO> wrapperSelectDetail =
                new LambdaQueryWrapper<UmmMessageDO>()
                        .eq(UmmMessageDO::getId, id)
                        .eq(UmmMessageDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMessageDO ummMessageDO = ummMessageMapper.selectOne(wrapperSelectDetail);
        return UmmMessageDOConverter.INSTANCE.convert(ummMessageDO);
    }

    @Override
    public UmmMessage getByGuid(String guid) {
        LambdaQueryWrapper<UmmMessageDO> wrapperSelectDetail =
                new LambdaQueryWrapper<UmmMessageDO>()
                        .eq(UmmMessageDO::getGuid, guid)
                        .eq(UmmMessageDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMessageDO ummMessageDO =
                ummMessageMapper.selectOne(wrapperSelectDetail);
        return UmmMessageDOConverter.INSTANCE.convert(ummMessageDO);
    }

    @Override
    public List<UmmMessage> getByIds(List<Long> ids) {
        LambdaQueryWrapper<UmmMessageDO> wrapperSelectDetail =
                new LambdaQueryWrapper<UmmMessageDO>()
                        .in(UmmMessageDO::getId, ids)
                        .eq(UmmMessageDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        List<UmmMessageDO> ummMessageDOS = ummMessageMapper.selectList(wrapperSelectDetail);
        List<UmmMessage> collect =
                ummMessageDOS.stream().map(UmmMessageDOConverter.INSTANCE::convert).collect(Collectors.toList());
        return collect;
    }

    public List<UmmMessage> getByTplId(Long templateId) {
        LambdaQueryWrapper<UmmMessageDO> wrapper =
                new LambdaQueryWrapper<UmmMessageDO>()
                        .eq(UmmMessageDO::getIsDeleted, 0)
                        .eq(UmmMessageDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                        .eq(UmmMessageDO::getTplId, templateId);
        return ummMessageMapper.selectList(wrapper).parallelStream().map(UmmMessageDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public IPage<UmmMessage> getByRange(Long pageNum, Long pageSize,
                                        UmmMessage ummMessage) {

        Page<UmmMessageDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        UmmMessageDO ummMessageDO =
                UmmMessageDOConverter.INSTANCE.convert(ummMessage);
        ummMessageDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        QueryWrapper<UmmMessageDO> queryWrapper = new QueryWrapper();
        queryWrapper.setEntity(ummMessageDO);
        IPage<UmmMessageDO> iPage = ummMessageMapper.selectPage(page,
                queryWrapper);
        List<UmmMessage> list = iPage.getRecords().stream()
                .map(UmmMessageDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
        Page<UmmMessage> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setSize(iPage.getSize());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    @Override
    public IPage<UmmMessage> getPage(Long pageNum, Long pageSize,
                                     UmmMessageQueryCondition ummMessageQueryCondition) {
        Page<UmmMessageDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        UmmMessageDO ummMessageDO =
                UmmMessageDOConverter.INSTANCE.convert(ummMessageQueryCondition);
        ummMessageDO.setTenantCode(RuntimeContextUtil.getTenantCode());

        QueryWrapper<UmmMessageDO> queryWrapper = new QueryWrapper();
        queryWrapper.setEntity(ummMessageDO);
        if (Objects.nonNull(ummMessageQueryCondition.getMsgTitle())) {
            queryWrapper.like("msg_title",
                    ummMessageQueryCondition.getMsgTitle());
        }
        if (Objects.nonNull(ummMessageQueryCondition.getSubmitDateStart()) && Objects.nonNull(ummMessageQueryCondition.getSubmitDateEnd())) {
            queryWrapper.between("gmt_modify",
                    ummMessageQueryCondition.getSubmitDateStart(),
                    ummMessageQueryCondition.getSubmitDateEnd());
        }
        if (!StringUtils.isEmpty(ummMessageQueryCondition.getOrderField())) {
            queryWrapper.orderBy(true, ummMessageQueryCondition.getIsAsc(),
                    ummMessageQueryCondition.getOrderField());
        }

        IPage<UmmMessageDO> iPage = ummMessageMapper.selectPage(page,
                queryWrapper);
        List<UmmMessage> list = iPage.getRecords().stream()
                .map(UmmMessageDOConverter.INSTANCE::convert)
                .collect(Collectors.toList());
        Page<UmmMessage> newPage = new Page<>();
        newPage.setCurrent(iPage.getCurrent());
        newPage.setSize(iPage.getSize());
        newPage.setTotal(iPage.getTotal());
        newPage.setRecords(list);
        return newPage;
    }

    @Override
    public UmmMessage add(UmmMessage ummMessage) {

        UmmMessageDO ummMessageDO =
                UmmMessageDOConverter.INSTANCE.convert(ummMessage);
        ummMessageDO.setTenantCode(RuntimeContextUtil.getTenantCode());
        ummMessageMapper.insert(ummMessageDO);
        ummMessage.setId(ummMessageDO.getId());
        return ummMessage;
    }

    @Override
    public boolean updateStatus(Long msgId, Integer successNum) {
        return ummMessageMapper.updateStatus(msgId, successNum, RuntimeContextUtil.getTenantCode());
    }

    @Override
    public boolean update(UmmMessage ummMessage) {

        UmmMessageDO ummMessageDO = UmmMessageDOConverter.INSTANCE.convert(ummMessage);

        LambdaQueryWrapper<UmmMessageDO> wrapperSelectDetail =
                new LambdaQueryWrapper<UmmMessageDO>()
                        .eq(UmmMessageDO::getId, ummMessageDO.getId())
                        .eq(UmmMessageDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMessageDO newUmmMessageDO = ummMessageMapper.selectOne(wrapperSelectDetail);

        if (newUmmMessageDO == null) {
            return false;
        }
        if (ummMessageDO.getTplId() != null) {
            newUmmMessageDO.setTplId(ummMessageDO.getTplId());
        }
        if (ummMessageDO.getGuid() != null) {
            newUmmMessageDO.setGuid(ummMessageDO.getGuid());
        }
        if (ummMessageDO.getMsgTitle() != null) {
            newUmmMessageDO.setMsgTitle(ummMessageDO.getMsgTitle());
        }
        if (ummMessageDO.getMsgContent() != null) {
            newUmmMessageDO.setMsgContent(ummMessageDO.getMsgContent());
        }
        if (ummMessageDO.getParam() != null) {
            newUmmMessageDO.setParam(ummMessageDO.getParam());
        }
        if (ummMessageDO.getSendMethod() != null) {
            newUmmMessageDO.setSendMethod(ummMessageDO.getSendMethod());
        }
        if (ummMessageDO.getSendTime() != null) {
            newUmmMessageDO.setSendTime(ummMessageDO.getSendTime());
        }
        if (ummMessageDO.getActualSendTime() != null) {
            newUmmMessageDO.setActualSendTime(ummMessageDO.getActualSendTime());
        }
        if (ummMessageDO.getCategoryId() != null) {
            newUmmMessageDO.setCategoryId(ummMessageDO.getCategoryId());
        }
        if (ummMessageDO.getStatus() != null) {
            newUmmMessageDO.setStatus(ummMessageDO.getStatus());
        }
        if (ummMessageDO.getCreateUserGuid() != null) {
            newUmmMessageDO.setCreateUserGuid(ummMessageDO.getCreateUserGuid());
        }
        if (ummMessageDO.getCreateUserName() != null) {
            newUmmMessageDO.setCreateUserName(ummMessageDO.getCreateUserName());
        }
        if (ummMessageDO.getUpdateUserGuid() != null) {
            newUmmMessageDO.setUpdateUserGuid(ummMessageDO.getUpdateUserGuid());
        }
        if (ummMessageDO.getUpdateUserName() != null) {
            newUmmMessageDO.setUpdateUserName(ummMessageDO.getUpdateUserName());
        }
        if (ummMessageDO.getGmtCreate() != null) {
            newUmmMessageDO.setGmtCreate(ummMessageDO.getGmtCreate());
        }
        if (ummMessageDO.getGmtModify() != null) {
            newUmmMessageDO.setGmtModify(ummMessageDO.getGmtModify());
        }
        if (ummMessageDO.getFeatures() != null) {
            newUmmMessageDO.setFeatures(ummMessageDO.getFeatures());
        }
        if (ummMessageDO.getIsDeleted() != null) {
            newUmmMessageDO.setIsDeleted(ummMessageDO.getIsDeleted());
        }
        if (ummMessageDO.getVersion() != null) {
            newUmmMessageDO.setVersion(ummMessageDO.getVersion());
        }
        if (ummMessageDO.getAppId() != null) {
            newUmmMessageDO.setAppId(ummMessageDO.getAppId());
        }
        if (ummMessageDO.getTotalNum() != null) {
            newUmmMessageDO.setTotalNum(ummMessageDO.getTotalNum());
        }
        if (ummMessageDO.getSuccessNum() != null) {
            newUmmMessageDO.setSuccessNum(ummMessageDO.getSuccessNum());
        }

        ummMessageMapper.updateById(newUmmMessageDO);
        return true;
    }

    @Override
    public boolean deleteById(Long id) {
        LambdaQueryWrapper<UmmMessageDO> wrapperSelectDetail =
                new LambdaQueryWrapper<UmmMessageDO>()
                        .eq(UmmMessageDO::getId, id)
                        .eq(UmmMessageDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmMessageDO ummMessageDO = ummMessageMapper.selectOne(wrapperSelectDetail);
        if (ummMessageDO == null) {
            return false;
        }
        return ummMessageMapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteByIdOnBatch(List<Long> ids) {
        return ummMessageMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Page<PageMessageDTO> pageQuery(Page<UmmMessageDO> page,
                                          PageMessageQueryDTO query) {
        if (page.getCurrent() <= 0) {
            page.setCurrent(1L);
        }

        if (page.getSize() <= 0) {
            page.setSize(10L);
        }

        if (StringUtils.hasLength(query.getOrderField())) {
            switch (query.getOrderField()) {
                case "gmtCreate":
                    query.setOrderField("gmt_create");
                    break;
                default:
                    query.setOrderField("gmt_modify");
            }
        }
        query.setTenantCode(RuntimeContextUtil.getTenantCode());
        Long count = ummMessageMapper.count(query);
        List<UmmMessageDO> list = ummMessageMapper.pageQuery((page.getCurrent() - 1) * page.getSize(), page.getSize(),
                query);
        List<PageMessageDTO> collect =
                list.stream().map(UmmMessageDOConverter.INSTANCE::convertPage).collect(Collectors.toList());

        return new Page<PageMessageDTO>()
                .setCurrent(page.getCurrent())
                .setSize(page.getSize())
                .setRecords(collect)
                .setTotal(count);
    }

    @Mapper
    interface UmmMessageDOConverter {
        UmmMessageDOConverter INSTANCE =
                Mappers.getMapper(UmmMessageDOConverter.class);

        UmmMessageDO convert(UmmMessage ummMessage);

        UmmMessage convert(UmmMessageDO ummMessageDO);

        UmmMessageDO convert(UmmMessageQueryCondition condition);

        PageMessageDTO convertPage(UmmMessageDO ummMessageDO);

    }

}