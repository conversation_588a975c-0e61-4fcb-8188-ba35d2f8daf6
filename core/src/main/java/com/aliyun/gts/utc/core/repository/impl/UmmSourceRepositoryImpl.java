package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.api.dto.UmmSourceDTO;
import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.core.repository.UmmSourceRepository;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.aliyun.gts.utc.dal.dao.UmmSourceMapper;
import com.aliyun.gts.utc.dal.dataobject.UmmSourceDO;
import com.aliyun.gts.utc.dal.dto.MessageSourceQueryDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
@Repository
public class UmmSourceRepositoryImpl implements UmmSourceRepository {
    @Autowired
    private UmmSourceMapper ummSourceMapper;

    @Override
    public UmmSource add(UmmSource ummSource) {
        UmmSourceDO ummSourceDO = UmmSourceDOConverter.INSTANCE.convert(ummSource);
        ummSourceDO.setTenantCode(RuntimeContextUtil.getTenantCode());
        ummSourceMapper.insert(ummSourceDO);
        ummSource.setId(ummSourceDO.getId());
        return ummSource;
    }

    @Override
    public int countSourceName(String sourceName) {
        LambdaQueryWrapper<UmmSourceDO> queryWrapper = new QueryWrapper<UmmSourceDO>()
                .lambda().eq(UmmSourceDO::getSourceName,sourceName)
                .eq(UmmSourceDO::getIsDeleted, 0)
                .eq(UmmSourceDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        return Math.toIntExact(ummSourceMapper.selectCount(queryWrapper));
    }

    @Override
    public Boolean checkSourceCode(String sourceCode) {
        LambdaQueryWrapper<UmmSourceDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmSourceDO>()
                .eq(UmmSourceDO::getIsDeleted, 0)
                .eq(UmmSourceDO::getSourceCode, sourceCode)
                .eq(UmmSourceDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmSourceDO ummSourceDO = ummSourceMapper.selectOne(wrapperSelectDetail);
        if (ummSourceDO != null) {
            return true;
        }
        return false;
    }

    @Override
    public UmmSource getBySourceCode(String sourceCode) {
        LambdaQueryWrapper<UmmSourceDO> wrapperSelectDetail = new LambdaQueryWrapper<UmmSourceDO>()
                .eq(UmmSourceDO::getIsDeleted, 0)
                .eq(UmmSourceDO::getSourceCode, sourceCode)
                .eq(UmmSourceDO::getTenantCode, RuntimeContextUtil.getTenantCode());
        UmmSourceDO ummSourceDO = ummSourceMapper.selectOne(wrapperSelectDetail);
        if(ummSourceDO == null){
            return null;
        }
        return UmmSourceDOConverter.INSTANCE.convert(ummSourceDO);
    }

    @Override
    public Boolean update(UmmSource ummSource) {
        UmmSourceDO ummSourceDO = UmmSourceDOConverter.INSTANCE.convert(ummSource);
        return ummSourceMapper.updateById(ummSourceDO) > 0;
    }

    @Override
    public UmmSource getById(Long id) {
        UmmSourceDO ummSourceDO = ummSourceMapper.selectById(id);
        if (ummSourceDO != null) {
            return UmmSourceDOConverter.INSTANCE.convert(ummSourceDO);
        }
        return null;
    }

    @Override
    public Boolean batchDelete(List<Long> idList) {
        return ummSourceMapper.deleteBatchIds(idList) == idList.size();
    }

    @Override
    public Page<UmmSourceDTO> pageQuery(Page<UmmSourceDTO> page, MessageSourceQueryDTO query) {
        query.setTenantCode(RuntimeContextUtil.getTenantCode());
        return ummSourceMapper.pageQuery(page, query);
    }

    @Override
    public List<UmmSource> getByRange(int pageNum, int pageSize) {
        Page<UmmSourceDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        Page<UmmSourceDO> ummSourceDOPage = ummSourceMapper.selectPage(page, new QueryWrapper<UmmSourceDO>().lambda()
                .eq(UmmSourceDO::getTenantCode, RuntimeContextUtil.getTenantCode())
                .eq(UmmSourceDO::getIsDeleted, 0)
        );
        return ummSourceDOPage.getRecords().stream().map(UmmSourceDOConverter.INSTANCE::convert).collect(Collectors.toList());
    }

    @Mapper
    interface UmmSourceDOConverter {
        UmmSourceDOConverter INSTANCE = Mappers.getMapper(UmmSourceDOConverter.class);

        UmmSource convert(UmmSourceDO ummSourceDO);

        UmmSourceDO convert(UmmSource ummSource);
    }
}
