/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("umm_msg_send_log")
public class UmmMsgSendLogDO {

    /**
    * 主键id
    */
    @TableId
    private Long id;

    /**
    * 消息发送数据id
    */
    @TableField("msg_send_id")
    private Long msgSendId;

    /**
    * 消息原文
    */
    @TableField("message_in")
    private String messageIn;

    /**
    * 返回的内容
    */
    @TableField("message_out")
    private String messageOut;

    /**
    * 发送时间
    */
    @TableField("send_time")
    private Date sendTime;

    /**
    * 3.发送成功，4.发送失败
    */
    @TableField("status")
    private Integer status;

    /**
    * 创建时间
    */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
    * 修改时间
    */
    @TableField("gmt_modify")
    private Date gmtModify;

    @TableField("tenant_code")
    private String tenantCode;

}