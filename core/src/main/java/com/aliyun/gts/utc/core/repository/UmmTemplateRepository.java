/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository;

import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.dal.dto.MessageTemplateQueryDTO;
import com.aliyun.gts.utc.dal.dto.PageMessageTemplateDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;


public interface UmmTemplateRepository {

    /**
     * 通过 id 获取ummTemplate
     *
     * @param id ummTemplate id
     * @return ummTemplate；null 如果账户不存在
     */
    UmmTemplate getById(Long id);

    UmmTemplate getByCode(String code);

    /**
     * 获取一个区间内的ummTemplate
     *
     * @param pageNum
     * @param pageSize 期望个数
     * @return 区间内的ummTemplate列表
     */
    IPage<UmmTemplate> getByRange(int pageNum, int pageSize,
                                  UmmTemplate ummTemplate);

    /**
     * 添加ummTemplate
     *
     * @param ummTemplate 待添加ummTemplate
     */
    UmmTemplate add(UmmTemplate ummTemplate);


    public Boolean checkTplCode(String tplCode);

    /**
     * 更新ummTemplate
     *
     * @param ummTemplate 待更新ummTemplate对象
     * @return true，更新成功；false，ummTemplate不存在
     */
    boolean update(UmmTemplate ummTemplate);

    /**
     * 通过 id 删除ummTemplate
     *
     * @param id 账户 id
     * @return true，删除成功；false，ummTemplate不存在
     */
    boolean deleteById(Long id);

    /**
     * 批量逻辑删除消息模板
     * @param idList 消息模板id列表
     * @return 删除结果
     */
    boolean batchDelete(List<Long> idList);

    /**
     * 分页查询消息模板
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<PageMessageTemplateDTO> pageQuery(Page<PageMessageTemplateDTO> page, MessageTemplateQueryDTO query);


    /**
     * 统计标题数量
     * @param msgTitle 标题
     * @param msgId 消息id
     * @return 数量
     */
    int countTitle(String msgTitle, Long msgId);

    /**
     * 通过栏目id获取模板
     * @param categoryId 栏目id
     * @return 模板列表
     */
    List<UmmTemplate> getByCategoryId(Long categoryId);

    /**
     * 通过源id获取模版
     * @param sourceId
     * @return
     */
    List<UmmTemplate> getBySourceId(Long sourceId);
}