package com.aliyun.gts.utc.integration.client.foura.impl;

import com.aliyun.gts.utc.integration.client.foura.UserCenterClient;
import com.aliyun.gts.utc.spi.UserServiceSpi;
import com.aliyun.gts.utc.spi.dto.User;
import com.aliyun.gts.utc.spi.request.UserQueryRequest;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.model.user.UserSimpleInfoDTO;
import com.yunqiao.foura.service.request.user.UserConditionDTO;
import com.yunqiao.foura.service.response.user.UserSimplePageResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: yaohao.yh
 * @date: 2023/9/10
 */
@Service
public class UserServiceSpiImpl implements UserServiceSpi {
    @Autowired
    private UserCenterClient userCenterClient;

    @Override
    public User queryUser(String uid) {
        User user = new User();
        UserInfoDTO userInfoDTO = userCenterClient.getUserDetail(uid);
        if (userInfoDTO == null) {
            return null;
        }
        user.setUserId(userInfoDTO.getBaseInfo().getUserId());
        user.setUserCode(userInfoDTO.getBaseInfo().getUserCode());
        user.setUserName(userInfoDTO.getBaseInfo().getUserName());
        user.setNickName(userInfoDTO.getBaseInfo().getNickName());
        user.setEmail(userInfoDTO.getBaseInfo().getEmail());
        user.setPhone(userInfoDTO.getBaseInfo().getPhone());
        return user;
    }

    @Override
    public List<User> queryUser(UserQueryRequest userQueryRequest) {
        UserConditionDTO userConditionDTO = new UserConditionDTO();
        BeanUtils.copyProperties(userQueryRequest, userConditionDTO);
        UserSimplePageResponse userSimplePageResponse = userCenterClient.listUsersByCondition(userConditionDTO);
        List<User> users = new ArrayList<>();
        userSimplePageResponse.getUsers()
                .forEach(userSimpleInfoDTO -> users.add(convert(userSimpleInfoDTO)));
        return users;
    }

    @Override
    public List<User> listMembersByOrgCode(List<String> orgGuidList) {
        List<User> allUsers = new ArrayList<>();
        for (String orgGuid : orgGuidList) {
            List<UserSimpleInfoDTO> userSimpleInfoDTOList = userCenterClient.listMembersByOrgCode(orgGuid);
            List<User> users = new ArrayList<>();
            userSimpleInfoDTOList.forEach(userSimpleInfoDTO -> users.add(convert(userSimpleInfoDTO)));
            allUsers.addAll(users);
        }
        return allUsers;
    }

    private User convert(UserSimpleInfoDTO userSimpleInfoDTO) {
        User user = new User();
        BeanUtils.copyProperties(userSimpleInfoDTO, user);
        user.setUserId(userSimpleInfoDTO.getUserId());
        user.setUserCode(userSimpleInfoDTO.getUserCode());
        user.setPhone(userSimpleInfoDTO.getMobilePhone());
        user.setUserName(userSimpleInfoDTO.getUsername());
        user.setNickName(userSimpleInfoDTO.getUsername());
        return user;
    }

    @Override
    public List<User> queryUser(List<String> uids) {
        List<User> users = new ArrayList<>();
        for (String uid : uids) {
            User user = queryUser(uid);
            if(user!=null){
                users.add(user);
            }
        }
        return users;
    }
}
