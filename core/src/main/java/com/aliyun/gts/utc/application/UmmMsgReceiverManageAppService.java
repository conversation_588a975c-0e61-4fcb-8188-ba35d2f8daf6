/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.application;

import com.aliyun.gts.utc.api.facade.request.ummmsgreceiver.CreateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgreceiver.PageQueryUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.facade.request.ummmsgreceiver.UpdateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.facade.response.ummmsgreceiver.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgReceiverManageAppService.java
 * source table name: umm_msg_receiver
 * initial code lines: 67
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
public interface UmmMsgReceiverManageAppService {

    /**
     * 创建UmmMsgReceiver
     *
     * @param createUmmMsgReceiverRequest 创建$UmmMsgReceiver请求
     * @return 创建UmmMsgReceiver响应
     */
    CreateUmmMsgReceiverResponse createUmmMsgReceiver(CreateUmmMsgReceiverRequest createUmmMsgReceiverRequest);

    /**
     * 查询UmmMsgReceiver
     *
     * @param id 查询UmmMsgReceiver请求
     * @return 查询UmmMsgReceiver响应
     */
    QueryUmmMsgReceiverResponse queryUmmMsgReceiver(Long id);

    /**
     * 分页查询UmmMsgReceiver
     *
     * @param pageQueryUmmMsgReceiverRequest 查询UmmMsgReceiver请求
     * @return UmmMsgReceiver查询结果
     */
    PageQueryUmmMsgReceiverResponse pageQueryUmmMsgReceiver(PageQueryUmmMsgReceiverRequest pageQueryUmmMsgReceiverRequest);

    /**
     * 删除UmmMsgReceiver
     *
     * @param id 删除UmmMsgReceiver请求
     * @return UmmMsgReceiver删除结果
     */
    DeleteUmmMsgReceiverResponse deleteUmmMsgReceiver(Long id);

    /**
     * 更新UmmMsgReceiver
     *
     * @param updateUmmMsgReceiverRequest 更新UmmMsgReceiver请求
     * @return UmmMsgReceiver更新结果
     */
    UpdateUmmMsgReceiverResponse updateUmmMsgReceiver(UpdateUmmMsgReceiverRequest updateUmmMsgReceiverRequest);

}