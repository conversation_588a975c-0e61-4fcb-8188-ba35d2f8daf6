/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.domain;

import lombok.Data;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgParameter.java
 * source table name: umm_msg_parameter
 * initial code lines: 103
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Data
public class UmmMsgParameter {

    /**
    * 参数id
    */
    private Long id;

    /**
    * 
    */
    private String guid;

    /**
    * 参数名称
    */
    private String paramName;

    /**
    * 参数表达式
    */
    private String paramExp;

    /**
    * 状态 0禁用 1启用
    */
    private Integer status;

    /**
    * 创建人guid
    */
    private String createUserGuid;

    /**
    * 创建人姓名
    */
    private String createUserName;

    /**
    * 修改人guid
    */
    private String updateUserGuid;

    /**
    * 修改人姓名
    */
    private String updateUserName;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModify;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    private String features;

    /**
    * 逻辑删除
    */
    private Long isDeleted;

    /**
    * 版本号
    */
    private Integer version;

    /**
    * 应用id
    */
    private Long appId;

    /**
    * 租户编码
    */
    private String tenantCode;

}