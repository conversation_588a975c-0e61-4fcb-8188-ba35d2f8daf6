package com.aliyun.gts.utc.common.enums;

public enum UmmMessageSendMethodEnum {

    SEND_AT_ONCE(1,"立即发送"),
    SEND_AT_TIME(2,"定时发送")
    ;

    UmmMessageSendMethodEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
