/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.repository;

import com.aliyun.gts.utc.api.facade.request.parameter.PageQueryMessageParameterRequest;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgParameterDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface UmmMsgParameterRepository {

    /**
     * 通过 id 获取ummMsgParameter
     *
     * @param id ummMsgParameter id
     * @return ummMsgParameter；null 如果账户不存在
     */
    UmmMsgParameter getById(Long id);

    /**
     * 获取一个区间内的ummMsgParameter
     *
     * @param pageNum
     * @param pageSize 期望个数
     * @return 区间内的ummMsgParameter列表
     */
    IPage<UmmMsgParameter> getByRange(int pageNum, int pageSize,
                                      UmmMsgParameter ummMsgParameter);

    /**
     * 添加ummMsgParameter
     *
     * @param ummMsgParameter 待添加ummMsgParameter
     */
    UmmMsgParameter add(UmmMsgParameter ummMsgParameter);

    /**
     * 更新ummMsgParameter
     *
     * @param ummMsgParameter 待更新ummMsgParameter对象
     * @return true，更新成功；false，ummMsgParameter不存在
     */
    boolean update(UmmMsgParameter ummMsgParameter);

    /**
     * 通过 id 删除ummMsgParameter
     *
     * @param id 账户 id
     * @return true，删除成功；false，ummMsgParameter不存在
     */
    boolean deleteById(Long id);

    /**
     * 获取所有系统参数
     *
     * @return 系统参数列表
     */
    List<UmmMsgParameter> getAll();

    /**
     * 根据查询条件分页查询参数
     *
     * @param pageNum  页码
     * @param pageSize 页面大小
     * @param request  查询条件
     * @return 分页后的参数列表
     */
    Page<UmmMsgParameter> getPageQuery(int pageNum, int pageSize,
                                       PageQueryMessageParameterRequest request);

    List<UmmMsgParameterDO> query();
}