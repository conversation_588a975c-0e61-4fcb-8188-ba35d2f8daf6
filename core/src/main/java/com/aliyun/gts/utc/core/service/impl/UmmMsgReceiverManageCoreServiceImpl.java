/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgReceiverDTO;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgreceiver.CreateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgreceiver.PageQueryUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgreceiver.UpdateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgreceiver.*;
import com.aliyun.gts.utc.core.domain.UmmMsgReceiver;
import com.aliyun.gts.utc.core.repository.UmmMsgReceiverRepository;
import com.aliyun.gts.utc.core.service.UmmMsgReceiverManageCoreService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.stream.Collectors;

@Service
public class UmmMsgReceiverManageCoreServiceImpl implements UmmMsgReceiverManageCoreService {
    private final UmmMsgReceiverRepository ummMsgReceiverRepository;

    public UmmMsgReceiverManageCoreServiceImpl(UmmMsgReceiverRepository ummMsgReceiverRepository) {
        this.ummMsgReceiverRepository = ummMsgReceiverRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUmmMsgReceiverResponse createUmmMsgReceiver(CreateUmmMsgReceiverRequest createUmmMsgReceiverRequest) {

        UmmMsgReceiverDTO ummMsgReceiverDTO =
                createUmmMsgReceiverRequest.getUmmMsgReceiverDTO();

        UmmMsgReceiver ummMsgReceiver =
                UmmMsgReceiverDTOConverter.INSTANCE.convert(ummMsgReceiverDTO);
        UmmMsgReceiver add = ummMsgReceiverRepository.add(ummMsgReceiver);
        ummMsgReceiverDTO.setId(add.getId());

        CreateUmmMsgReceiverResponse response = new CreateUmmMsgReceiverResponse();
        response.setUmmMsgReceiverDTO(ummMsgReceiverDTO);

        return response;
    }

    @Override
    public QueryUmmMsgReceiverResponse queryUmmMsgReceiver(Long id) {

        UmmMsgReceiver ummMsgReceiver = ummMsgReceiverRepository.getById(id);
        UmmMsgReceiverDTO ummMsgReceiverDTO = UmmMsgReceiverDTOConverter.INSTANCE.convert(ummMsgReceiver);
        QueryUmmMsgReceiverResponse response = new QueryUmmMsgReceiverResponse();
        response.setUmmMsgReceiverDTO(ummMsgReceiverDTO);
        return response;
    }

    @Override
    public QueryUmmMsgReceiverResponse queryUmmMsgReceiverByMsgId(Long msgId) {
        UmmMsgReceiver ummMsgReceiver = ummMsgReceiverRepository.getByMsgId(msgId);
        UmmMsgReceiverDTO ummMsgReceiverDTO = UmmMsgReceiverDTOConverter.INSTANCE.convert(ummMsgReceiver);
        QueryUmmMsgReceiverResponse response = new QueryUmmMsgReceiverResponse();
        response.setUmmMsgReceiverDTO(ummMsgReceiverDTO);
        return response;
    }

    @Override
    public PageQueryUmmMsgReceiverResponse pageQueryUmmMsgReceiver(PageQueryUmmMsgReceiverRequest request) {

        UmmMsgReceiverDTO ummMsgReceiverDTO = request.getUmmMsgReceiverDTO();
        UmmMsgReceiver ummMsgReceiver = UmmMsgReceiverDTOConverter.INSTANCE.convert(ummMsgReceiverDTO);
        IPage<UmmMsgReceiver> byRange = ummMsgReceiverRepository.getByRange(request.getPageNum(), request.getPageSize(), ummMsgReceiver);

        PageQueryUmmMsgReceiverResponse response = new PageQueryUmmMsgReceiverResponse();
        response.setTotalCount(byRange.getTotal());
        response.setPageNum(byRange.getCurrent());
        response.setPageSize(byRange.getSize());
        response.setList(byRange.getRecords().stream()
                .map(UmmMsgReceiverDTOConverter.INSTANCE::convert)
                .collect(Collectors.toList()));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteUmmMsgReceiverResponse deleteUmmMsgReceiver(Long id) {
        DeleteUmmMsgReceiverResponse deleteUmmMsgReceiverResponse = new DeleteUmmMsgReceiverResponse();
        deleteUmmMsgReceiverResponse.setIsDelete(ummMsgReceiverRepository.deleteById(id));
        return deleteUmmMsgReceiverResponse;
    }

    @Override
    public UpdateUmmMsgReceiverResponse updateUmmMsgReceiver(UpdateUmmMsgReceiverRequest request) {

        UmmMsgReceiverDTO ummMsgReceiverDTO = request.getUmmMsgReceiverDTO();

        UmmMsgReceiver ummMsgReceiver = UmmMsgReceiverDTOConverter.INSTANCE.convert(ummMsgReceiverDTO);
        UpdateUmmMsgReceiverResponse updateUmmMsgReceiverResponse = new UpdateUmmMsgReceiverResponse();
        updateUmmMsgReceiverResponse.setIsUpdate(ummMsgReceiverRepository.update(ummMsgReceiver));
        return updateUmmMsgReceiverResponse;
    }

    @Mapper
    interface UmmMsgReceiverDTOConverter {
        UmmMsgReceiverDTOConverter INSTANCE = Mappers.getMapper(UmmMsgReceiverDTOConverter.class);

        UmmMsgReceiver convert(UmmMsgReceiverDTO ummMsgReceiverDTO);

        UmmMsgReceiverDTO convert(UmmMsgReceiver ummMsgReceiver);
    }

}