package com.aliyun.gts.utc.dal.dao;

import com.aliyun.gts.utc.api.dto.UmmSourceDTO;
import com.aliyun.gts.utc.dal.dataobject.UmmSourceDO;
import com.aliyun.gts.utc.dal.dto.MessageSourceQueryDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
@Mapper
@Component
public interface UmmSourceMapper extends BaseMapper<UmmSourceDO> {

    Page<UmmSourceDTO> pageQuery(Page<UmmSourceDTO> page, @Param("dto")MessageSourceQueryDTO query);
}
