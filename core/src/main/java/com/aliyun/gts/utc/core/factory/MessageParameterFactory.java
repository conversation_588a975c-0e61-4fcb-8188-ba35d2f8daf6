package com.aliyun.gts.utc.core.factory;

import com.aliyun.gts.utc.api.facade.response.parameter.PageQueryMessageParameterResponse;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;

/**
 * <AUTHOR>
 * @date 2021/6/5 17:41
 */
public class MessageParameterFactory {

    public static PageQueryMessageParameterResponse changeReturn(UmmMsgParameter ummMsgParameter){
        PageQueryMessageParameterResponse response = new PageQueryMessageParameterResponse();
        response.setExpression(ummMsgParameter.getParamExp());
        response.setName(ummMsgParameter.getParamName());
        response.setId(ummMsgParameter.getId());
        return response;
    }
}
