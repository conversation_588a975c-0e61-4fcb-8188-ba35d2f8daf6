DROP TABLE IF EXISTS `umm_template`;
CREATE TABLE `umm_template`
(
    `id`               BIGINT(20) UNSIGNED NOT NULL,
    `guid`             VARCHAR(36) NOT NULL,
    `tpl_title`        VARCHAR(200)  DEFAULT NULL,
    `tpl_content`      VARCHAR(4000) DEFAULT NULL,
    `param`            JSON( null) DEFAULT NULL,
    `support_channels` JSON( null) DEFAULT NULL,
    `status`           INT(10) DEFAULT '1',
    `create_user_guid` VARCHAR(36)   DEFAULT NULL,
    `create_user_name` VARCHAR(128)  DEFAULT NULL,
    `update_user_guid` VARCHAR(36)   DEFAULT NULL,
    `update_user_name` VARCHAR(128)  DEFAULT NULL,
    `gmt_create`       DATETIME(0) DEFAULT NULL,
    `gmt_modify`       DATETIME(0) DEFAULT NULL,
    `features`         <PERSON><PERSON><PERSON>( null) DEFAULT NULL,
    `is_deleted`       BIGINT(19) DEFAULT NULL,
    `version`          INT(10) DEFAULT NULL,
    `app_id`           BIGINT(19) DEFAULT NULL,
    `tenant_code`      VARCHAR(32)   DEFAULT NULL,
    PRIMARY KEY (`id`)
);
