DROP TABLE IF EXISTS `umm_msg_channel`;
CREATE TABLE `umm_msg_channel`
(
    `id`          BIGINT(20) UNSIGNED NOT NULL,
    `msg_id`      BIGINT(20) UNSIGNED NOT NULL,
    `channel_id`  BIGINT(20) UNSIGNED NOT NULL,
    `start_date`  DATE(10) DEFAULT NULL,
    `end_date`    DATE(10) DEFAULT NULL,
    `tenant_code` VARCHAR(32) DEFAULT NULL,
    `app_id`      BIGINT(19) DEFAULT NULL,
    `status`      INT(10) DEFAULT '1',
    PRIMARY KEY (`id`)
);
