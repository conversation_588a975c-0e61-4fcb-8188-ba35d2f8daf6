DROP TABLE IF EXISTS `umm_msg_receiver`;
CREATE TABLE `umm_msg_receiver`
(
    `id`            BIGINT(20) UNSIGNED NOT NULL,
    `msg_id`        BIGINT(20) UNSIGNED NOT NULL,
    `receiver_type` CHAR(3) NOT NULL,
    `receiver_guid` VARCHAR(36) DEFAULT NULL,
    `start_date`    DATE(10) DEFAULT NULL,
    `end_date`      DATE(10) DEFAULT NULL,
    `tenant_code`   VARCHAR(32) DEFAULT NULL,
    `app_id`        BIGINT(19) DEFAULT NULL,
    `status`        INT(10) DEFAULT '1',
    PRIMARY KEY (`id`)
);
