DROP TABLE IF EXISTS `umm_message`;
CREATE TABLE `umm_message`
(
    `id`               BIGINT(20) UNSIGNED NOT NULL,
    `tpl_id`           BIGINT(20) UNSIGNED DEFAULT NULL,
    `guid`             VARCHAR(36) NOT NULL,
    `msg_title`        VARCHAR(200)  DEFAULT NULL,
    `msg_content`      VARCHAR(4000) DEFAULT NULL,
    `param`            JSON( null) DEFAULT NULL,
    `send_method`      INT(10) DEFAULT NULL,
    `send_time`        DATETIME(0) DEFAULT NULL,
    `actual_send_time` DATETIME(0) DEFAULT NULL,
    `category_id`      BIGINT(19) DEFAULT NULL,
    `status`           INT(10) DEFAULT '1',
    `create_user_guid` VARCHAR(36)   DEFAULT NULL,
    `create_user_name` VARCHAR(128)  DEFAULT NULL,
    `update_user_guid` VARCHAR(36)   DEFAULT NULL,
    `update_user_name` VARCHAR(128)  DEFAULT NULL,
    `gmt_create`       DATETIME(0) DEFAULT NULL,
    `gmt_modify`       DATETIME(0) DEFAULT NULL,
    `features`         JSON( null) DEFAULT NULL,
    `is_deleted`       BIGINT(19) DEFAULT NULL,
    `version`          INT(10) DEFAULT NULL,
    `app_id`           BIGINT(19) DEFAULT NULL,
    `tenant_code`      VARCHAR(32)   DEFAULT NULL,
    PRIMARY KEY (`id`)
);
