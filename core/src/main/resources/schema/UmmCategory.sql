DROP TABLE IF EXISTS `umm_category`;
CREATE TABLE `umm_category`
(
    `id`               BIGINT(19) NOT NULL,
    `guid`             VARCHAR(36) NOT NULL,
    `category_name`    VA<PERSON>HAR(64)  DEFAULT NULL,
    `parent_id`        BIGINT(19) DEFAULT NULL,
    `status`           INT(10) DEFAULT '1',
    `create_user_guid` VARCHAR(36)  DEFAULT NULL,
    `create_user_name` VARCHAR(128) DEFAULT NULL,
    `update_user_guid` VARCHAR(36)  DEFAULT NULL,
    `update_user_name` VARCHAR(128) DEFAULT NULL,
    `gmt_create`       DATETIME(0) DEFAULT NULL,
    `gmt_modify`       DATETIME(0) DEFAULT NULL,
    `features`         JSON( null) DEFAULT NULL,
    `is_deleted`       BIGINT(19) DEFAULT NULL,
    `version`          INT(10) DEFAULT NULL,
    `app_id`           BIGINT(19) DEFAULT NULL,
    `tenant_code`      VARCHAR(32)  DEFAULT NULL,
    <PERSON><PERSON>AR<PERSON> KEY (`id`)
);
