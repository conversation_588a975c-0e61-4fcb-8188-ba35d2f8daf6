DROP TABLE IF EXISTS `umm_msg_send`;
CREATE TABLE `umm_msg_send`
(
    `id`               BIGINT(20) UNSIGNED NOT NULL,
    `guid`             VARCHAR(36) NOT NULL,
    `user_guid`        VARCHAR(36) NOT NULL,
    `user_code`        VA<PERSON>HAR(128)  DEFAULT NULL,
    `user_name`        VA<PERSON>HAR(128)  DEFAULT NULL,
    `user_org`         VARCHAR(256)  DEFAULT NULL,
    `user_postion`     VARCHAR(256)  DEFAULT NULL,
    `email`            VARCHAR(128)  DEFAULT NULL,
    `phone`            VARCHAR(128)  DEFAULT NULL,
    `msg_id`           VARCHAR(200)  DEFAULT NULL,
    `msg_title`        VARCHAR(200)  DEFAULT NULL,
    `msg_content`      VARCHAR(4000) DEFAULT NULL,
    `channel_id`       BIGINT(20) UNSIGNED DEFAULT NULL,
    `send_time`        DATETIME(0) DEFAULT NULL,
    `status`           INT(10) DEFAULT '1',
    `create_user_guid` VARCHAR(36)   DEFAULT NULL,
    `create_user_name` VARCHAR(128)  DEFAULT NULL,
    `update_user_guid` VARCHAR(36)   DEFAULT NULL,
    `update_user_name` VARCHAR(128)  DEFAULT NULL,
    `gmt_create`       DATETIME(0) DEFAULT NULL,
    `gmt_modify`       DATETIME(0) DEFAULT NULL,
    `features`         JSON( null) DEFAULT NULL,
    `is_deleted`       BIGINT(19) DEFAULT NULL,
    `version`          INT(10) DEFAULT NULL,
    `app_id`           BIGINT(19) DEFAULT NULL,
    `tenant_code`      VARCHAR(32)   DEFAULT NULL,
    PRIMARY KEY (`id`)
);
