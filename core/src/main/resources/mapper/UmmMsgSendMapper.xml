<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.gts.utc.dal.dao.UmmMsgSendMapper">

    <resultMap id="ummMsgSendDOMap" type="com.aliyun.gts.utc.dal.dataobject.UmmMsgSendDO">
        <id property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="userGuid" column="user_guid"/>
        <result property="userCode" column="user_code"/>
        <result property="userName" column="user_name"/>
        <result property="userOrg" column="user_org"/>
        <result property="userPostion" column="user_postion"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="msgId" column="msg_id"/>
        <result property="msgTitle" column="msg_title"/>
        <result property="msgContent" column="msg_content"/>
        <result property="channelId" column="channel_id"/>
        <result property="sendTime" column="send_time"/>
        <result property="status" column="status"/>
        <result property="readTime" column="read_time"/>
        <result property="readStatus" column="read_status"/>
        <result property="createUserGuid" column="create_user_guid"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateUserGuid" column="update_user_guid"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="features" column="features"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="version" column="version"/>
        <result property="appId" column="app_id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="retryRule" column="retry_rule"/>
    </resultMap>

    <insert id="batchAdd">
        insert into
        umm_msg_send(guid,user_guid,user_code,user_name
        ,user_org,user_postion,email,phone,msg_id
        ,msg_title,msg_content,channel_id,send_time,status,create_user_guid,create_user_name,gmt_create,
        update_user_guid,update_user_name,tenant_code)
        values
        <foreach collection="dto" item="item" separator=",">
            (#{item.guid}, #{item.userGuid}, #{item.userCode}, #{item.userName},
             #{item.userOrg}, #{item.userPostion},#{item.email},#{item.phone},#{item.msgId},
            #{item.msgTitle}, #{item.msgContent}, #{item.channelId},#{item.sendTime},#{item.status},
            #{item.createUserGuid}, #{item.createUserName}, #{item.gmtCreate},
             #{item.updateUserGuid}, #{item.updateUserName}, #{item.tenantCode})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE umm_msg_send
            <set>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.sendTime != null">
                    send_time = #{item.sendTime},
                </if>
                gmt_modify = NOW()
            </set>
            WHERE id = ${item.id}
        </foreach>
    </update>
</mapper>
