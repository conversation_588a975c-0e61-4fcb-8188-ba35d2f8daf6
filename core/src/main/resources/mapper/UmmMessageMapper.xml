<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.gts.utc.dal.dao.UmmMessageMapper">

    <resultMap id="ummMessageDOMap" type="com.aliyun.gts.utc.dal.dataobject.UmmMessageDO">
        <id property="id" column="id"/>
        <result property="tplId" column="tpl_id"/>
        <result property="guid" column="guid"/>
        <result property="msgTitle" column="msg_title"/>
        <result property="msgContent" column="msg_content"/>
        <result property="param" column="param"/>
        <result property="sendMethod" column="send_method"/>
        <result property="sendTime" column="send_time"/>
        <result property="actualSendTime" column="actual_send_time"/>
        <result property="categoryId" column="category_id"/>
        <result property="status" column="status"/>
        <result property="createUserGuid" column="create_user_guid"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateUserGuid" column="update_user_guid"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="features" column="features"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="version" column="version"/>
        <result property="appId" column="app_id"/>
        <result property="tenantCode" column="tenant_code"/>
    </resultMap>
    <select id="pageQuery"
            resultType="com.aliyun.gts.utc.dal.dataobject.UmmMessageDO">
    select *
        <include refid="condition"/>
        limit #{offset}, #{pageSize}
    </select>

    <sql id="condition">
        from umm_message as um
        <where>
            um.is_deleted = 0
            and um.tenant_code = #{dto.tenantCode}
            <if test="dto.sendMethod">
                and um.send_method = #{dto.sendMethod}
            </if>
            <if test="dto.status!=null">
                and um.status = #{dto.status}
            </if>
            <if test="dto.categoryIdList !=null and dto.categoryIdList.size()>0">
                and um.category_id in
                <foreach collection="dto.categoryIdList" item="item" index="index"
                         open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.submitDateStart !=null and dto.submitDateEnd != null">
                and (um.gmt_modify between #{dto.submitDateStart} and
                #{dto.submitDateEnd})
            </if>
            <if test="dto.msgTitle !=null">
                and um.msg_title like concat('%',#{dto.msgTitle},'%')
            </if>
            <if test="dto.updateUserName != null">
                and um.update_user_name like concat('%',#{dto.updateUserName},'%')
            </if>
            <if test="null != dto.orderField and null != dto.isAsc">
                <if test="dto.isAsc">
                    order by ${dto.orderField} asc
                </if>
                <if test="!dto.isAsc">
                    order by ${dto.orderField} desc
                </if>
            </if>
            <if test="null == dto.orderField">
                order by um.gmt_modify desc
            </if>
        </where>

    </sql>

    <select id="count" resultType="java.lang.Long">
        SELECT count(*)
        <include refid="condition"/>
    </select>

    <update id="updateStatus">
        UPDATE umm_message
        SET
        `status` =
        IF
        ( success_num = 0, 5, IF ( total_num <![CDATA[<=]]> success_num, 4, 3 )
        )
        WHERE
        id = #{msgId}
        AND tenant_code = #{tenantCode}
    </update>

    <select id="selectByDaysBefore"
            resultType="com.aliyun.gts.utc.api.facade.response.ummmessage.StatisticsMessageResponse">
        SELECT
            message.total_num,
            message.success_num,
            message.actual_send_time as send_time,
            source.source_type
        FROM
            umm_message message
                INNER JOIN umm_template template ON message.tpl_id = template.id
                INNER JOIN umm_source source ON source.id = template.source_id
        WHERE
            message.total_num is Not null and
            message.is_deleted = 0 and
            source.source_type in ( "MAIL", "SMS") and
            message.actual_send_time BETWEEN DATE_FORMAT( DATE_SUB( CURDATE(), INTERVAL  #{days}  DAY ), '%Y-%m-%d 00:00:00' )
            AND
            DATE_FORMAT(CURDATE() - INTERVAL 1 DAY,'%Y-%m-%d 23:59:59')
    </select>
</mapper>
