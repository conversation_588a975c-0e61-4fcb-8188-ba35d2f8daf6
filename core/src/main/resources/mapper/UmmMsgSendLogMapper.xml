<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.gts.utc.dal.dao.UmmMsgSendLogMapper">

    <resultMap id="ummMsgSendLogDOMap" type="com.aliyun.gts.utc.dal.dataobject.UmmMsgSendLogDO">
        <id property="id" column="id"/>
        <result property="msgSendId" column="msg_send_id"/>
        <result property="messageIn" column="message_in"/>
        <result property="messageOut" column="message_out"/>
        <result property="sendTime" column="send_time"/>
        <result property="status" column="status"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="tenantCode" column="tenant_code"/>
    </resultMap>

    <insert id="batchAdd">
        insert into
        umm_msg_send_log(msg_send_id,message_in,message_out,send_time
        ,status,gmt_create,gmt_modify, tenant_code)
        values
        <foreach collection="dto" item="item" separator=",">
            (#{item.msgSendId}, #{item.messageIn}, #{item.messageOut}, #{item.sendTime},
            #{item.status}, #{item.gmtCreate},#{item.gmtModify}, #{item.tenantCode})
        </foreach>
    </insert>

</mapper>