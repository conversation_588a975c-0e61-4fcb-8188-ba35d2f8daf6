<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.gts.utc.dal.dao.UmmMsgChannelMapper">

    <resultMap id="ummMsgChannelDOMap" type="com.aliyun.gts.utc.dal.dataobject.UmmMsgChannelDO">
        <id property="id" column="id"/>
        <result property="msgId" column="msg_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="appId" column="app_id"/>
        <result property="status" column="status"/>
    </resultMap>

    <insert id="batchAdd">
        insert into
        umm_msg_channel(msg_id,channel_id, tenant_code)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.msgId}, #{item.channelId}, #{item.tenantCode})
        </foreach>
    </insert>
</mapper>
