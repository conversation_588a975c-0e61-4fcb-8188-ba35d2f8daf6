<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.gts.utc.dal.dao.UmmCategoryMapper">

    <resultMap id="ummCategoryDOMap" type="com.aliyun.gts.utc.dal.dataobject.UmmCategoryDO">
        <id property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="categoryName" column="category_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="status" column="status"/>
        <result property="createUserGuid" column="create_user_guid"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateUserGuid" column="update_user_guid"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="features" column="features"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="version" column="version"/>
        <result property="appId" column="app_id"/>
        <result property="tenantCode" column="tenant_code"/>
    </resultMap>

</mapper>