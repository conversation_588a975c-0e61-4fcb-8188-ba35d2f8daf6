<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.gts.utc.dal.dao.UmmMsgParameterMapper">

    <resultMap id="ummMsgParameterDOMap" type="com.aliyun.gts.utc.dal.dataobject.UmmMsgParameterDO">
        <id property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="paramName" column="param_name"/>
        <result property="paramExp" column="param_exp"/>
        <result property="status" column="status"/>
        <result property="createUserGuid" column="create_user_guid"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateUserGuid" column="update_user_guid"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="features" column="features"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="version" column="version"/>
        <result property="appId" column="app_id"/>
        <result property="tenantCode" column="tenant_code"/>
    </resultMap>

</mapper>