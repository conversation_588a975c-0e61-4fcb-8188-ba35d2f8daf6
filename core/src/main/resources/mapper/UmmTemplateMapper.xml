<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.gts.utc.dal.dao.UmmTemplateMapper">

    <resultMap id="ummTemplateDOMap" type="com.aliyun.gts.utc.dal.dataobject.UmmTemplateDO">
        <id property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="tplTitle" column="tpl_title"/>
        <result property="tplContent" column="tpl_content"/>
        <result property="param" column="param"/>
        <result property="categoryId" column="category_id"/>
        <result property="supportChannels" column="support_channels"/>
        <result property="status" column="status"/>
        <result property="createUserGuid" column="create_user_guid"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateUserGuid" column="update_user_guid"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="features" column="features"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="version" column="version"/>
        <result property="appId" column="app_id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="tplCode" column="tpl_code"/>
        <result property="sourceId" column="source_id"/>
    </resultMap>

    <select id="pageQuery" resultType="com.aliyun.gts.utc.dal.dto.PageMessageTemplateDTO">
        select
            ut.id as id,ut.tpl_title as msgTitle, ut.gmt_modify as gmtModify, ut.features as features,
            ut.update_user_name as submitName,uc.category_name as categoryName,
            ut.category_id as categoryId, ut.source_id as sourceId
        from
            umm_template as ut
        left join
            umm_category as uc on ut.category_id = uc.id
        where
            ut.is_deleted = 0
        and
            uc.is_deleted = 0
        and
            ut.tenant_code = #{dto.tenantCode}
        <if test="null != dto.titleQuery and '' != dto.titleQuery">
            and ut.tpl_title like #{dto.titleQuery}
        </if>
        <if test="null != dto.submitNameQuery and '' != dto.submitNameQuery">
            and ut.update_user_name like #{dto.submitNameQuery}
        </if>
        <if test="null != dto.categoryId">
            and ut.category_id = #{dto.categoryId}
        </if>
        <if test="null != dto.categoryIdList and dto.categoryIdList.size() >0">
            and ut.category_id in
            <foreach collection="dto.categoryIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by ut.id
        <if test="null != dto.orderField and null != dto.isAsc">
            <if test="dto.isAsc">
                order by ${dto.orderField} asc
            </if>
            <if test="!dto.isAsc">
                order by ${dto.orderField} desc
            </if>
        </if>
        <if test="null == dto.orderField">
            order by ut.gmt_modify desc
        </if>
    </select>

</mapper>