<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.gts.utc.dal.dao.UmmSourceMapper">

    <resultMap id="ummSourceDOMap" type="com.aliyun.gts.utc.dal.dto.MessageSourceQueryDTO">
        <id property="id" column="id"/>
        <result property="sourceName" column="source_name"/>
        <result property="sourceCode" column="source_code"/>
        <result property="sourceType" column="source_type"/>
        <result property="sourceParam" column="source_param"/>
        <result property="createUserGuid" column="create_user_guid"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateUserGuid" column="update_user_guid"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="version" column="version"/>
        <result property="tenantCode" column="tenant_code"/>
    </resultMap>

    <select id="pageQuery" resultType="com.aliyun.gts.utc.api.dto.UmmSourceDTO">
        select
        id as id,
        source_name as sourceName,
        source_code as sourceCode,
        source_type as sourceType,
        source_param as sourceParam,
        create_user_guid as createUserGuid,
        create_user_name as createUserName,
        update_user_guid as updateUserGuid,
        update_user_name as updateUserName,
        gmt_create as gmtCreate,
        gmt_modify as gmtModify,
        tenant_code as tenantCode
        from
        umm_source
        where
        is_deleted = 0
        and tenant_code = #{dto.tenantCode}
        <if test="null != dto.sourceName and '' != dto.sourceName">
            and source_name like #{dto.sourceName}
        </if>
        <if test="null != dto.updateUserName and '' != dto.updateUserName">
            and update_user_name like #{dto.updateUserName}
        </if>
        <if test="null != dto.sourceType and '' != dto.sourceType">
            and source_type like #{dto.sourceType}
        </if>
        <if test="null != dto.orderField and null != dto.isAsc">
            <if test="dto.isAsc">
                order by ${dto.orderField} asc
            </if>
            <if test="!dto.isAsc">
                order by ${dto.orderField} desc
            </if>
        </if>
        <if test="null == dto.orderField">
            order by gmt_modify desc
        </if>
    </select>

</mapper>