package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmTemplate;
import com.aliyun.gts.utc.dal.dataobject.UmmTemplateDO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmTemplateRepositoryImpl$UmmTemplateDOConverterImpl implements UmmTemplateRepositoryImpl.UmmTemplateDOConverter {

    @Override
    public UmmTemplateDO convert(UmmTemplate ummTemplate) {
        if ( ummTemplate == null ) {
            return null;
        }

        UmmTemplateDO ummTemplateDO = new UmmTemplateDO();

        ummTemplateDO.setGuid( ummTemplate.getGuid() );
        ummTemplateDO.setCreateUserGuid( ummTemplate.getCreateUserGuid() );
        ummTemplateDO.setCreateUserName( ummTemplate.getCreateUserName() );
        ummTemplateDO.setUpdateUserGuid( ummTemplate.getUpdateUserGuid() );
        ummTemplateDO.setUpdateUserName( ummTemplate.getUpdateUserName() );
        ummTemplateDO.setGmtCreate( ummTemplate.getGmtCreate() );
        ummTemplateDO.setGmtModify( ummTemplate.getGmtModify() );
        ummTemplateDO.setIsDeleted( ummTemplate.getIsDeleted() );
        ummTemplateDO.setVersion( ummTemplate.getVersion() );
        ummTemplateDO.setAppId( ummTemplate.getAppId() );
        ummTemplateDO.setTenantCode( ummTemplate.getTenantCode() );
        ummTemplateDO.setId( ummTemplate.getId() );
        ummTemplateDO.setTplTitle( ummTemplate.getTplTitle() );
        ummTemplateDO.setTplContent( ummTemplate.getTplContent() );
        ummTemplateDO.setTplCode( ummTemplate.getTplCode() );
        ummTemplateDO.setParam( ummTemplate.getParam() );
        ummTemplateDO.setCategoryId( ummTemplate.getCategoryId() );
        ummTemplateDO.setSupportChannels( ummTemplate.getSupportChannels() );
        ummTemplateDO.setStatus( ummTemplate.getStatus() );
        ummTemplateDO.setFeatures( ummTemplate.getFeatures() );
        ummTemplateDO.setSourceId( ummTemplate.getSourceId() );

        return ummTemplateDO;
    }

    @Override
    public UmmTemplate convert(UmmTemplateDO ummTemplateDO) {
        if ( ummTemplateDO == null ) {
            return null;
        }

        UmmTemplate ummTemplate = new UmmTemplate();

        ummTemplate.setId( ummTemplateDO.getId() );
        ummTemplate.setGuid( ummTemplateDO.getGuid() );
        ummTemplate.setTplTitle( ummTemplateDO.getTplTitle() );
        ummTemplate.setTplContent( ummTemplateDO.getTplContent() );
        ummTemplate.setTplCode( ummTemplateDO.getTplCode() );
        ummTemplate.setParam( ummTemplateDO.getParam() );
        ummTemplate.setCategoryId( ummTemplateDO.getCategoryId() );
        ummTemplate.setSupportChannels( ummTemplateDO.getSupportChannels() );
        ummTemplate.setStatus( ummTemplateDO.getStatus() );
        ummTemplate.setCreateUserGuid( ummTemplateDO.getCreateUserGuid() );
        ummTemplate.setCreateUserName( ummTemplateDO.getCreateUserName() );
        ummTemplate.setUpdateUserGuid( ummTemplateDO.getUpdateUserGuid() );
        ummTemplate.setUpdateUserName( ummTemplateDO.getUpdateUserName() );
        ummTemplate.setGmtCreate( ummTemplateDO.getGmtCreate() );
        ummTemplate.setGmtModify( ummTemplateDO.getGmtModify() );
        ummTemplate.setFeatures( ummTemplateDO.getFeatures() );
        ummTemplate.setIsDeleted( ummTemplateDO.getIsDeleted() );
        ummTemplate.setVersion( ummTemplateDO.getVersion() );
        ummTemplate.setAppId( ummTemplateDO.getAppId() );
        ummTemplate.setTenantCode( ummTemplateDO.getTenantCode() );
        ummTemplate.setSourceId( ummTemplateDO.getSourceId() );

        return ummTemplate;
    }
}
