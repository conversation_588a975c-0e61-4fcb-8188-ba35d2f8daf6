package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgChannelDTO;
import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgChannelManageCoreServiceImpl$UmmMsgChannelDTOConverterImpl implements UmmMsgChannelManageCoreServiceImpl.UmmMsgChannelDTOConverter {

    @Override
    public UmmMsgChannel convert(UmmMsgChannelDTO ummMsgChannelDTO) {
        if ( ummMsgChannelDTO == null ) {
            return null;
        }

        UmmMsgChannel ummMsgChannel = new UmmMsgChannel();

        ummMsgChannel.setId( ummMsgChannelDTO.getId() );
        ummMsgChannel.setMsgId( ummMsgChannelDTO.getMsgId() );
        ummMsgChannel.setChannelId( ummMsgChannelDTO.getChannelId() );
        ummMsgChannel.setStartDate( ummMsgChannelDTO.getStartDate() );
        ummMsgChannel.setEndDate( ummMsgChannelDTO.getEndDate() );
        ummMsgChannel.setTenantCode( ummMsgChannelDTO.getTenantCode() );
        ummMsgChannel.setAppId( ummMsgChannelDTO.getAppId() );
        ummMsgChannel.setStatus( ummMsgChannelDTO.getStatus() );

        return ummMsgChannel;
    }

    @Override
    public UmmMsgChannelDTO convert(UmmMsgChannel ummMsgChannel) {
        if ( ummMsgChannel == null ) {
            return null;
        }

        UmmMsgChannelDTO ummMsgChannelDTO = new UmmMsgChannelDTO();

        ummMsgChannelDTO.setId( ummMsgChannel.getId() );
        ummMsgChannelDTO.setMsgId( ummMsgChannel.getMsgId() );
        ummMsgChannelDTO.setChannelId( ummMsgChannel.getChannelId() );
        ummMsgChannelDTO.setStartDate( ummMsgChannel.getStartDate() );
        ummMsgChannelDTO.setEndDate( ummMsgChannel.getEndDate() );
        ummMsgChannelDTO.setTenantCode( ummMsgChannel.getTenantCode() );
        ummMsgChannelDTO.setAppId( ummMsgChannel.getAppId() );
        ummMsgChannelDTO.setStatus( ummMsgChannel.getStatus() );

        return ummMsgChannelDTO;
    }
}
