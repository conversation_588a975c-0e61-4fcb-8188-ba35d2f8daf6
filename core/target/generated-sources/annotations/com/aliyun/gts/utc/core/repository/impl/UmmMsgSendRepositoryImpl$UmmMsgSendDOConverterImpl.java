package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgSendDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgSendRepositoryImpl$UmmMsgSendDOConverterImpl implements UmmMsgSendRepositoryImpl.UmmMsgSendDOConverter {

    @Override
    public UmmMsgSendDO convert(UmmMsgSend ummMsgSend) {
        if ( ummMsgSend == null ) {
            return null;
        }

        UmmMsgSendDO ummMsgSendDO = new UmmMsgSendDO();

        ummMsgSendDO.setId( ummMsgSend.getId() );
        ummMsgSendDO.setGuid( ummMsgSend.getGuid() );
        ummMsgSendDO.setUserGuid( ummMsgSend.getUserGuid() );
        ummMsgSendDO.setUserCode( ummMsgSend.getUserCode() );
        ummMsgSendDO.setUserName( ummMsgSend.getUserName() );
        ummMsgSendDO.setUserOrg( ummMsgSend.getUserOrg() );
        ummMsgSendDO.setUserPostion( ummMsgSend.getUserPostion() );
        ummMsgSendDO.setEmail( ummMsgSend.getEmail() );
        ummMsgSendDO.setPhone( ummMsgSend.getPhone() );
        ummMsgSendDO.setMsgId( ummMsgSend.getMsgId() );
        ummMsgSendDO.setMsgTitle( ummMsgSend.getMsgTitle() );
        ummMsgSendDO.setMsgContent( ummMsgSend.getMsgContent() );
        ummMsgSendDO.setChannelId( ummMsgSend.getChannelId() );
        ummMsgSendDO.setSendTime( ummMsgSend.getSendTime() );
        ummMsgSendDO.setStatus( ummMsgSend.getStatus() );
        ummMsgSendDO.setReadStatus( ummMsgSend.getReadStatus() );
        ummMsgSendDO.setReadTime( ummMsgSend.getReadTime() );
        ummMsgSendDO.setCreateUserGuid( ummMsgSend.getCreateUserGuid() );
        ummMsgSendDO.setCreateUserName( ummMsgSend.getCreateUserName() );
        ummMsgSendDO.setUpdateUserGuid( ummMsgSend.getUpdateUserGuid() );
        ummMsgSendDO.setUpdateUserName( ummMsgSend.getUpdateUserName() );
        ummMsgSendDO.setGmtCreate( ummMsgSend.getGmtCreate() );
        ummMsgSendDO.setGmtModify( ummMsgSend.getGmtModify() );
        ummMsgSendDO.setFeatures( ummMsgSend.getFeatures() );
        ummMsgSendDO.setIsDeleted( ummMsgSend.getIsDeleted() );
        ummMsgSendDO.setVersion( ummMsgSend.getVersion() );
        ummMsgSendDO.setAppId( ummMsgSend.getAppId() );
        ummMsgSendDO.setTenantCode( ummMsgSend.getTenantCode() );
        ummMsgSendDO.setRetryRule( ummMsgSend.getRetryRule() );

        return ummMsgSendDO;
    }

    @Override
    public UmmMsgSend convert(UmmMsgSendDO ummMsgSendDO) {
        if ( ummMsgSendDO == null ) {
            return null;
        }

        UmmMsgSend ummMsgSend = new UmmMsgSend();

        ummMsgSend.setId( ummMsgSendDO.getId() );
        ummMsgSend.setGuid( ummMsgSendDO.getGuid() );
        ummMsgSend.setUserGuid( ummMsgSendDO.getUserGuid() );
        ummMsgSend.setUserCode( ummMsgSendDO.getUserCode() );
        ummMsgSend.setUserName( ummMsgSendDO.getUserName() );
        ummMsgSend.setUserOrg( ummMsgSendDO.getUserOrg() );
        ummMsgSend.setUserPostion( ummMsgSendDO.getUserPostion() );
        ummMsgSend.setEmail( ummMsgSendDO.getEmail() );
        ummMsgSend.setPhone( ummMsgSendDO.getPhone() );
        ummMsgSend.setMsgId( ummMsgSendDO.getMsgId() );
        ummMsgSend.setMsgTitle( ummMsgSendDO.getMsgTitle() );
        ummMsgSend.setMsgContent( ummMsgSendDO.getMsgContent() );
        ummMsgSend.setChannelId( ummMsgSendDO.getChannelId() );
        ummMsgSend.setSendTime( ummMsgSendDO.getSendTime() );
        ummMsgSend.setStatus( ummMsgSendDO.getStatus() );
        ummMsgSend.setReadStatus( ummMsgSendDO.getReadStatus() );
        ummMsgSend.setReadTime( ummMsgSendDO.getReadTime() );
        ummMsgSend.setCreateUserGuid( ummMsgSendDO.getCreateUserGuid() );
        ummMsgSend.setCreateUserName( ummMsgSendDO.getCreateUserName() );
        ummMsgSend.setUpdateUserGuid( ummMsgSendDO.getUpdateUserGuid() );
        ummMsgSend.setUpdateUserName( ummMsgSendDO.getUpdateUserName() );
        ummMsgSend.setGmtCreate( ummMsgSendDO.getGmtCreate() );
        ummMsgSend.setGmtModify( ummMsgSendDO.getGmtModify() );
        ummMsgSend.setFeatures( ummMsgSendDO.getFeatures() );
        ummMsgSend.setIsDeleted( ummMsgSendDO.getIsDeleted() );
        ummMsgSend.setVersion( ummMsgSendDO.getVersion() );
        ummMsgSend.setAppId( ummMsgSendDO.getAppId() );
        ummMsgSend.setTenantCode( ummMsgSendDO.getTenantCode() );
        ummMsgSend.setRetryRule( ummMsgSendDO.getRetryRule() );

        return ummMsgSend;
    }

    @Override
    public List<UmmMsgSend> convert(List<UmmMsgSendDO> ummMsgSendDO) {
        if ( ummMsgSendDO == null ) {
            return null;
        }

        List<UmmMsgSend> list = new ArrayList<UmmMsgSend>( ummMsgSendDO.size() );
        for ( UmmMsgSendDO ummMsgSendDO1 : ummMsgSendDO ) {
            list.add( convert( ummMsgSendDO1 ) );
        }

        return list;
    }
}
