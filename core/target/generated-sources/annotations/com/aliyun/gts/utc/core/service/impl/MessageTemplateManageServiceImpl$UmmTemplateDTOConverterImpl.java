package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.dto.UmmTemplateDTO;
import com.aliyun.gts.utc.core.domain.UmmTemplate;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class MessageTemplateManageServiceImpl$UmmTemplateDTOConverterImpl implements MessageTemplateManageServiceImpl.UmmTemplateDTOConverter {

    @Override
    public UmmTemplate convert(UmmTemplateDTO ummTemplateDTO) {
        if ( ummTemplateDTO == null ) {
            return null;
        }

        UmmTemplate ummTemplate = new UmmTemplate();

        ummTemplate.setId( ummTemplateDTO.getId() );
        ummTemplate.setGuid( ummTemplateDTO.getGuid() );
        ummTemplate.setTplTitle( ummTemplateDTO.getTplTitle() );
        ummTemplate.setTplContent( ummTemplateDTO.getTplContent() );
        ummTemplate.setTplCode( ummTemplateDTO.getTplCode() );
        ummTemplate.setParam( ummTemplateDTO.getParam() );
        ummTemplate.setCategoryId( ummTemplateDTO.getCategoryId() );
        ummTemplate.setSupportChannels( ummTemplateDTO.getSupportChannels() );
        ummTemplate.setStatus( ummTemplateDTO.getStatus() );
        ummTemplate.setCreateUserGuid( ummTemplateDTO.getCreateUserGuid() );
        ummTemplate.setCreateUserName( ummTemplateDTO.getCreateUserName() );
        ummTemplate.setUpdateUserGuid( ummTemplateDTO.getUpdateUserGuid() );
        ummTemplate.setUpdateUserName( ummTemplateDTO.getUpdateUserName() );
        ummTemplate.setGmtCreate( ummTemplateDTO.getGmtCreate() );
        ummTemplate.setGmtModify( ummTemplateDTO.getGmtModify() );
        ummTemplate.setFeatures( ummTemplateDTO.getFeatures() );
        ummTemplate.setIsDeleted( ummTemplateDTO.getIsDeleted() );
        ummTemplate.setVersion( ummTemplateDTO.getVersion() );
        ummTemplate.setAppId( ummTemplateDTO.getAppId() );
        ummTemplate.setTenantCode( ummTemplateDTO.getTenantCode() );
        ummTemplate.setSourceId( ummTemplateDTO.getSourceId() );

        return ummTemplate;
    }

    @Override
    public UmmTemplateDTO convert(UmmTemplate ummTemplate) {
        if ( ummTemplate == null ) {
            return null;
        }

        UmmTemplateDTO ummTemplateDTO = new UmmTemplateDTO();

        ummTemplateDTO.setId( ummTemplate.getId() );
        ummTemplateDTO.setGuid( ummTemplate.getGuid() );
        ummTemplateDTO.setTplTitle( ummTemplate.getTplTitle() );
        ummTemplateDTO.setTplCode( ummTemplate.getTplCode() );
        ummTemplateDTO.setTplContent( ummTemplate.getTplContent() );
        ummTemplateDTO.setParam( ummTemplate.getParam() );
        ummTemplateDTO.setSupportChannels( ummTemplate.getSupportChannels() );
        ummTemplateDTO.setCategoryId( ummTemplate.getCategoryId() );
        ummTemplateDTO.setStatus( ummTemplate.getStatus() );
        ummTemplateDTO.setCreateUserGuid( ummTemplate.getCreateUserGuid() );
        ummTemplateDTO.setCreateUserName( ummTemplate.getCreateUserName() );
        ummTemplateDTO.setUpdateUserGuid( ummTemplate.getUpdateUserGuid() );
        ummTemplateDTO.setUpdateUserName( ummTemplate.getUpdateUserName() );
        ummTemplateDTO.setGmtCreate( ummTemplate.getGmtCreate() );
        ummTemplateDTO.setGmtModify( ummTemplate.getGmtModify() );
        ummTemplateDTO.setFeatures( ummTemplate.getFeatures() );
        ummTemplateDTO.setIsDeleted( ummTemplate.getIsDeleted() );
        ummTemplateDTO.setVersion( ummTemplate.getVersion() );
        ummTemplateDTO.setAppId( ummTemplate.getAppId() );
        ummTemplateDTO.setTenantCode( ummTemplate.getTenantCode() );
        ummTemplateDTO.setSourceId( ummTemplate.getSourceId() );

        return ummTemplateDTO;
    }
}
