package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmSource;
import com.aliyun.gts.utc.dal.dataobject.UmmSourceDO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmSourceRepositoryImpl$UmmSourceDOConverterImpl implements UmmSourceRepositoryImpl.UmmSourceDOConverter {

    @Override
    public UmmSource convert(UmmSourceDO ummSourceDO) {
        if ( ummSourceDO == null ) {
            return null;
        }

        UmmSource ummSource = new UmmSource();

        ummSource.setId( ummSourceDO.getId() );
        ummSource.setSourceName( ummSourceDO.getSourceName() );
        ummSource.setSourceCode( ummSourceDO.getSourceCode() );
        ummSource.setSourceType( ummSourceDO.getSourceType() );
        ummSource.setSourceParam( ummSourceDO.getSourceParam() );
        ummSource.setCreateUserGuid( ummSourceDO.getCreateUserGuid() );
        ummSource.setCreateUserName( ummSourceDO.getCreateUserName() );
        ummSource.setUpdateUserGuid( ummSourceDO.getUpdateUserGuid() );
        ummSource.setUpdateUserName( ummSourceDO.getUpdateUserName() );
        ummSource.setGmtCreate( ummSourceDO.getGmtCreate() );
        ummSource.setGmtModify( ummSourceDO.getGmtModify() );
        ummSource.setIsDeleted( ummSourceDO.getIsDeleted() );
        ummSource.setVersion( ummSourceDO.getVersion() );
        ummSource.setTenantCode( ummSourceDO.getTenantCode() );

        return ummSource;
    }

    @Override
    public UmmSourceDO convert(UmmSource ummSource) {
        if ( ummSource == null ) {
            return null;
        }

        UmmSourceDO ummSourceDO = new UmmSourceDO();

        ummSourceDO.setCreateUserGuid( ummSource.getCreateUserGuid() );
        ummSourceDO.setCreateUserName( ummSource.getCreateUserName() );
        ummSourceDO.setUpdateUserGuid( ummSource.getUpdateUserGuid() );
        ummSourceDO.setUpdateUserName( ummSource.getUpdateUserName() );
        ummSourceDO.setGmtCreate( ummSource.getGmtCreate() );
        ummSourceDO.setGmtModify( ummSource.getGmtModify() );
        ummSourceDO.setIsDeleted( ummSource.getIsDeleted() );
        ummSourceDO.setVersion( ummSource.getVersion() );
        ummSourceDO.setTenantCode( ummSource.getTenantCode() );
        ummSourceDO.setId( ummSource.getId() );
        ummSourceDO.setSourceName( ummSource.getSourceName() );
        ummSourceDO.setSourceCode( ummSource.getSourceCode() );
        ummSourceDO.setSourceType( ummSource.getSourceType() );
        ummSourceDO.setSourceParam( ummSource.getSourceParam() );

        return ummSourceDO;
    }
}
