package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.AllUmmCategoryDTO;
import com.aliyun.gts.utc.api.dto.UmmCategoryDTO;
import com.aliyun.gts.utc.core.domain.UmmCategory;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmCategoryManageAppServiceImpl$UmmCategoryDTOConverterImpl implements UmmCategoryManageAppServiceImpl.UmmCategoryDTOConverter {

    @Override
    public UmmCategory convert(UmmCategoryDTO ummCategoryDTO) {
        if ( ummCategoryDTO == null ) {
            return null;
        }

        UmmCategory ummCategory = new UmmCategory();

        ummCategory.setId( ummCategoryDTO.getId() );
        ummCategory.setGuid( ummCategoryDTO.getGuid() );
        ummCategory.setCategoryName( ummCategoryDTO.getCategoryName() );
        ummCategory.setCategoryCode( ummCategoryDTO.getCategoryCode() );
        ummCategory.setParentId( ummCategoryDTO.getParentId() );
        ummCategory.setStatus( ummCategoryDTO.getStatus() );
        ummCategory.setCreateUserGuid( ummCategoryDTO.getCreateUserGuid() );
        ummCategory.setCreateUserName( ummCategoryDTO.getCreateUserName() );
        ummCategory.setUpdateUserGuid( ummCategoryDTO.getUpdateUserGuid() );
        ummCategory.setUpdateUserName( ummCategoryDTO.getUpdateUserName() );
        ummCategory.setGmtCreate( ummCategoryDTO.getGmtCreate() );
        ummCategory.setGmtModify( ummCategoryDTO.getGmtModify() );
        ummCategory.setFeatures( ummCategoryDTO.getFeatures() );
        ummCategory.setIsDeleted( ummCategoryDTO.getIsDeleted() );
        ummCategory.setVersion( ummCategoryDTO.getVersion() );
        ummCategory.setAppId( ummCategoryDTO.getAppId() );
        ummCategory.setTenantCode( ummCategoryDTO.getTenantCode() );

        return ummCategory;
    }

    @Override
    public UmmCategoryDTO convert(UmmCategory ummCategory) {
        if ( ummCategory == null ) {
            return null;
        }

        UmmCategoryDTO ummCategoryDTO = new UmmCategoryDTO();

        ummCategoryDTO.setId( ummCategory.getId() );
        ummCategoryDTO.setGuid( ummCategory.getGuid() );
        ummCategoryDTO.setCategoryName( ummCategory.getCategoryName() );
        ummCategoryDTO.setCategoryCode( ummCategory.getCategoryCode() );
        ummCategoryDTO.setParentId( ummCategory.getParentId() );
        ummCategoryDTO.setStatus( ummCategory.getStatus() );
        ummCategoryDTO.setCreateUserGuid( ummCategory.getCreateUserGuid() );
        ummCategoryDTO.setCreateUserName( ummCategory.getCreateUserName() );
        ummCategoryDTO.setUpdateUserGuid( ummCategory.getUpdateUserGuid() );
        ummCategoryDTO.setUpdateUserName( ummCategory.getUpdateUserName() );
        ummCategoryDTO.setGmtCreate( ummCategory.getGmtCreate() );
        ummCategoryDTO.setGmtModify( ummCategory.getGmtModify() );
        ummCategoryDTO.setFeatures( ummCategory.getFeatures() );
        ummCategoryDTO.setIsDeleted( ummCategory.getIsDeleted() );
        ummCategoryDTO.setVersion( ummCategory.getVersion() );
        ummCategoryDTO.setAppId( ummCategory.getAppId() );
        ummCategoryDTO.setTenantCode( ummCategory.getTenantCode() );

        return ummCategoryDTO;
    }

    @Override
    public UmmCategory convert(AllUmmCategoryDTO allUmmCategoryDTO) {
        if ( allUmmCategoryDTO == null ) {
            return null;
        }

        UmmCategory ummCategory = new UmmCategory();

        ummCategory.setId( allUmmCategoryDTO.getId() );
        ummCategory.setGuid( allUmmCategoryDTO.getGuid() );
        ummCategory.setCategoryName( allUmmCategoryDTO.getCategoryName() );
        ummCategory.setCategoryCode( allUmmCategoryDTO.getCategoryCode() );
        ummCategory.setParentId( allUmmCategoryDTO.getParentId() );
        ummCategory.setStatus( allUmmCategoryDTO.getStatus() );
        ummCategory.setCreateUserGuid( allUmmCategoryDTO.getCreateUserGuid() );
        ummCategory.setCreateUserName( allUmmCategoryDTO.getCreateUserName() );
        ummCategory.setUpdateUserGuid( allUmmCategoryDTO.getUpdateUserGuid() );
        ummCategory.setUpdateUserName( allUmmCategoryDTO.getUpdateUserName() );
        ummCategory.setGmtCreate( allUmmCategoryDTO.getGmtCreate() );
        ummCategory.setGmtModify( allUmmCategoryDTO.getGmtModify() );
        ummCategory.setFeatures( allUmmCategoryDTO.getFeatures() );
        ummCategory.setIsDeleted( allUmmCategoryDTO.getIsDeleted() );
        ummCategory.setVersion( allUmmCategoryDTO.getVersion() );
        ummCategory.setAppId( allUmmCategoryDTO.getAppId() );
        ummCategory.setTenantCode( allUmmCategoryDTO.getTenantCode() );

        return ummCategory;
    }
}
