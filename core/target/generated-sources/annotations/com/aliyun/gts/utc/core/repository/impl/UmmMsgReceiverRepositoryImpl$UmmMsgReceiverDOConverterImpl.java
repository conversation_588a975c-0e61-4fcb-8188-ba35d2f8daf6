package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmMsgReceiver;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgReceiverDO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgReceiverRepositoryImpl$UmmMsgReceiverDOConverterImpl implements UmmMsgReceiverRepositoryImpl.UmmMsgReceiverDOConverter {

    @Override
    public UmmMsgReceiverDO convert(UmmMsgReceiver ummMsgReceiver) {
        if ( ummMsgReceiver == null ) {
            return null;
        }

        UmmMsgReceiverDO ummMsgReceiverDO = new UmmMsgReceiverDO();

        ummMsgReceiverDO.setId( ummMsgReceiver.getId() );
        ummMsgReceiverDO.setMsgId( ummMsgReceiver.getMsgId() );
        ummMsgReceiverDO.setReceiverType( ummMsgReceiver.getReceiverType() );
        ummMsgReceiverDO.setReceiverGuid( ummMsgReceiver.getReceiverGuid() );
        ummMsgReceiverDO.setStartDate( ummMsgReceiver.getStartDate() );
        ummMsgReceiverDO.setEndDate( ummMsgReceiver.getEndDate() );
        ummMsgReceiverDO.setTenantCode( ummMsgReceiver.getTenantCode() );
        ummMsgReceiverDO.setAppId( ummMsgReceiver.getAppId() );
        ummMsgReceiverDO.setStatus( ummMsgReceiver.getStatus() );

        return ummMsgReceiverDO;
    }

    @Override
    public UmmMsgReceiver convert(UmmMsgReceiverDO ummMsgReceiverDO) {
        if ( ummMsgReceiverDO == null ) {
            return null;
        }

        UmmMsgReceiver ummMsgReceiver = new UmmMsgReceiver();

        ummMsgReceiver.setId( ummMsgReceiverDO.getId() );
        ummMsgReceiver.setMsgId( ummMsgReceiverDO.getMsgId() );
        ummMsgReceiver.setReceiverType( ummMsgReceiverDO.getReceiverType() );
        ummMsgReceiver.setReceiverGuid( ummMsgReceiverDO.getReceiverGuid() );
        ummMsgReceiver.setStartDate( ummMsgReceiverDO.getStartDate() );
        ummMsgReceiver.setEndDate( ummMsgReceiverDO.getEndDate() );
        ummMsgReceiver.setTenantCode( ummMsgReceiverDO.getTenantCode() );
        ummMsgReceiver.setAppId( ummMsgReceiverDO.getAppId() );
        ummMsgReceiver.setStatus( ummMsgReceiverDO.getStatus() );

        return ummMsgReceiver;
    }
}
