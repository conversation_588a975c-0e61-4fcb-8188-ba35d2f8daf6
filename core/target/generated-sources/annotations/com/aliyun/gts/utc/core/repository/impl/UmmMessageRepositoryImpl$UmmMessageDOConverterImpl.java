package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmMessage;
import com.aliyun.gts.utc.core.domain.UmmMessageQueryCondition;
import com.aliyun.gts.utc.dal.dataobject.UmmMessageDO;
import com.aliyun.gts.utc.dal.dto.PageMessageDTO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMessageRepositoryImpl$UmmMessageDOConverterImpl implements UmmMessageRepositoryImpl.UmmMessageDOConverter {

    @Override
    public UmmMessageDO convert(UmmMessage ummMessage) {
        if ( ummMessage == null ) {
            return null;
        }

        UmmMessageDO ummMessageDO = new UmmMessageDO();

        ummMessageDO.setId( ummMessage.getId() );
        ummMessageDO.setTplId( ummMessage.getTplId() );
        ummMessageDO.setGuid( ummMessage.getGuid() );
        ummMessageDO.setMsgTitle( ummMessage.getMsgTitle() );
        ummMessageDO.setMsgContent( ummMessage.getMsgContent() );
        ummMessageDO.setParam( ummMessage.getParam() );
        ummMessageDO.setSendMethod( ummMessage.getSendMethod() );
        ummMessageDO.setSendTime( ummMessage.getSendTime() );
        ummMessageDO.setActualSendTime( ummMessage.getActualSendTime() );
        ummMessageDO.setCategoryId( ummMessage.getCategoryId() );
        ummMessageDO.setStatus( ummMessage.getStatus() );
        ummMessageDO.setCreateUserGuid( ummMessage.getCreateUserGuid() );
        ummMessageDO.setCreateUserName( ummMessage.getCreateUserName() );
        ummMessageDO.setUpdateUserGuid( ummMessage.getUpdateUserGuid() );
        ummMessageDO.setUpdateUserName( ummMessage.getUpdateUserName() );
        ummMessageDO.setGmtCreate( ummMessage.getGmtCreate() );
        ummMessageDO.setGmtModify( ummMessage.getGmtModify() );
        ummMessageDO.setFeatures( ummMessage.getFeatures() );
        ummMessageDO.setIsDeleted( ummMessage.getIsDeleted() );
        ummMessageDO.setVersion( ummMessage.getVersion() );
        ummMessageDO.setAppId( ummMessage.getAppId() );
        ummMessageDO.setTenantCode( ummMessage.getTenantCode() );
        ummMessageDO.setTotalNum( ummMessage.getTotalNum() );
        ummMessageDO.setSuccessNum( ummMessage.getSuccessNum() );

        return ummMessageDO;
    }

    @Override
    public UmmMessage convert(UmmMessageDO ummMessageDO) {
        if ( ummMessageDO == null ) {
            return null;
        }

        UmmMessage ummMessage = new UmmMessage();

        ummMessage.setId( ummMessageDO.getId() );
        ummMessage.setTplId( ummMessageDO.getTplId() );
        ummMessage.setGuid( ummMessageDO.getGuid() );
        ummMessage.setMsgTitle( ummMessageDO.getMsgTitle() );
        ummMessage.setMsgContent( ummMessageDO.getMsgContent() );
        ummMessage.setParam( ummMessageDO.getParam() );
        ummMessage.setSendMethod( ummMessageDO.getSendMethod() );
        ummMessage.setSendTime( ummMessageDO.getSendTime() );
        ummMessage.setActualSendTime( ummMessageDO.getActualSendTime() );
        ummMessage.setCategoryId( ummMessageDO.getCategoryId() );
        ummMessage.setStatus( ummMessageDO.getStatus() );
        ummMessage.setCreateUserGuid( ummMessageDO.getCreateUserGuid() );
        ummMessage.setCreateUserName( ummMessageDO.getCreateUserName() );
        ummMessage.setUpdateUserGuid( ummMessageDO.getUpdateUserGuid() );
        ummMessage.setUpdateUserName( ummMessageDO.getUpdateUserName() );
        ummMessage.setGmtCreate( ummMessageDO.getGmtCreate() );
        ummMessage.setGmtModify( ummMessageDO.getGmtModify() );
        ummMessage.setFeatures( ummMessageDO.getFeatures() );
        ummMessage.setIsDeleted( ummMessageDO.getIsDeleted() );
        ummMessage.setVersion( ummMessageDO.getVersion() );
        ummMessage.setAppId( ummMessageDO.getAppId() );
        ummMessage.setTenantCode( ummMessageDO.getTenantCode() );
        ummMessage.setTotalNum( ummMessageDO.getTotalNum() );
        ummMessage.setSuccessNum( ummMessageDO.getSuccessNum() );

        return ummMessage;
    }

    @Override
    public UmmMessageDO convert(UmmMessageQueryCondition condition) {
        if ( condition == null ) {
            return null;
        }

        UmmMessageDO ummMessageDO = new UmmMessageDO();

        ummMessageDO.setMsgTitle( condition.getMsgTitle() );
        ummMessageDO.setSendMethod( condition.getSendMethod() );
        ummMessageDO.setCategoryId( condition.getCategoryId() );
        ummMessageDO.setStatus( condition.getStatus() );
        ummMessageDO.setCreateUserName( condition.getCreateUserName() );

        return ummMessageDO;
    }

    @Override
    public PageMessageDTO convertPage(UmmMessageDO ummMessageDO) {
        if ( ummMessageDO == null ) {
            return null;
        }

        PageMessageDTO pageMessageDTO = new PageMessageDTO();

        pageMessageDTO.setId( ummMessageDO.getId() );
        pageMessageDTO.setTplId( ummMessageDO.getTplId() );
        pageMessageDTO.setGuid( ummMessageDO.getGuid() );
        pageMessageDTO.setMsgTitle( ummMessageDO.getMsgTitle() );
        pageMessageDTO.setMsgContent( ummMessageDO.getMsgContent() );
        pageMessageDTO.setParam( ummMessageDO.getParam() );
        pageMessageDTO.setSendMethod( ummMessageDO.getSendMethod() );
        pageMessageDTO.setSendTime( ummMessageDO.getSendTime() );
        pageMessageDTO.setActualSendTime( ummMessageDO.getActualSendTime() );
        pageMessageDTO.setCategoryId( ummMessageDO.getCategoryId() );
        pageMessageDTO.setStatus( ummMessageDO.getStatus() );
        pageMessageDTO.setCreateUserGuid( ummMessageDO.getCreateUserGuid() );
        pageMessageDTO.setCreateUserName( ummMessageDO.getCreateUserName() );
        pageMessageDTO.setUpdateUserGuid( ummMessageDO.getUpdateUserGuid() );
        pageMessageDTO.setUpdateUserName( ummMessageDO.getUpdateUserName() );
        pageMessageDTO.setGmtCreate( ummMessageDO.getGmtCreate() );
        pageMessageDTO.setGmtModify( ummMessageDO.getGmtModify() );
        pageMessageDTO.setFeatures( ummMessageDO.getFeatures() );
        pageMessageDTO.setIsDeleted( ummMessageDO.getIsDeleted() );
        pageMessageDTO.setVersion( ummMessageDO.getVersion() );
        pageMessageDTO.setAppId( ummMessageDO.getAppId() );
        pageMessageDTO.setTenantCode( ummMessageDO.getTenantCode() );

        return pageMessageDTO;
    }
}
