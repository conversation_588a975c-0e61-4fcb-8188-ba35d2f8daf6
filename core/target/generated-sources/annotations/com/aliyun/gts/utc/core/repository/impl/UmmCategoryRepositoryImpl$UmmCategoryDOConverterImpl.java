package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmCategory;
import com.aliyun.gts.utc.dal.dataobject.UmmCategoryDO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmCategoryRepositoryImpl$UmmCategoryDOConverterImpl implements UmmCategoryRepositoryImpl.UmmCategoryDOConverter {

    @Override
    public UmmCategoryDO convert(UmmCategory ummCategory) {
        if ( ummCategory == null ) {
            return null;
        }

        UmmCategoryDO ummCategoryDO = new UmmCategoryDO();

        ummCategoryDO.setGuid( ummCategory.getGuid() );
        ummCategoryDO.setCreateUserGuid( ummCategory.getCreateUserGuid() );
        ummCategoryDO.setCreateUserName( ummCategory.getCreateUserName() );
        ummCategoryDO.setUpdateUserGuid( ummCategory.getUpdateUserGuid() );
        ummCategoryDO.setUpdateUserName( ummCategory.getUpdateUserName() );
        ummCategoryDO.setGmtCreate( ummCategory.getGmtCreate() );
        ummCategoryDO.setGmtModify( ummCategory.getGmtModify() );
        ummCategoryDO.setIsDeleted( ummCategory.getIsDeleted() );
        ummCategoryDO.setVersion( ummCategory.getVersion() );
        ummCategoryDO.setAppId( ummCategory.getAppId() );
        ummCategoryDO.setTenantCode( ummCategory.getTenantCode() );
        ummCategoryDO.setId( ummCategory.getId() );
        ummCategoryDO.setCategoryName( ummCategory.getCategoryName() );
        ummCategoryDO.setCategoryCode( ummCategory.getCategoryCode() );
        ummCategoryDO.setParentId( ummCategory.getParentId() );
        ummCategoryDO.setStatus( ummCategory.getStatus() );
        ummCategoryDO.setFeatures( ummCategory.getFeatures() );

        return ummCategoryDO;
    }

    @Override
    public UmmCategory convert(UmmCategoryDO ummCategoryDO) {
        if ( ummCategoryDO == null ) {
            return null;
        }

        UmmCategory ummCategory = new UmmCategory();

        ummCategory.setId( ummCategoryDO.getId() );
        ummCategory.setGuid( ummCategoryDO.getGuid() );
        ummCategory.setCategoryName( ummCategoryDO.getCategoryName() );
        ummCategory.setCategoryCode( ummCategoryDO.getCategoryCode() );
        ummCategory.setParentId( ummCategoryDO.getParentId() );
        ummCategory.setStatus( ummCategoryDO.getStatus() );
        ummCategory.setCreateUserGuid( ummCategoryDO.getCreateUserGuid() );
        ummCategory.setCreateUserName( ummCategoryDO.getCreateUserName() );
        ummCategory.setUpdateUserGuid( ummCategoryDO.getUpdateUserGuid() );
        ummCategory.setUpdateUserName( ummCategoryDO.getUpdateUserName() );
        ummCategory.setGmtCreate( ummCategoryDO.getGmtCreate() );
        ummCategory.setGmtModify( ummCategoryDO.getGmtModify() );
        ummCategory.setFeatures( ummCategoryDO.getFeatures() );
        ummCategory.setIsDeleted( ummCategoryDO.getIsDeleted() );
        ummCategory.setVersion( ummCategoryDO.getVersion() );
        ummCategory.setAppId( ummCategoryDO.getAppId() );
        ummCategory.setTenantCode( ummCategoryDO.getTenantCode() );

        return ummCategory;
    }
}
