package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class MessageManageServiceImpl$UmmMsgSendDTOConverterImpl implements MessageManageServiceImpl.UmmMsgSendDTOConverter {

    @Override
    public UmmMsgSend convert(UmmMsgSendDTO ummMessageSendDTO) {
        if ( ummMessageSendDTO == null ) {
            return null;
        }

        UmmMsgSend ummMsgSend = new UmmMsgSend();

        ummMsgSend.setId( ummMessageSendDTO.getId() );
        ummMsgSend.setGuid( ummMessageSendDTO.getGuid() );
        ummMsgSend.setUserGuid( ummMessageSendDTO.getUserGuid() );
        ummMsgSend.setUserCode( ummMessageSendDTO.getUserCode() );
        ummMsgSend.setUserName( ummMessageSendDTO.getUserName() );
        ummMsgSend.setUserOrg( ummMessageSendDTO.getUserOrg() );
        ummMsgSend.setUserPostion( ummMessageSendDTO.getUserPostion() );
        ummMsgSend.setEmail( ummMessageSendDTO.getEmail() );
        ummMsgSend.setPhone( ummMessageSendDTO.getPhone() );
        ummMsgSend.setMsgId( ummMessageSendDTO.getMsgId() );
        ummMsgSend.setMsgTitle( ummMessageSendDTO.getMsgTitle() );
        ummMsgSend.setMsgContent( ummMessageSendDTO.getMsgContent() );
        ummMsgSend.setChannelId( ummMessageSendDTO.getChannelId() );
        ummMsgSend.setSendTime( ummMessageSendDTO.getSendTime() );
        ummMsgSend.setStatus( ummMessageSendDTO.getStatus() );
        ummMsgSend.setReadStatus( ummMessageSendDTO.getReadStatus() );
        ummMsgSend.setReadTime( ummMessageSendDTO.getReadTime() );
        ummMsgSend.setCreateUserGuid( ummMessageSendDTO.getCreateUserGuid() );
        ummMsgSend.setCreateUserName( ummMessageSendDTO.getCreateUserName() );
        ummMsgSend.setUpdateUserGuid( ummMessageSendDTO.getUpdateUserGuid() );
        ummMsgSend.setUpdateUserName( ummMessageSendDTO.getUpdateUserName() );
        ummMsgSend.setGmtCreate( ummMessageSendDTO.getGmtCreate() );
        ummMsgSend.setGmtModify( ummMessageSendDTO.getGmtModify() );
        ummMsgSend.setFeatures( ummMessageSendDTO.getFeatures() );
        ummMsgSend.setIsDeleted( ummMessageSendDTO.getIsDeleted() );
        ummMsgSend.setVersion( ummMessageSendDTO.getVersion() );
        ummMsgSend.setAppId( ummMessageSendDTO.getAppId() );
        ummMsgSend.setTenantCode( ummMessageSendDTO.getTenantCode() );
        ummMsgSend.setRetryRule( ummMessageSendDTO.getRetryRule() );

        return ummMsgSend;
    }

    @Override
    public UmmMsgSendDTO convert(UmmMsgSend ummMessage) {
        if ( ummMessage == null ) {
            return null;
        }

        UmmMsgSendDTO ummMsgSendDTO = new UmmMsgSendDTO();

        ummMsgSendDTO.setId( ummMessage.getId() );
        ummMsgSendDTO.setGuid( ummMessage.getGuid() );
        ummMsgSendDTO.setUserGuid( ummMessage.getUserGuid() );
        ummMsgSendDTO.setUserCode( ummMessage.getUserCode() );
        ummMsgSendDTO.setUserName( ummMessage.getUserName() );
        ummMsgSendDTO.setUserOrg( ummMessage.getUserOrg() );
        ummMsgSendDTO.setUserPostion( ummMessage.getUserPostion() );
        ummMsgSendDTO.setEmail( ummMessage.getEmail() );
        ummMsgSendDTO.setPhone( ummMessage.getPhone() );
        ummMsgSendDTO.setMsgId( ummMessage.getMsgId() );
        ummMsgSendDTO.setMsgTitle( ummMessage.getMsgTitle() );
        ummMsgSendDTO.setMsgContent( ummMessage.getMsgContent() );
        ummMsgSendDTO.setChannelId( ummMessage.getChannelId() );
        ummMsgSendDTO.setSendTime( ummMessage.getSendTime() );
        ummMsgSendDTO.setStatus( ummMessage.getStatus() );
        ummMsgSendDTO.setReadStatus( ummMessage.getReadStatus() );
        ummMsgSendDTO.setReadTime( ummMessage.getReadTime() );
        ummMsgSendDTO.setCreateUserGuid( ummMessage.getCreateUserGuid() );
        ummMsgSendDTO.setCreateUserName( ummMessage.getCreateUserName() );
        ummMsgSendDTO.setUpdateUserGuid( ummMessage.getUpdateUserGuid() );
        ummMsgSendDTO.setUpdateUserName( ummMessage.getUpdateUserName() );
        ummMsgSendDTO.setGmtCreate( ummMessage.getGmtCreate() );
        ummMsgSendDTO.setGmtModify( ummMessage.getGmtModify() );
        ummMsgSendDTO.setFeatures( ummMessage.getFeatures() );
        ummMsgSendDTO.setIsDeleted( ummMessage.getIsDeleted() );
        ummMsgSendDTO.setVersion( ummMessage.getVersion() );
        ummMsgSendDTO.setAppId( ummMessage.getAppId() );
        ummMsgSendDTO.setTenantCode( ummMessage.getTenantCode() );
        ummMsgSendDTO.setRetryRule( ummMessage.getRetryRule() );

        return ummMsgSendDTO;
    }
}
