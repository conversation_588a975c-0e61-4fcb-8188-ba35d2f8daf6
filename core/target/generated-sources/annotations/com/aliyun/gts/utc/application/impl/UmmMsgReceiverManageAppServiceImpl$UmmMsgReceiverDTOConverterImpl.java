package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgReceiverDTO;
import com.aliyun.gts.utc.core.domain.UmmMsgReceiver;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgReceiverManageAppServiceImpl$UmmMsgReceiverDTOConverterImpl implements UmmMsgReceiverManageAppServiceImpl.UmmMsgReceiverDTOConverter {

    @Override
    public UmmMsgReceiver convert(UmmMsgReceiverDTO ummMsgReceiverDTO) {
        if ( ummMsgReceiverDTO == null ) {
            return null;
        }

        UmmMsgReceiver ummMsgReceiver = new UmmMsgReceiver();

        ummMsgReceiver.setId( ummMsgReceiverDTO.getId() );
        ummMsgReceiver.setMsgId( ummMsgReceiverDTO.getMsgId() );
        ummMsgReceiver.setReceiverType( ummMsgReceiverDTO.getReceiverType() );
        ummMsgReceiver.setReceiverGuid( ummMsgReceiverDTO.getReceiverGuid() );
        ummMsgReceiver.setStartDate( ummMsgReceiverDTO.getStartDate() );
        ummMsgReceiver.setEndDate( ummMsgReceiverDTO.getEndDate() );
        ummMsgReceiver.setTenantCode( ummMsgReceiverDTO.getTenantCode() );
        ummMsgReceiver.setAppId( ummMsgReceiverDTO.getAppId() );
        ummMsgReceiver.setStatus( ummMsgReceiverDTO.getStatus() );

        return ummMsgReceiver;
    }

    @Override
    public UmmMsgReceiverDTO convert(UmmMsgReceiver ummMsgReceiver) {
        if ( ummMsgReceiver == null ) {
            return null;
        }

        UmmMsgReceiverDTO ummMsgReceiverDTO = new UmmMsgReceiverDTO();

        ummMsgReceiverDTO.setId( ummMsgReceiver.getId() );
        ummMsgReceiverDTO.setMsgId( ummMsgReceiver.getMsgId() );
        ummMsgReceiverDTO.setReceiverType( ummMsgReceiver.getReceiverType() );
        ummMsgReceiverDTO.setReceiverGuid( ummMsgReceiver.getReceiverGuid() );
        ummMsgReceiverDTO.setStartDate( ummMsgReceiver.getStartDate() );
        ummMsgReceiverDTO.setEndDate( ummMsgReceiver.getEndDate() );
        ummMsgReceiverDTO.setTenantCode( ummMsgReceiver.getTenantCode() );
        ummMsgReceiverDTO.setAppId( ummMsgReceiver.getAppId() );
        ummMsgReceiverDTO.setStatus( ummMsgReceiver.getStatus() );

        return ummMsgReceiverDTO;
    }
}
