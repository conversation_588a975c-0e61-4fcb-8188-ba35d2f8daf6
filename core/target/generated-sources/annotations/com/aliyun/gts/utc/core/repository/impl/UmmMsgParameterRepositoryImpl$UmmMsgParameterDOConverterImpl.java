package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgParameterDO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgParameterRepositoryImpl$UmmMsgParameterDOConverterImpl implements UmmMsgParameterRepositoryImpl.UmmMsgParameterDOConverter {

    @Override
    public UmmMsgParameterDO convert(UmmMsgParameter ummMsgParameter) {
        if ( ummMsgParameter == null ) {
            return null;
        }

        UmmMsgParameterDO ummMsgParameterDO = new UmmMsgParameterDO();

        ummMsgParameterDO.setId( ummMsgParameter.getId() );
        ummMsgParameterDO.setGuid( ummMsgParameter.getGuid() );
        ummMsgParameterDO.setParamName( ummMsgParameter.getParamName() );
        ummMsgParameterDO.setParamExp( ummMsgParameter.getParamExp() );
        if ( ummMsgParameter.getStatus() != null ) {
            ummMsgParameterDO.setStatus( ummMsgParameter.getStatus().longValue() );
        }
        ummMsgParameterDO.setCreateUserGuid( ummMsgParameter.getCreateUserGuid() );
        ummMsgParameterDO.setCreateUserName( ummMsgParameter.getCreateUserName() );
        ummMsgParameterDO.setUpdateUserGuid( ummMsgParameter.getUpdateUserGuid() );
        ummMsgParameterDO.setUpdateUserName( ummMsgParameter.getUpdateUserName() );
        ummMsgParameterDO.setGmtCreate( ummMsgParameter.getGmtCreate() );
        ummMsgParameterDO.setGmtModify( ummMsgParameter.getGmtModify() );
        ummMsgParameterDO.setFeatures( ummMsgParameter.getFeatures() );
        ummMsgParameterDO.setIsDeleted( ummMsgParameter.getIsDeleted() );
        ummMsgParameterDO.setVersion( ummMsgParameter.getVersion() );
        ummMsgParameterDO.setAppId( ummMsgParameter.getAppId() );
        ummMsgParameterDO.setTenantCode( ummMsgParameter.getTenantCode() );

        return ummMsgParameterDO;
    }

    @Override
    public UmmMsgParameter convert(UmmMsgParameterDO ummMsgParameterDO) {
        if ( ummMsgParameterDO == null ) {
            return null;
        }

        UmmMsgParameter ummMsgParameter = new UmmMsgParameter();

        ummMsgParameter.setId( ummMsgParameterDO.getId() );
        ummMsgParameter.setGuid( ummMsgParameterDO.getGuid() );
        ummMsgParameter.setParamName( ummMsgParameterDO.getParamName() );
        ummMsgParameter.setParamExp( ummMsgParameterDO.getParamExp() );
        if ( ummMsgParameterDO.getStatus() != null ) {
            ummMsgParameter.setStatus( ummMsgParameterDO.getStatus().intValue() );
        }
        ummMsgParameter.setCreateUserGuid( ummMsgParameterDO.getCreateUserGuid() );
        ummMsgParameter.setCreateUserName( ummMsgParameterDO.getCreateUserName() );
        ummMsgParameter.setUpdateUserGuid( ummMsgParameterDO.getUpdateUserGuid() );
        ummMsgParameter.setUpdateUserName( ummMsgParameterDO.getUpdateUserName() );
        ummMsgParameter.setGmtCreate( ummMsgParameterDO.getGmtCreate() );
        ummMsgParameter.setGmtModify( ummMsgParameterDO.getGmtModify() );
        ummMsgParameter.setFeatures( ummMsgParameterDO.getFeatures() );
        ummMsgParameter.setIsDeleted( ummMsgParameterDO.getIsDeleted() );
        ummMsgParameter.setVersion( ummMsgParameterDO.getVersion() );
        ummMsgParameter.setAppId( ummMsgParameterDO.getAppId() );
        ummMsgParameter.setTenantCode( ummMsgParameterDO.getTenantCode() );

        return ummMsgParameter;
    }
}
