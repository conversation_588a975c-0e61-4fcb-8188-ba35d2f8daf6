package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgSendDTO;
import com.aliyun.gts.utc.core.domain.UmmMsgSend;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgSendManageAppServiceImpl$UmmMsgSendDTOConverterImpl implements UmmMsgSendManageAppServiceImpl.UmmMsgSendDTOConverter {

    @Override
    public UmmMsgSend convert(UmmMsgSendDTO ummMsgSendDTO) {
        if ( ummMsgSendDTO == null ) {
            return null;
        }

        UmmMsgSend ummMsgSend = new UmmMsgSend();

        ummMsgSend.setId( ummMsgSendDTO.getId() );
        ummMsgSend.setGuid( ummMsgSendDTO.getGuid() );
        ummMsgSend.setUserGuid( ummMsgSendDTO.getUserGuid() );
        ummMsgSend.setUserCode( ummMsgSendDTO.getUserCode() );
        ummMsgSend.setUserName( ummMsgSendDTO.getUserName() );
        ummMsgSend.setUserOrg( ummMsgSendDTO.getUserOrg() );
        ummMsgSend.setUserPostion( ummMsgSendDTO.getUserPostion() );
        ummMsgSend.setEmail( ummMsgSendDTO.getEmail() );
        ummMsgSend.setPhone( ummMsgSendDTO.getPhone() );
        ummMsgSend.setMsgId( ummMsgSendDTO.getMsgId() );
        ummMsgSend.setMsgTitle( ummMsgSendDTO.getMsgTitle() );
        ummMsgSend.setMsgContent( ummMsgSendDTO.getMsgContent() );
        ummMsgSend.setChannelId( ummMsgSendDTO.getChannelId() );
        ummMsgSend.setSendTime( ummMsgSendDTO.getSendTime() );
        ummMsgSend.setStatus( ummMsgSendDTO.getStatus() );
        ummMsgSend.setCreateUserGuid( ummMsgSendDTO.getCreateUserGuid() );
        ummMsgSend.setCreateUserName( ummMsgSendDTO.getCreateUserName() );
        ummMsgSend.setUpdateUserGuid( ummMsgSendDTO.getUpdateUserGuid() );
        ummMsgSend.setUpdateUserName( ummMsgSendDTO.getUpdateUserName() );
        ummMsgSend.setGmtCreate( ummMsgSendDTO.getGmtCreate() );
        ummMsgSend.setGmtModify( ummMsgSendDTO.getGmtModify() );
        ummMsgSend.setFeatures( ummMsgSendDTO.getFeatures() );
        ummMsgSend.setIsDeleted( ummMsgSendDTO.getIsDeleted() );
        ummMsgSend.setVersion( ummMsgSendDTO.getVersion() );
        ummMsgSend.setAppId( ummMsgSendDTO.getAppId() );
        ummMsgSend.setTenantCode( ummMsgSendDTO.getTenantCode() );
        ummMsgSend.setRetryRule( ummMsgSendDTO.getRetryRule() );

        return ummMsgSend;
    }

    @Override
    public UmmMsgSendDTO convert(UmmMsgSend ummMsgSend) {
        if ( ummMsgSend == null ) {
            return null;
        }

        UmmMsgSendDTO ummMsgSendDTO = new UmmMsgSendDTO();

        ummMsgSendDTO.setId( ummMsgSend.getId() );
        ummMsgSendDTO.setGuid( ummMsgSend.getGuid() );
        ummMsgSendDTO.setUserGuid( ummMsgSend.getUserGuid() );
        ummMsgSendDTO.setUserCode( ummMsgSend.getUserCode() );
        ummMsgSendDTO.setUserName( ummMsgSend.getUserName() );
        ummMsgSendDTO.setUserOrg( ummMsgSend.getUserOrg() );
        ummMsgSendDTO.setUserPostion( ummMsgSend.getUserPostion() );
        ummMsgSendDTO.setEmail( ummMsgSend.getEmail() );
        ummMsgSendDTO.setPhone( ummMsgSend.getPhone() );
        ummMsgSendDTO.setMsgId( ummMsgSend.getMsgId() );
        ummMsgSendDTO.setMsgTitle( ummMsgSend.getMsgTitle() );
        ummMsgSendDTO.setMsgContent( ummMsgSend.getMsgContent() );
        ummMsgSendDTO.setChannelId( ummMsgSend.getChannelId() );
        ummMsgSendDTO.setSendTime( ummMsgSend.getSendTime() );
        ummMsgSendDTO.setStatus( ummMsgSend.getStatus() );
        ummMsgSendDTO.setCreateUserGuid( ummMsgSend.getCreateUserGuid() );
        ummMsgSendDTO.setCreateUserName( ummMsgSend.getCreateUserName() );
        ummMsgSendDTO.setUpdateUserGuid( ummMsgSend.getUpdateUserGuid() );
        ummMsgSendDTO.setUpdateUserName( ummMsgSend.getUpdateUserName() );
        ummMsgSendDTO.setGmtCreate( ummMsgSend.getGmtCreate() );
        ummMsgSendDTO.setGmtModify( ummMsgSend.getGmtModify() );
        ummMsgSendDTO.setFeatures( ummMsgSend.getFeatures() );
        ummMsgSendDTO.setIsDeleted( ummMsgSend.getIsDeleted() );
        ummMsgSendDTO.setVersion( ummMsgSend.getVersion() );
        ummMsgSendDTO.setAppId( ummMsgSend.getAppId() );
        ummMsgSendDTO.setTenantCode( ummMsgSend.getTenantCode() );
        ummMsgSendDTO.setRetryRule( ummMsgSend.getRetryRule() );

        return ummMsgSendDTO;
    }
}
