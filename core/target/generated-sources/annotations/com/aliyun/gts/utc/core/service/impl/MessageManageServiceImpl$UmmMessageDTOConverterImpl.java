package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.dto.UmmMessageDTO;
import com.aliyun.gts.utc.core.domain.UmmMessage;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class MessageManageServiceImpl$UmmMessageDTOConverterImpl implements MessageManageServiceImpl.UmmMessageDTOConverter {

    @Override
    public UmmMessage convert(UmmMessageDTO ummMessageDTO) {
        if ( ummMessageDTO == null ) {
            return null;
        }

        UmmMessage ummMessage = new UmmMessage();

        ummMessage.setId( ummMessageDTO.getId() );
        ummMessage.setTplId( ummMessageDTO.getTplId() );
        ummMessage.setGuid( ummMessageDTO.getGuid() );
        ummMessage.setMsgTitle( ummMessageDTO.getMsgTitle() );
        ummMessage.setMsgContent( ummMessageDTO.getMsgContent() );
        ummMessage.setParam( ummMessageDTO.getParam() );
        ummMessage.setSendMethod( ummMessageDTO.getSendMethod() );
        ummMessage.setSendTime( ummMessageDTO.getSendTime() );
        ummMessage.setActualSendTime( ummMessageDTO.getActualSendTime() );
        ummMessage.setCategoryId( ummMessageDTO.getCategoryId() );
        ummMessage.setStatus( ummMessageDTO.getStatus() );
        ummMessage.setCreateUserGuid( ummMessageDTO.getCreateUserGuid() );
        ummMessage.setCreateUserName( ummMessageDTO.getCreateUserName() );
        ummMessage.setUpdateUserGuid( ummMessageDTO.getUpdateUserGuid() );
        ummMessage.setUpdateUserName( ummMessageDTO.getUpdateUserName() );
        ummMessage.setGmtCreate( ummMessageDTO.getGmtCreate() );
        ummMessage.setGmtModify( ummMessageDTO.getGmtModify() );
        ummMessage.setFeatures( ummMessageDTO.getFeatures() );
        ummMessage.setIsDeleted( ummMessageDTO.getIsDeleted() );
        ummMessage.setVersion( ummMessageDTO.getVersion() );
        ummMessage.setAppId( ummMessageDTO.getAppId() );
        ummMessage.setTenantCode( ummMessageDTO.getTenantCode() );

        return ummMessage;
    }

    @Override
    public UmmMessageDTO convert(UmmMessage ummMessage) {
        if ( ummMessage == null ) {
            return null;
        }

        UmmMessageDTO ummMessageDTO = new UmmMessageDTO();

        ummMessageDTO.setId( ummMessage.getId() );
        ummMessageDTO.setTplId( ummMessage.getTplId() );
        ummMessageDTO.setGuid( ummMessage.getGuid() );
        ummMessageDTO.setMsgTitle( ummMessage.getMsgTitle() );
        ummMessageDTO.setMsgContent( ummMessage.getMsgContent() );
        ummMessageDTO.setParam( ummMessage.getParam() );
        ummMessageDTO.setSendMethod( ummMessage.getSendMethod() );
        ummMessageDTO.setSendTime( ummMessage.getSendTime() );
        ummMessageDTO.setActualSendTime( ummMessage.getActualSendTime() );
        ummMessageDTO.setCategoryId( ummMessage.getCategoryId() );
        ummMessageDTO.setStatus( ummMessage.getStatus() );
        ummMessageDTO.setCreateUserGuid( ummMessage.getCreateUserGuid() );
        ummMessageDTO.setCreateUserName( ummMessage.getCreateUserName() );
        ummMessageDTO.setUpdateUserGuid( ummMessage.getUpdateUserGuid() );
        ummMessageDTO.setUpdateUserName( ummMessage.getUpdateUserName() );
        ummMessageDTO.setGmtCreate( ummMessage.getGmtCreate() );
        ummMessageDTO.setGmtModify( ummMessage.getGmtModify() );
        ummMessageDTO.setFeatures( ummMessage.getFeatures() );
        ummMessageDTO.setIsDeleted( ummMessage.getIsDeleted() );
        ummMessageDTO.setVersion( ummMessage.getVersion() );
        ummMessageDTO.setAppId( ummMessage.getAppId() );
        ummMessageDTO.setTenantCode( ummMessage.getTenantCode() );

        return ummMessageDTO;
    }
}
