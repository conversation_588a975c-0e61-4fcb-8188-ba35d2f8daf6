package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmMsgChannel;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgChannelDO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgChannelRepositoryImpl$UmmMsgChannelDOConverterImpl implements UmmMsgChannelRepositoryImpl.UmmMsgChannelDOConverter {

    @Override
    public UmmMsgChannelDO convert(UmmMsgChannel ummMsgChannel) {
        if ( ummMsgChannel == null ) {
            return null;
        }

        UmmMsgChannelDO ummMsgChannelDO = new UmmMsgChannelDO();

        ummMsgChannelDO.setId( ummMsgChannel.getId() );
        ummMsgChannelDO.setMsgId( ummMsgChannel.getMsgId() );
        ummMsgChannelDO.setChannelId( ummMsgChannel.getChannelId() );
        ummMsgChannelDO.setStartDate( ummMsgChannel.getStartDate() );
        ummMsgChannelDO.setEndDate( ummMsgChannel.getEndDate() );
        ummMsgChannelDO.setTenantCode( ummMsgChannel.getTenantCode() );
        ummMsgChannelDO.setAppId( ummMsgChannel.getAppId() );
        ummMsgChannelDO.setStatus( ummMsgChannel.getStatus() );

        return ummMsgChannelDO;
    }

    @Override
    public UmmMsgChannel convert(UmmMsgChannelDO ummMsgChannelDO) {
        if ( ummMsgChannelDO == null ) {
            return null;
        }

        UmmMsgChannel ummMsgChannel = new UmmMsgChannel();

        ummMsgChannel.setId( ummMsgChannelDO.getId() );
        ummMsgChannel.setMsgId( ummMsgChannelDO.getMsgId() );
        ummMsgChannel.setChannelId( ummMsgChannelDO.getChannelId() );
        ummMsgChannel.setStartDate( ummMsgChannelDO.getStartDate() );
        ummMsgChannel.setEndDate( ummMsgChannelDO.getEndDate() );
        ummMsgChannel.setTenantCode( ummMsgChannelDO.getTenantCode() );
        ummMsgChannel.setAppId( ummMsgChannelDO.getAppId() );
        ummMsgChannel.setStatus( ummMsgChannelDO.getStatus() );

        return ummMsgChannel;
    }
}
