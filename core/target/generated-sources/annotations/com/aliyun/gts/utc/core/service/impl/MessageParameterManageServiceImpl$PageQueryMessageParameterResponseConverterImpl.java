package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.facade.response.parameter.PageQueryMessageParameterResponse;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class MessageParameterManageServiceImpl$PageQueryMessageParameterResponseConverterImpl implements MessageParameterManageServiceImpl.PageQueryMessageParameterResponseConverter {

    @Override
    public UmmMsgParameter convert(PageQueryMessageParameterResponse pageQueryMessageParameterResponse) {
        if ( pageQueryMessageParameterResponse == null ) {
            return null;
        }

        UmmMsgParameter ummMsgParameter = new UmmMsgParameter();

        ummMsgParameter.setId( pageQueryMessageParameterResponse.getId() );

        return ummMsgParameter;
    }

    @Override
    public PageQueryMessageParameterResponse convert(UmmMsgParameter ummMsgParameter) {
        if ( ummMsgParameter == null ) {
            return null;
        }

        PageQueryMessageParameterResponse pageQueryMessageParameterResponse = new PageQueryMessageParameterResponse();

        pageQueryMessageParameterResponse.setId( ummMsgParameter.getId() );

        return pageQueryMessageParameterResponse;
    }
}
