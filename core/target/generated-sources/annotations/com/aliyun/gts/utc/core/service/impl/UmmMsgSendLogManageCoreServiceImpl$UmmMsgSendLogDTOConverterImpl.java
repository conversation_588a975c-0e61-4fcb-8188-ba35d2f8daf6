package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgSendLogDTO;
import com.aliyun.gts.utc.core.domain.UmmMsgSendLog;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgSendLogManageCoreServiceImpl$UmmMsgSendLogDTOConverterImpl implements UmmMsgSendLogManageCoreServiceImpl.UmmMsgSendLogDTOConverter {

    @Override
    public UmmMsgSendLog convert(UmmMsgSendLogDTO ummMsgSendLogDTO) {
        if ( ummMsgSendLogDTO == null ) {
            return null;
        }

        UmmMsgSendLog ummMsgSendLog = new UmmMsgSendLog();

        ummMsgSendLog.setId( ummMsgSendLogDTO.getId() );
        ummMsgSendLog.setMsgSendId( ummMsgSendLogDTO.getMsgSendId() );
        ummMsgSendLog.setMessageIn( ummMsgSendLogDTO.getMessageIn() );
        ummMsgSendLog.setMessageOut( ummMsgSendLogDTO.getMessageOut() );
        ummMsgSendLog.setSendTime( ummMsgSendLogDTO.getSendTime() );
        ummMsgSendLog.setStatus( ummMsgSendLogDTO.getStatus() );
        ummMsgSendLog.setGmtCreate( ummMsgSendLogDTO.getGmtCreate() );
        ummMsgSendLog.setGmtModify( ummMsgSendLogDTO.getGmtModify() );
        ummMsgSendLog.setTenantCode( ummMsgSendLogDTO.getTenantCode() );

        return ummMsgSendLog;
    }

    @Override
    public UmmMsgSendLogDTO convert(UmmMsgSendLog ummMsgSendLog) {
        if ( ummMsgSendLog == null ) {
            return null;
        }

        UmmMsgSendLogDTO ummMsgSendLogDTO = new UmmMsgSendLogDTO();

        ummMsgSendLogDTO.setId( ummMsgSendLog.getId() );
        ummMsgSendLogDTO.setMsgSendId( ummMsgSendLog.getMsgSendId() );
        ummMsgSendLogDTO.setMessageIn( ummMsgSendLog.getMessageIn() );
        ummMsgSendLogDTO.setMessageOut( ummMsgSendLog.getMessageOut() );
        ummMsgSendLogDTO.setSendTime( ummMsgSendLog.getSendTime() );
        ummMsgSendLogDTO.setStatus( ummMsgSendLog.getStatus() );
        ummMsgSendLogDTO.setGmtCreate( ummMsgSendLog.getGmtCreate() );
        ummMsgSendLogDTO.setGmtModify( ummMsgSendLog.getGmtModify() );
        ummMsgSendLogDTO.setTenantCode( ummMsgSendLog.getTenantCode() );

        return ummMsgSendLogDTO;
    }
}
