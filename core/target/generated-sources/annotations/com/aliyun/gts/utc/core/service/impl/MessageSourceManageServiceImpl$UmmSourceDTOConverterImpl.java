package com.aliyun.gts.utc.core.service.impl;

import com.aliyun.gts.utc.api.dto.UmmSourceDTO;
import com.aliyun.gts.utc.core.domain.UmmSource;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class MessageSourceManageServiceImpl$UmmSourceDTOConverterImpl implements MessageSourceManageServiceImpl.UmmSourceDTOConverter {

    @Override
    public UmmSource convert(UmmSourceDTO ummSourceDTO) {
        if ( ummSourceDTO == null ) {
            return null;
        }

        UmmSource ummSource = new UmmSource();

        ummSource.setId( ummSourceDTO.getId() );
        ummSource.setSourceName( ummSourceDTO.getSourceName() );
        ummSource.setSourceCode( ummSourceDTO.getSourceCode() );
        ummSource.setSourceType( ummSourceDTO.getSourceType() );
        ummSource.setSourceParam( ummSourceDTO.getSourceParam() );
        ummSource.setCreateUserGuid( ummSourceDTO.getCreateUserGuid() );
        ummSource.setCreateUserName( ummSourceDTO.getCreateUserName() );
        ummSource.setUpdateUserGuid( ummSourceDTO.getUpdateUserGuid() );
        ummSource.setUpdateUserName( ummSourceDTO.getUpdateUserName() );
        ummSource.setGmtCreate( ummSourceDTO.getGmtCreate() );
        ummSource.setGmtModify( ummSourceDTO.getGmtModify() );
        ummSource.setIsDeleted( ummSourceDTO.getIsDeleted() );
        ummSource.setVersion( ummSourceDTO.getVersion() );
        ummSource.setTenantCode( ummSourceDTO.getTenantCode() );

        return ummSource;
    }

    @Override
    public UmmSourceDTO convert(UmmSource ummSource) {
        if ( ummSource == null ) {
            return null;
        }

        UmmSourceDTO ummSourceDTO = new UmmSourceDTO();

        ummSourceDTO.setId( ummSource.getId() );
        ummSourceDTO.setSourceName( ummSource.getSourceName() );
        ummSourceDTO.setSourceCode( ummSource.getSourceCode() );
        ummSourceDTO.setSourceType( ummSource.getSourceType() );
        ummSourceDTO.setSourceParam( ummSource.getSourceParam() );
        ummSourceDTO.setCreateUserGuid( ummSource.getCreateUserGuid() );
        ummSourceDTO.setCreateUserName( ummSource.getCreateUserName() );
        ummSourceDTO.setUpdateUserGuid( ummSource.getUpdateUserGuid() );
        ummSourceDTO.setUpdateUserName( ummSource.getUpdateUserName() );
        ummSourceDTO.setGmtCreate( ummSource.getGmtCreate() );
        ummSourceDTO.setGmtModify( ummSource.getGmtModify() );
        ummSourceDTO.setIsDeleted( ummSource.getIsDeleted() );
        ummSourceDTO.setVersion( ummSource.getVersion() );
        ummSourceDTO.setTenantCode( ummSource.getTenantCode() );

        return ummSourceDTO;
    }
}
