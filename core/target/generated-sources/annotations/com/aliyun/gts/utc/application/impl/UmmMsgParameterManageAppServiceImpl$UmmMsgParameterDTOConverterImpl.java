package com.aliyun.gts.utc.application.impl;

import com.aliyun.gts.utc.api.dto.UmmMsgParameterDTO;
import com.aliyun.gts.utc.core.domain.UmmMsgParameter;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgParameterManageAppServiceImpl$UmmMsgParameterDTOConverterImpl implements UmmMsgParameterManageAppServiceImpl.UmmMsgParameterDTOConverter {

    @Override
    public UmmMsgParameter convert(UmmMsgParameterDTO ummMsgParameterDTO) {
        if ( ummMsgParameterDTO == null ) {
            return null;
        }

        UmmMsgParameter ummMsgParameter = new UmmMsgParameter();

        ummMsgParameter.setId( ummMsgParameterDTO.getId() );
        ummMsgParameter.setGuid( ummMsgParameterDTO.getGuid() );
        ummMsgParameter.setParamName( ummMsgParameterDTO.getParamName() );
        ummMsgParameter.setParamExp( ummMsgParameterDTO.getParamExp() );
        ummMsgParameter.setStatus( ummMsgParameterDTO.getStatus() );
        ummMsgParameter.setCreateUserGuid( ummMsgParameterDTO.getCreateUserGuid() );
        ummMsgParameter.setCreateUserName( ummMsgParameterDTO.getCreateUserName() );
        ummMsgParameter.setUpdateUserGuid( ummMsgParameterDTO.getUpdateUserGuid() );
        ummMsgParameter.setUpdateUserName( ummMsgParameterDTO.getUpdateUserName() );
        ummMsgParameter.setGmtCreate( ummMsgParameterDTO.getGmtCreate() );
        ummMsgParameter.setGmtModify( ummMsgParameterDTO.getGmtModify() );
        ummMsgParameter.setFeatures( ummMsgParameterDTO.getFeatures() );
        ummMsgParameter.setIsDeleted( ummMsgParameterDTO.getIsDeleted() );
        ummMsgParameter.setVersion( ummMsgParameterDTO.getVersion() );
        ummMsgParameter.setAppId( ummMsgParameterDTO.getAppId() );
        ummMsgParameter.setTenantCode( ummMsgParameterDTO.getTenantCode() );

        return ummMsgParameter;
    }

    @Override
    public UmmMsgParameterDTO convert(UmmMsgParameter ummMsgParameter) {
        if ( ummMsgParameter == null ) {
            return null;
        }

        UmmMsgParameterDTO ummMsgParameterDTO = new UmmMsgParameterDTO();

        ummMsgParameterDTO.setId( ummMsgParameter.getId() );
        ummMsgParameterDTO.setGuid( ummMsgParameter.getGuid() );
        ummMsgParameterDTO.setParamName( ummMsgParameter.getParamName() );
        ummMsgParameterDTO.setParamExp( ummMsgParameter.getParamExp() );
        ummMsgParameterDTO.setStatus( ummMsgParameter.getStatus() );
        ummMsgParameterDTO.setCreateUserGuid( ummMsgParameter.getCreateUserGuid() );
        ummMsgParameterDTO.setCreateUserName( ummMsgParameter.getCreateUserName() );
        ummMsgParameterDTO.setUpdateUserGuid( ummMsgParameter.getUpdateUserGuid() );
        ummMsgParameterDTO.setUpdateUserName( ummMsgParameter.getUpdateUserName() );
        ummMsgParameterDTO.setGmtCreate( ummMsgParameter.getGmtCreate() );
        ummMsgParameterDTO.setGmtModify( ummMsgParameter.getGmtModify() );
        ummMsgParameterDTO.setFeatures( ummMsgParameter.getFeatures() );
        ummMsgParameterDTO.setIsDeleted( ummMsgParameter.getIsDeleted() );
        ummMsgParameterDTO.setVersion( ummMsgParameter.getVersion() );
        ummMsgParameterDTO.setAppId( ummMsgParameter.getAppId() );
        ummMsgParameterDTO.setTenantCode( ummMsgParameter.getTenantCode() );

        return ummMsgParameterDTO;
    }
}
