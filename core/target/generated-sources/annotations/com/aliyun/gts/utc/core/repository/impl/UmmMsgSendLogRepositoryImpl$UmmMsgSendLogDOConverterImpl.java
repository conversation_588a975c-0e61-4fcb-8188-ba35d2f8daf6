package com.aliyun.gts.utc.core.repository.impl;

import com.aliyun.gts.utc.core.domain.UmmMsgSendLog;
import com.aliyun.gts.utc.dal.dataobject.UmmMsgSendLogDO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-21T21:16:02+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
class UmmMsgSendLogRepositoryImpl$UmmMsgSendLogDOConverterImpl implements UmmMsgSendLogRepositoryImpl.UmmMsgSendLogDOConverter {

    @Override
    public UmmMsgSendLogDO convert(UmmMsgSendLog ummMsgSendLog) {
        if ( ummMsgSendLog == null ) {
            return null;
        }

        UmmMsgSendLogDO ummMsgSendLogDO = new UmmMsgSendLogDO();

        ummMsgSendLogDO.setId( ummMsgSendLog.getId() );
        ummMsgSendLogDO.setMsgSendId( ummMsgSendLog.getMsgSendId() );
        ummMsgSendLogDO.setMessageIn( ummMsgSendLog.getMessageIn() );
        ummMsgSendLogDO.setMessageOut( ummMsgSendLog.getMessageOut() );
        ummMsgSendLogDO.setSendTime( ummMsgSendLog.getSendTime() );
        ummMsgSendLogDO.setStatus( ummMsgSendLog.getStatus() );
        ummMsgSendLogDO.setGmtCreate( ummMsgSendLog.getGmtCreate() );
        ummMsgSendLogDO.setGmtModify( ummMsgSendLog.getGmtModify() );
        ummMsgSendLogDO.setTenantCode( ummMsgSendLog.getTenantCode() );

        return ummMsgSendLogDO;
    }

    @Override
    public UmmMsgSendLog convert(UmmMsgSendLogDO ummMsgSendLogDO) {
        if ( ummMsgSendLogDO == null ) {
            return null;
        }

        UmmMsgSendLog ummMsgSendLog = new UmmMsgSendLog();

        ummMsgSendLog.setId( ummMsgSendLogDO.getId() );
        ummMsgSendLog.setMsgSendId( ummMsgSendLogDO.getMsgSendId() );
        ummMsgSendLog.setMessageIn( ummMsgSendLogDO.getMessageIn() );
        ummMsgSendLog.setMessageOut( ummMsgSendLogDO.getMessageOut() );
        ummMsgSendLog.setSendTime( ummMsgSendLogDO.getSendTime() );
        ummMsgSendLog.setStatus( ummMsgSendLogDO.getStatus() );
        ummMsgSendLog.setGmtCreate( ummMsgSendLogDO.getGmtCreate() );
        ummMsgSendLog.setGmtModify( ummMsgSendLogDO.getGmtModify() );
        ummMsgSendLog.setTenantCode( ummMsgSendLogDO.getTenantCode() );

        return ummMsgSendLog;
    }
}
