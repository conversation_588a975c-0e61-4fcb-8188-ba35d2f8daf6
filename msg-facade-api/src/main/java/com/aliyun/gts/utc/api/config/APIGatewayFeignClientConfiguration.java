package com.aliyun.gts.utc.api.config;

import com.aliyun.gts.yunqiao.apigw.auth.APIGatewayCustomHeaderInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

/**
 * @author: yaohao.yh
 * @date: 2023/9/10
 */
public class APIGatewayFeignClientConfiguration {
    @Value("${msg.apigw.appCode:}")
    private String appCode;
    @Value("${msg.apigw.accesskey:}")
    private String appKey;
    @Value("${msg.apigw.secretKey:}")
    private String appSecret;

    @Bean
    public APIGatewayCustomHeaderInterceptor apiGatewayCustomHeaderInterceptor() {
        return new APIGatewayCustomHeaderInterceptor(this.appCode, this.appKey, this.appSecret);
    }
}
