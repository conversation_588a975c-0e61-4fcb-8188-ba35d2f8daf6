/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessageDTO.java
 * source table name: umm_message
 * initial code lines: 159
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Data
public class UmmMessageDTO {

    /**
    * 主键id
    */
    @ApiModelProperty("主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
    * 模板id
    */
    @ApiModelProperty("模板id")
    private Long tplId;

    /**
    *
    */
    @ApiModelProperty("")
    private String guid;

    /**
    * 消息标题
    */
    @ApiModelProperty("消息标题")
    private String msgTitle;

    /**
    * 消息内容
    */
    @ApiModelProperty("消息内容")
    private String msgContent;

    /**
    * 发送参数,json存储
    */
    @ApiModelProperty("发送参数,json存储")
    private String param;

    /**
    * 1.实时发送|2.定时发送
    */
    @ApiModelProperty("1.实时发送|2.定时发送")
    private Integer sendMethod;

    /**
    * 分发时间，定时发送时的时间
    */
    @ApiModelProperty("分发时间，定时发送时的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
    * 实际发送时间
    */
    @ApiModelProperty("实际发送时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date actualSendTime;

    /**
    * 栏目ID
    */
    @ApiModelProperty("栏目ID")
    private Long categoryId;

    /**
    * 状态  1待审批 2待发送 3发送成功 4审批未通过 5.发送失败  6已撤销
    */
    @ApiModelProperty("状态  1待审批 2待发送 3发送成功 4审批未通过 5.发送失败  6已撤销")
    private Integer status;

    /**
    * 创建人guid
    */
    @ApiModelProperty("创建人guid")
    private String createUserGuid;

    /**
    * 创建人姓名
    */
    @ApiModelProperty("创建人姓名")
    private String createUserName;

    /**
    * 修改人guid
    */
    @ApiModelProperty("修改人guid")
    private String updateUserGuid;

    /**
    * 修改人姓名
    */
    @ApiModelProperty("修改人姓名")
    private String updateUserName;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date gmtCreate;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date gmtModify;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    @ApiModelProperty("所有外部系统冗余字段，以json形式存储")
    private String features;

    /**
    * 逻辑删除
    */
    @ApiModelProperty("逻辑删除")
    private Long isDeleted;

    /**
    * 版本号
    */
    @ApiModelProperty("版本号")
    private Integer version;

    /**
    * 应用id
    */
    @ApiModelProperty("应用id")
    private Long appId;

    /**
    * 租户编码
    */
    @ApiModelProperty("租户编码")
    private String tenantCode;
    /**
    * 租户ID
    */
    @ApiModelProperty("租户ID")
    private Long tenantId;

}
