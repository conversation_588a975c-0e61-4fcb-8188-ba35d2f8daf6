package com.aliyun.gts.utc.api.facade.response.template;


import com.aliyun.gts.utc.api.dto.SourceDTO;
import com.aliyun.gts.utc.api.dto.ParamDTO;
import com.aliyun.gts.utc.api.dto.ReceiverDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 查询消息模板详情返回体
 * <AUTHOR>
 * @date 2021/6/1 16:38
 */
@Data
public class QueryMessageTemplateResponse {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("消息标题")
    private String msgTitle;

    @ApiModelProperty("模板编码")
    private String tplCode;

    @ApiModelProperty("归属栏目id")
    private Long categoryId;

    @ApiModelProperty("栏目名称")
    private String categoryName;

    @ApiModelProperty("消息内容")
    private String msgContent;

    @ApiModelProperty("参数")
    private ParamDTO paramDTO;

    @ApiModelProperty("发送范围类型，ALL/ORG/POS")
    private String receiverType;

    @ApiModelProperty("发送范围guid列表")
    private List<String> receiverGuidList;

    @ApiModelProperty("发送人员信息列表")
    private List<ReceiverDTO> receiverList;

    @ApiModelProperty("渠道id")
    private Long channelId;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("发送类型，1-实时发送/2-定时发送")
    private Integer sendType;

    @ApiModelProperty("发送类型，实时发送、定时发送")
    private String sendTypeName;

    @ApiModelProperty("提交人")
    private String submitName;

    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    @ApiModelProperty("消息源信息")
    private SourceDTO source;
}
