/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.annotations.ApiParam;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendDTO.java
 * source table name: umm_msg_send
 * initial code lines: 201
 * initial generated date: 2021-06-03 22:51:27
 *
 * @date 2021-06-03 22:51:27
 */
@Data
public class UmmMsgSendDTO {

    /**
    * 主键id
    */
    @ApiParam("主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
    *
    */
    @ApiParam("")
    private String guid;

    /**
    * 用户guid
    */
    @ApiParam("用户guid")
    private String userGuid;

    /**
    * 用户编码，用户登录账号
    */
    @ApiParam("用户编码，用户登录账号")
    private String userCode;

    /**
    * 用户姓名
    */
    @ApiParam("用户姓名")
    private String userName;

    /**
    * 用户组织
    */
    @ApiParam("用户组织")
    private String userOrg;

    /**
    * 用户岗位
    */
    @ApiParam("用户岗位")
    private String userPostion;

    /**
    * 邮箱
    */
    @ApiParam("邮箱")
    private String email;

    /**
    * 电话
    */
    @ApiParam("电话")
    private String phone;

    /**
    * 消息id
    */
    @ApiParam("消息id")
    private String msgId;

    /**
    * 消息标题
    */
    @ApiParam("消息标题")
    private String msgTitle;

    /**
    * 消息内容
    */
    @ApiParam("消息内容")
    private String msgContent;

    /**
    * 消息渠道ID
    */
    @ApiParam("消息渠道ID")
    private Long channelId;

    /**
    * 发送时间
    */
    @ApiParam("发送时间")
    private Date sendTime;

    /**
    * 1.未发送,,2.发送中，3.发送成功，4.发送失败
    */
    @ApiParam("1.未发送,,2.发送中，3.发送成功，4.发送失败")
    private Integer status;

    /**
    * 阅读状态 0：未读 1：已读
    */
    @ApiParam("阅读状态 0：未读 1：已读")
    private Integer readStatus;

    /**
    * 阅读时间
    */
    @ApiParam("阅读时间")
    private Date readTime;

    /**
    * 创建人guid
    */
    @ApiParam("创建人guid")
    private String createUserGuid;

    /**
    * 创建人姓名
    */
    @ApiParam("创建人姓名")
    private String createUserName;

    /**
    * 修改人guid
    */
    @ApiParam("修改人guid")
    private String updateUserGuid;

    /**
    * 修改人姓名
    */
    @ApiParam("修改人姓名")
    private String updateUserName;

    /**
    * 创建时间
    */
    @ApiParam("创建时间")
    private Date gmtCreate;

    /**
    * 修改时间
    */
    @ApiParam("修改时间")
    private Date gmtModify;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    @ApiParam("所有外部系统冗余字段，以json形式存储")
    private String features;

    /**
    * 逻辑删除
    */
    @ApiParam("逻辑删除")
    private Long isDeleted;

    /**
    * 版本号
    */
    @ApiParam("版本号")
    private Integer version;

    /**
    * 应用id
    */
    @ApiParam("应用id")
    private Long appId;

    /**
    * 租户编码
    */
    @ApiParam("租户编码")
    private String tenantCode;

    /**
    * 重试规则
    */
    @ApiParam("重试规则")
    private String retryRule;

}
