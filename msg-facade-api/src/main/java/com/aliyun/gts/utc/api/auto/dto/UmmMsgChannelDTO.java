/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.annotations.ApiParam;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgChannelDTO.java
 * source table name: umm_msg_channel
 * initial code lines: 75
 * initial generated date: 2021-06-05 17:03:30
 *
 * @date 2021-06-05 17:03:30
 */
@Data
public class UmmMsgChannelDTO {

    /**
    * 主键id
    */
    @ApiParam("主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
    * msg主键id
    */
    @ApiParam("msg主键id")
    private Long msgId;

    /**
    * 消息渠道ID
    */
    @ApiParam("消息渠道ID")
    private Long channelId;

    /**
    * 生效日期
    */
    @ApiParam("生效日期")
    private Date startDate;

    /**
    * 失效日期
    */
    @ApiParam("失效日期")
    private Date endDate;

    /**
    * 租户编码
    */
    @ApiParam("租户编码")
    private String tenantCode;

    /**
    * 应用id
    */
    @ApiParam("应用id")
    private Long appId;

    /**
    * 1启用 2禁用
    */
    @ApiParam("1启用 2禁用")
    private Integer status;

}