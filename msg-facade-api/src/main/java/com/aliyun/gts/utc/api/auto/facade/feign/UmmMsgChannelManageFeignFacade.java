/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.facade.feign;

import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.CreateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.PageQueryUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgchannel.PageQueryUmmMsgChannelResponse;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgchannel.UpdateUmmMsgChannelRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgchannel.CreateUmmMsgChannelResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgchannel.QueryUmmMsgChannelResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgchannel.DeleteUmmMsgChannelResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgchannel.UpdateUmmMsgChannelResponse;
import org.springframework.web.bind.annotation.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgChannelManageFeignFacade.java
 * source table name: umm_msg_channel
 * initial code lines: 74
 * initial generated date: 2021-06-05 17:03:30
 *
 * @date 2021-06-05 17:03:30
 */
@RequestMapping("/umm-msg-channels")
public interface UmmMsgChannelManageFeignFacade {

    /**
     * 查询UmmMsgChannel
     *
     * @param id 查询UmmMsgChannel请求
     * @return UmmMsgChannel查询结果
     */
    @GetMapping("/{id}")
    QueryUmmMsgChannelResponse queryUmmMsgChannel(@PathVariable("id") Long id);

    /**
     * 分页查询UmmMsgChannel
     *
     * @param request 查询UmmMsgChannel请求
     * @return UmmMsgChannel查询结果
     */
    @PostMapping("/queries")
    PageQueryUmmMsgChannelResponse pageQueryUmmMsgChannel(@RequestBody PageQueryUmmMsgChannelRequest request);

    /**
     * 创建UmmMsgChannel
     *
     * @param request 创建UmmMsgChannel请求
     * @return 创建UmmMsgChannel响应
     */
    @PostMapping()
    CreateUmmMsgChannelResponse createUmmMsgChannel(@RequestBody CreateUmmMsgChannelRequest request);

    /**
     * 删除UmmMsgChannel
     *
     * @param id 删除UmmMsgChannel请求
     * @return UmmMsgChannel删除结果
     */
    @DeleteMapping("/{id}")
    DeleteUmmMsgChannelResponse deleteUmmMsgChannel(@PathVariable("id") Long id);

    /**
     * 更新UmmMsgChannel
     *
     * @param request 更新UmmMsgChannel请求
     * @return UmmMsgChannel更新结果
     */
    @PutMapping
    UpdateUmmMsgChannelResponse updateUmmMsgChannel(@RequestBody UpdateUmmMsgChannelRequest request);

}