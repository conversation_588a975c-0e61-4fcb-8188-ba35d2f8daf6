package com.aliyun.gts.utc.api.facade.request.parameter;

import com.aliyun.gts.utc.api.facade.request.AbstractPageQueryRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2021/6/4 10:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageQueryMessageParameterRequest extends AbstractPageQueryRequest {

    @ApiModelProperty("查询条件")
    private String query;

    @ApiModelProperty("排序字段 name | expression")
    private String orderField;

    @ApiModelProperty("是否升序")
    private Boolean isAsc;
}
