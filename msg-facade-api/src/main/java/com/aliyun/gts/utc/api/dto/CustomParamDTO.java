package com.aliyun.gts.utc.api.dto;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 自定义参数dto
 * <AUTHOR>
 * @date 2021/6/2 14:42
 */
@Data
public class CustomParamDTO {


    @Size(min=1, max=20, message ="自定义参数名称长度限制1-20")
    @Pattern(regexp = "[a-zA-Z]+")
    @ApiParam("自定义参数名称")
    private String name;

//    @Size(min=1, max=30, message ="自定义参数值长度限制1-30")
    @ApiParam("自定义参数值")
    private String value;

}
