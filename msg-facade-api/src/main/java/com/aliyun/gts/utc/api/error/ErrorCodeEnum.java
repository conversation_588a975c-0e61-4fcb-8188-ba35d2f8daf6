package com.aliyun.gts.utc.api.error;

/**
 * 错误码定义
 */
public enum ErrorCodeEnum {
    // 通用异常
    SUCCESS(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.INFO, "0000", "successful"),
    UNKNOWN(ErrorTypeEnum.SYSTEM, ErrorLevelEnum.ERROR, "0001", "unknown error"),
    THIRD_PARTY_ERROR(ErrorTypeEnum.SYSTEM, ErrorLevelEnum.ERROR, "0002", "third party error"),
    PARAM_ILLEGAL(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.WARN, "0004", "parameter illegal"),

    // 业务异常
    NO_PERMISSION(ErrorTypeEnum.BUSINESS, ErrorLevelEnum.ERROR, "1000101", "无权操作"),

    //消息推送相关异常
    RECEIVER_MISS(ErrorTypeEnum.MESSAGE_SEND,ErrorLevelEnum.WARN,"0006","未设置消息接收对象"),
    CHANNEL_MISS(ErrorTypeEnum.MESSAGE_SEND,ErrorLevelEnum.WARN,"0007","未设置消息发送渠道"),
    SEND_TIME_MISS(ErrorTypeEnum.MESSAGE_SEND,ErrorLevelEnum.WARN,"0008","未设置消息定时发送时间"),
    SEND_TIME_OUTOFDATA(ErrorTypeEnum.MESSAGE_SEND,ErrorLevelEnum.WARN,"0009","设置发送时间过期");

    // FIXME: 修改为合适的系统标识
    private static final String SYSTEM = "001";

    public enum ErrorLevelEnum {
        INFO(1),
        WARN(3),
        ERROR(5);

        private final int code;

        ErrorLevelEnum(int code) {this.code = code;}

        /**
         * Getter method for property <tt>code</tt>.
         *
         * @return property value of code
         */
        public int getCode() {
            return code;
        }
    }

    public enum ErrorTypeEnum {
        BUSINESS(1),
        SYSTEM(3),
        THIRD_PARTY(5),
        MESSAGE_SEND(6)
        ;

        private final int code;

        ErrorTypeEnum(int code) {this.code = code;}

        /**
         * Getter method for property <tt>code</tt>.
         *
         * @return property value of code
         */
        public int getCode() {
            return code;
        }
    }

    private final ErrorTypeEnum  type;
    private final ErrorLevelEnum level;
    private final String         detailCode;
    private final String         description;

    ErrorCodeEnum(ErrorTypeEnum type, ErrorLevelEnum level, String detailCode, String description) {
        this.type = type;
        this.level = level;
        this.detailCode = detailCode;
        this.description = description;
    }

    /**
     * Getter method for property <tt>type</tt>.
     *
     * @return property value of type
     */
    public ErrorTypeEnum getType() {
        return type;
    }

    /**
     * Getter method for property <tt>level</tt>.
     *
     * @return property value of level
     */
    public ErrorLevelEnum getLevel() {
        return level;
    }

    public String getSystem() {
        return SYSTEM;
    }

    /**
     * Getter method for property <tt>detailCode</tt>.
     *
     * @return property value of detailCode
     */
    public String getDetailCode() {
        return detailCode;
    }

    /**
     * Getter method for property <tt>description</tt>.
     *
     * @return property value of description
     */
    public String getDescription() {
        return description;
    }

    public String toCode() {
        return String.format("%d%d%s%s", getType().getCode(), getLevel().getCode(), getSystem(), getDetailCode());
    }

}