package com.aliyun.gts.utc.api.facade.request.source;

import com.aliyun.gts.utc.api.facade.request.AbstractPageQueryRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageQueryMessageSourceRequest extends AbstractPageQueryRequest {

    @ApiModelProperty("消息源名称关键字")
    private String sourceName;

    @ApiModelProperty("消息源类型")
    private String sourceType;

    @ApiModelProperty("提交人关键字")
    private String updateUserName;

    @Pattern(regexp = "sourceName|sourceType|updateUserName|gmtModify", message = "排序字段不正确")
    @ApiModelProperty("排序字段 sourceName|sourceType|updateUserName|gmtModify")
    private String orderField;

    @ApiModelProperty("是否升序")
    private Boolean isAsc;
}
