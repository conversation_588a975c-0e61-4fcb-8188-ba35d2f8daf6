package com.aliyun.gts.utc.api.facade.feign.message;


import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.aliyun.gts.utc.api.config.APIGatewayFeignClientConfiguration;
import com.aliyun.gts.utc.api.facade.request.CommonMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.MailMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.SmsMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.message.MessageSendRequest;
import com.aliyun.gts.yunqiao.apigw.auth.APIGatewayCustomHeaderInterceptor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * 消息分发对外服务
 **/
@FeignClient(
        value = "umm-distribute",
        url = "${msg.service.endpoint:}",
        path = "/${msg.apigw.appCode:}" + "${msg.service.apiPrefix:/umm/distribute/external}",
        configuration = APIGatewayFeignClientConfiguration.class
)
@Api(tags = "消息发送接口")
public interface MessageSendApi {

    @PostMapping("/message/send")
    @ApiOperation("通用消息发送")
    @ResponseBody
    ResultResponse<String> sendMessage(@Valid @RequestBody MessageSendRequest request);

    /**
     * 发送邮件服务
     *
     * @param request
     * @return
     */
    @PostMapping("/message/send/mail")
    @ApiOperation("邮件发送")
    @ResponseBody
    @Deprecated
    ResultResponse<String> sendEmail(@Valid @RequestBody MailMessageSendRequest request);

    /**
     * 发送消息服务（Deprecated）
     *
     * @param request
     * @return
     */
    @PostMapping("/message/send/sms")
    @ApiOperation("短信发送")
    @ResponseBody
    @Deprecated
    ResultResponse<String> sendSms(@Valid @RequestBody SmsMessageSendRequest request);

}
