/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.facade.response.ummmessage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: DeleteUmmMessageResponse.java
 * source table name: umm_message
 * initial code lines: 26
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("消息删除结果返回对象")
public class DeleteUmmMessageResponse {

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除成功")
    private Boolean isDelete;

}