/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgReceiverDTO.java
 * source table name: umm_msg_receiver
 * initial code lines: 81
 * initial generated date: 2021-06-05 14:10:07
 *
 * @date 2021-06-05 14:10:07
 */
@Data
public class UmmMsgReceiverDTO {

    /**
    * 主键id
    */
    @ApiParam("主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
    * msg主键id
    */
    @ApiParam("msg主键id")
    private Long msgId;

    /**
    * 接收主体 ALL,ORG,POS
    */
    @ApiParam("接收主体 ALL,ORG,POS,USR")
    private String receiverType;

    /**
    * ORG guid, POS guid
    */
    @ApiParam("ORG guid, POS guid,USR guid")
    private String receiverGuid;

    /**
    * 生效
    */
    @ApiParam("生效")
    private Date startDate;

    /**
    * 失效日期
    */
    @ApiParam("失效日期")
    private Date endDate;

    /**
    * 租户编码
    */
    @ApiParam("租户编码")
    private String tenantCode;

    /**
    * 应用id
    */
    @ApiParam("应用id")
    private Long appId;

    /**
    * 1启用 2禁用
    */
    @ApiParam("1启用 2禁用")
    private Integer status;

}