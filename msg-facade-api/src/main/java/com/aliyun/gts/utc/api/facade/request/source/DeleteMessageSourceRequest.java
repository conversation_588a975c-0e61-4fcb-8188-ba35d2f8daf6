package com.aliyun.gts.utc.api.facade.request.source;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
@Data
public class DeleteMessageSourceRequest {

    @NotNull(message = "主键列表不可为空")
    @ApiModelProperty("删除主键id列表")
    @Size(min = 1, message = "主键列表不可为空")
    private List<Long> idList;
}
