package com.aliyun.gts.utc.api.facade.request.ummmessage;

import com.aliyun.gts.utc.api.facade.request.AbstractPageQueryRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;

/**
 * 分页查询消息发送请求体
 *
 * <AUTHOR>
 * @date 2021/6/2 17:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageQueryUmmMessageListRequest extends AbstractPageQueryRequest {

    @ApiParam("消息标题")
    private String msgTitle;

    @ApiParam("发送人")
    private String createUserName;

    @ApiParam("栏目ID")
    private Long categoryId;

    @ApiParam("栏目ID")
    private List<Long> categoryIdList;

    @ApiParam("提交开始日期")
    private List<Long> submitDate;

    @ApiParam("提交开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitDateStart;

    @ApiParam("提交结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitDateEnd;

    @ApiParam("消息状态  1待审批 2待发送 3发送成功 4审批未通过 5.发送失败  6已撤销")
    private Integer status;

    @ApiParam("1.实时发送|2.定时发送")
    private Integer sendMethod;

    @ApiParam("排序字段")
    @Pattern(regexp = "msgTitle|createUserName|categoryId|position|sendTime|readTime|sendStatus|readStatus")
    private String orderField;

    @ApiParam("是否升序")
    private Boolean isAsc;

}
