package com.aliyun.gts.utc.api.facade.request.template;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量删除消息模板请求体
 *
 * <AUTHOR>
 * @date 2021/6/1 14:46
 */
@Data
public class DeleteMessageTemplateRequest {

    @NotNull(message = "主键列表不可为空")
    @ApiModelProperty("删除主键id列表")
    @Size(min = 1, message = "主键列表不可为空")
    private List<Long> idList;
}
