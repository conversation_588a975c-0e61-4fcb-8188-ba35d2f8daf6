package com.aliyun.gts.utc.api.facade.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * @author: yaohao.yh
 * @date: 2023/9/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonMessageSendRequest extends BaseRequest{
    @ApiModelProperty(value = "消息类型，支持类型：email/sms/internal", required = true)
    @NotBlank(message = "消息类型不能为空")
    private String messageType;

    @ApiModelProperty(value = "消息发送对象", required = true)
    @NotBlank(message = "消息发送对象不能为空")
    private String to;

    @ApiModelProperty(value = "消息主题（仅针对邮件类型）")
    private String subject;

    @ApiModelProperty(value = "消息内容")
    @NotBlank(message = "消息内部不能为空")
    private String content;
}
