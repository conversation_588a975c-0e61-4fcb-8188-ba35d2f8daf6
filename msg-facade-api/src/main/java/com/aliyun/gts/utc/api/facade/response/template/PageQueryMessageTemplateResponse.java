package com.aliyun.gts.utc.api.facade.response.template;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/1 17:05
 */
@Data
public class PageQueryMessageTemplateResponse {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("消息标题")
    private String msgTitle;

    @ApiModelProperty("归属栏目id")
    private Long categoryId;

    @ApiModelProperty("栏目名称")
    private String categoryName;

    @ApiModelProperty("渠道id")
    private Long channelId;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("发送类型，1-实时发送/2-定时发送")
    private Integer sendType;

    @ApiModelProperty("发送类型，实时发送/定时发送")
    private String sendTypeName;

    @ApiModelProperty("提交人")
    private String submitName;

    @ApiModelProperty("更改时间, yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModify;

    @ApiModelProperty("消息源id")
    private Long sourceId;
}
