package com.aliyun.gts.utc.api.facade.response.source;


import com.aliyun.gts.utc.api.facade.request.source.MailSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.SmsSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.SxxySourceRequest;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.Date;

/**
 * 查询消息源详情返回体
 * <AUTHOR>
 * @version 2023/12/5
 */
@Data
public class QueryMessageSourceResponse {
    /**
     * 主键id
     */
    @ApiParam("主键id")
    private Long id;

    /**
     * 消息源名称
     */
    @ApiParam("消息源名称")
    private String sourceName;

    /**
     * 消息源编码
     */
    @ApiParam("消息源编码")
    private String sourceCode;

    /**
     * 消息源类型
     */
    @ApiParam("消息源类型")
    private String sourceType;

    /**
     * 邮箱host
     */
    @ApiParam("邮箱host")
    private String mailHost;

    /**
     * 邮箱用户名
     */
    @ApiParam("邮箱用户名")
    private String mailUserName;

    /**
     * 邮箱端口号
     */
    @ApiParam("邮箱端口号")
    private Integer mailPort;

    /**
     * 是否使用SSL协议
     */
    @ApiParam("是否使用SSL协议")
    private Boolean mailSSL;

    /**
     * 邮箱密码
     */
    @ApiParam("邮箱密码")
    private String mailPassWord;

    /**
     * 访问令牌
     */
    @ApiParam("访问令牌")
    private String accessToken;
    /**
     * 应用ID
     */
    @ApiParam("应用ID")
    private String appId;

    /**
     * 业务ID
     */
    @ApiParam("业务ID")
    private String businessId;


    /**
     * 发送人信息
     */
    @ApiParam("发送人信息")
    private String submitter;

    /**
     * 创建人guid
     */
    @ApiParam("创建人guid")
    private String createUserGuid;

    /**
     * 创建人姓名
     */
    @ApiParam("创建人姓名")
    private String createUserName;

    /**
     * 修改人guid
     */
    @ApiParam("修改人guid")
    private String updateUserGuid;

    /**
     * 修改人姓名
     */
    @ApiParam("修改人姓名")
    private String updateUserName;

    /**
     * 创建时间
     */
    @ApiParam("创建时间")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @ApiParam("修改时间")
    private Date gmtModify;

    /**
     * 租户编码
     */
    @ApiParam("租户编码")
    private String tenantCode;

    @ApiModelProperty("邮箱参数")
    MailSourceRequest mail;

    @ApiModelProperty("三峡行云参数参数")
    SxxySourceRequest sxxy;

    @ApiModelProperty("短信参数")
    SmsSourceRequest sms;
}
