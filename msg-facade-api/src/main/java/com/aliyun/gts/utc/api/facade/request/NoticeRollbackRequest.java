package com.aliyun.gts.utc.api.facade.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class NoticeRollbackRequest {

    /**
     * 消息类型，固定填写：workNotification
     */
    @NotBlank(message = "消息类型不能为空")
    private String msgType = "workNotification";
    /**
     * 撤回的应用，填写：应用标识
     */
    @NotBlank(message = "应用标识不能为空")
    private String msgApp;
    /**
     * 租户
     */
    @NotBlank(message = "租户不能为空")
    private String tenantId;
    /**
     * 消息id
     */
    @NotBlank(message = "消息id不能为空")
    private String bizMsgId;
}
