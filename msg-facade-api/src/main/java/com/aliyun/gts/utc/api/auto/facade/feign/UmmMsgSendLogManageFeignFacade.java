/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.facade.feign;

import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsendlog.UpdateUmmMsgSendLogResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsendlog.QueryUmmMsgSendLogResponse;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsendlog.CreateUmmMsgSendLogRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsendlog.PageQueryUmmMsgSendLogRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsendlog.DeleteUmmMsgSendLogResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsendlog.CreateUmmMsgSendLogResponse;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsendlog.UpdateUmmMsgSendLogRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsendlog.PageQueryUmmMsgSendLogResponse;
import org.springframework.web.bind.annotation.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendLogManageFeignFacade.java
 * source table name: umm_msg_send_log
 * initial code lines: 74
 * initial generated date: 2021-06-03 22:51:27
 *
 * @date 2021-06-03 22:51:27
 */
@RequestMapping("/umm-msg-send-logs")
public interface UmmMsgSendLogManageFeignFacade {

    /**
     * 查询UmmMsgSendLog
     *
     * @param id 查询UmmMsgSendLog请求
     * @return UmmMsgSendLog查询结果
     */
    @GetMapping("/{id}")
    QueryUmmMsgSendLogResponse queryUmmMsgSendLog(@PathVariable("id") Long id);

    /**
     * 分页查询UmmMsgSendLog
     *
     * @param request 查询UmmMsgSendLog请求
     * @return UmmMsgSendLog查询结果
     */
    @PostMapping("/queries")
    PageQueryUmmMsgSendLogResponse pageQueryUmmMsgSendLog(@RequestBody PageQueryUmmMsgSendLogRequest request);

    /**
     * 创建UmmMsgSendLog
     *
     * @param request 创建UmmMsgSendLog请求
     * @return 创建UmmMsgSendLog响应
     */
    @PostMapping()
    CreateUmmMsgSendLogResponse createUmmMsgSendLog(@RequestBody CreateUmmMsgSendLogRequest request);

    /**
     * 删除UmmMsgSendLog
     *
     * @param id 删除UmmMsgSendLog请求
     * @return UmmMsgSendLog删除结果
     */
    @DeleteMapping("/{id}")
    DeleteUmmMsgSendLogResponse deleteUmmMsgSendLog(@PathVariable("id") Long id);

    /**
     * 更新UmmMsgSendLog
     *
     * @param request 更新UmmMsgSendLog请求
     * @return UmmMsgSendLog更新结果
     */
    @PutMapping
    UpdateUmmMsgSendLogResponse updateUmmMsgSendLog(@RequestBody UpdateUmmMsgSendLogRequest request);

}