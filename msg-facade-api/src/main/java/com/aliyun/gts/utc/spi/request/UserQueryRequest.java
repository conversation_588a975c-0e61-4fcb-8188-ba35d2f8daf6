package com.aliyun.gts.utc.spi.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

@Data
@EqualsAndHashCode
public class UserQueryRequest {

    /**
     * 页码
     */
    @Min(1)
    private long pageNum = 1;

    /**
     * 每页数据量
     */
    @Max(500)
    private long pageSize = 500;

    /**
     * 部门id列表
     */
    private List<String> orgGuidList;
    private String userName;
    private String userCode;
    private String orgCode;
    private String phone;
    private String email;
    private String keyword;
}
