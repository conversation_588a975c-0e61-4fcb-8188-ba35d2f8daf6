/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.facade.response.ummmsgchannel;

import lombok.Data;

import java.util.List;

import com.aliyun.gts.utc.api.auto.dto.UmmMsgChannelDTO;
import com.aliyun.gts.utc.api.auto.facade.response.AbstractPageQueryResponse;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: PageQueryUmmMsgChannelResponse.java
 * source table name: umm_msg_channel
 * initial code lines: 31
 * initial generated date: 2021-06-05 17:03:30
 *
 * @date 2021-06-05 17:03:30
 */
@Data
public class PageQueryUmmMsgChannelResponse extends AbstractPageQueryResponse{

    /**
     * 分页查询umm_msg_channel列表
     */
    private List<UmmMsgChannelDTO> list;

}