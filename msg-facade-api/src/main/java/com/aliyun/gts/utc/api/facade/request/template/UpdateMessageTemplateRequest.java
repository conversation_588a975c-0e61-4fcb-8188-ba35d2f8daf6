package com.aliyun.gts.utc.api.facade.request.template;

import com.aliyun.gts.utc.api.dto.SourceDTO;
import com.aliyun.gts.utc.api.dto.ParamDTO;
import com.aliyun.gts.utc.api.dto.ReceiverDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/1 14:46
 */
@Data
public class UpdateMessageTemplateRequest {

    @ApiModelProperty("主键")
    @NotNull(message = "主键不可为空")
    private Long id;

    @NotNull(message = "消息标题不可为空")
    @ApiModelProperty("消息标题")
    @Size(min = 2, max = 30, message = "消息标题长度限制2-30个字符")
    private String msgTitle;

    @NotNull(message = "模板编码不能为空")
    @ApiModelProperty("模板编码")
    private String tplCode;

    @NotNull(message = "归属栏目不可为空")
    @ApiModelProperty("归属栏目id")
    private Long categoryId;

    @NotNull(message = "消息内容不可为空")
    @ApiModelProperty("消息内容")
    @Size(min = 2, max = 400, message = "消息内容长度限制2-400个字符")
    private String msgContent;

    @ApiModelProperty("系统参数")
    @Valid
    private ParamDTO paramDTO;

    @ApiModelProperty("发送范围类型，ALL/ORG/USR")
    @Pattern(regexp = "ALL|ORG|USR", message = "发送范围类型错误")
    private String receiverType;

    @ApiModelProperty("发送范围guid列表")
    private List<String> receiverGuidList;

    @ApiModelProperty("发生人员信息列表")
    private List<ReceiverDTO> receiverList;

    @ApiModelProperty("渠道id")
    private Long channelId;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @NotNull(message = "发生类型不可为空")
    @ApiModelProperty("发生类型，1-实时发生/2-定时发送")
    @Min(value = 1)
    @Max(value = 2)
    private Integer sendType;

    @ApiModelProperty("消息源信息")
    private SourceDTO source;
}
