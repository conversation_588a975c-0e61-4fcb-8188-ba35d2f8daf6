/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.facade.feign;

import com.aliyun.gts.utc.api.auto.facade.request.ummmsgreceiver.UpdateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgreceiver.CreateUmmMsgReceiverRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgreceiver.QueryUmmMsgReceiverResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgreceiver.UpdateUmmMsgReceiverResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgreceiver.DeleteUmmMsgReceiverResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgreceiver.PageQueryUmmMsgReceiverResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgreceiver.CreateUmmMsgReceiverResponse;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgreceiver.PageQueryUmmMsgReceiverRequest;
import org.springframework.web.bind.annotation.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgReceiverManageFeignFacade.java
 * source table name: umm_msg_receiver
 * initial code lines: 74
 * initial generated date: 2021-06-05 14:10:07
 *
 * @date 2021-06-05 14:10:07
 */
@RequestMapping("/umm-msg-receivers")
public interface UmmMsgReceiverManageFeignFacade {

    /**
     * 查询UmmMsgReceiver
     *
     * @param id 查询UmmMsgReceiver请求
     * @return UmmMsgReceiver查询结果
     */
    @GetMapping("/{id}")
    QueryUmmMsgReceiverResponse queryUmmMsgReceiver(@PathVariable("id") Long id);

    /**
     * 分页查询UmmMsgReceiver
     *
     * @param request 查询UmmMsgReceiver请求
     * @return UmmMsgReceiver查询结果
     */
    @PostMapping("/queries")
    PageQueryUmmMsgReceiverResponse pageQueryUmmMsgReceiver(@RequestBody PageQueryUmmMsgReceiverRequest request);

    /**
     * 创建UmmMsgReceiver
     *
     * @param request 创建UmmMsgReceiver请求
     * @return 创建UmmMsgReceiver响应
     */
    @PostMapping()
    CreateUmmMsgReceiverResponse createUmmMsgReceiver(@RequestBody CreateUmmMsgReceiverRequest request);

    /**
     * 删除UmmMsgReceiver
     *
     * @param id 删除UmmMsgReceiver请求
     * @return UmmMsgReceiver删除结果
     */
    @DeleteMapping("/{id}")
    DeleteUmmMsgReceiverResponse deleteUmmMsgReceiver(@PathVariable("id") Long id);

    /**
     * 更新UmmMsgReceiver
     *
     * @param request 更新UmmMsgReceiver请求
     * @return UmmMsgReceiver更新结果
     */
    @PutMapping
    UpdateUmmMsgReceiverResponse updateUmmMsgReceiver(@RequestBody UpdateUmmMsgReceiverRequest request);

}