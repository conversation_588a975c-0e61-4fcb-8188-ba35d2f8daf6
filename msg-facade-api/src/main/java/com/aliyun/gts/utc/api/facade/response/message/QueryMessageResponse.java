package com.aliyun.gts.utc.api.facade.response.message;

import com.aliyun.gts.utc.api.dto.ParamDTO;
import com.aliyun.gts.utc.api.dto.ReceiverDTO;
import com.aliyun.gts.utc.api.dto.SourceDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/2 10:06
 */
@Data
public class QueryMessageResponse {

    @ApiParam("主键")
    private Long id;

    @ApiParam("模板主键")
    private Long templateId;

    @ApiParam("模板编码")
    private String tplCode;

    @ApiParam("消息标题")
    private String msgTitle;

    @ApiParam("归属栏目id")
    private Long categoryId;

    @ApiParam("栏目名称")
    private String categoryName;

    @ApiParam("消息内容")
    private String msgContent;

    @ApiParam("消息参数")
    private ParamDTO paramDTO;

    @ApiParam("发送范围类型，ALL/ORG/POS")
    private String receiverType;

    @ApiParam("发送范围guid列表")
    private List<String> receiverGuidList;

    @ApiParam("发送范围guid列表")
    private List<ReceiverDTO> receiverList;

    @ApiParam("渠道id")
    private Long channelId;

    @ApiParam("渠道名称")
    private String channelName;

    @ApiParam("发送类型，1-实时发送/2-定时发送")
    private Integer sendType;

    @ApiParam("发送类型，实时发送/定时发送")
    private String sendTypeName;

    @ApiParam("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    @ApiParam("提交人")
    private String submitName;

    @ApiParam("发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    @ApiParam("实际发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualSendTime;

    @ApiParam("发送状态， 状态  1待审批 2待发送 3发送成功 4审批未通过 5.发送失败  6已撤销")
    private Integer status;

    @ApiParam("发送总数量")
    private Integer sendCount;

    @ApiParam("发送成功数量")
    private Integer sendSuccessCount;

    @ApiParam("等待发送数量")
    private Integer  awaitCount;

    @ApiParam("消息源信息")
    private SourceDTO source;
}
