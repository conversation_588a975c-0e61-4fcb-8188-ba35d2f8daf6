/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.annotations.ApiParam;

import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessageDTO.java
 * source table name: umm_message
 * initial code lines: 159
 * initial generated date: 2021-06-05 14:10:07
 *
 * @date 2021-06-05 14:10:07
 */
@Data
public class UmmMessageDTO {

    /**
    * 主键id
    */
    @ApiParam("主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
    * 模板id
    */
    @ApiParam("模板id")
    private Long tplId;

    /**
    *
    */
    @ApiParam("")
    private String guid;

    /**
    * 消息标题
    */
    @ApiParam("消息标题")
    private String msgTitle;

    /**
    * 消息内容
    */
    @ApiParam("消息内容")
    private String msgContent;

    /**
    * 发送参数,json存储
    */
    @ApiParam("发送参数,json存储")
    private String param;

    /**
    * 1.实时发送|2.定时发送
    */
    @ApiParam("1.实时发送|2.定时发送")
    private Integer sendMethod;

    /**
    * 分发时间，定时发送时的时间
    */
    @ApiParam("分发时间，定时发送时的时间")
    private Date sendTime;

    /**
    * 实际发送时间
    */
    @ApiParam("实际发送时间")
    private Date actualSendTime;

    /**
    * 栏目ID
    */
    @ApiParam("栏目ID")
    private Long categoryId;

    /**
    * 状态（0草稿；1发送中；2部分成功；3全部成功；4发送失败；5待审批；6审批未通过；7已撤销）
    */
    @ApiParam("状态（0草稿；1发送中；2部分成功；3全部成功；4发送失败；5待审批；6审批未通过；7已撤销）")
    private Integer status;

    /**
    * 创建人guid
    */
    @ApiParam("创建人guid")
    private String createUserGuid;

    /**
    * 创建人姓名
    */
    @ApiParam("创建人姓名")
    private String createUserName;

    /**
    * 修改人guid
    */
    @ApiParam("修改人guid")
    private String updateUserGuid;

    /**
    * 修改人姓名
    */
    @ApiParam("修改人姓名")
    private String updateUserName;

    /**
    * 创建时间
    */
    @ApiParam("创建时间")
    private Date gmtCreate;

    /**
    * 修改时间
    */
    @ApiParam("修改时间")
    private Date gmtModify;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    @ApiParam("所有外部系统冗余字段，以json形式存储")
    private String features;

    /**
    * 逻辑删除
    */
    @ApiParam("逻辑删除")
    private Long isDeleted;

    /**
    * 版本号
    */
    @ApiParam("版本号")
    private Integer version;

    /**
    * 应用id
    */
    @ApiParam("应用id")
    private Long appId;

    /**
    * 租户编码
    */
    @ApiParam("租户编码")
    private String tenantCode;


    private Integer totalNum;

    private Integer successNum;
}
