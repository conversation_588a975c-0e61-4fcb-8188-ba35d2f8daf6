package com.aliyun.gts.utc.api.facade.response.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 折线图数据
 * @author: guoshengLi
 * @create: 2025-02-25 13:32
 * @Description:
 */
@Data
public class ChartResponse {
  /**
   * 日期
   */
  @ApiModelProperty("日期")
  private String date;

  /**
   * 次数
   */
  @ApiModelProperty("次数")
  private Integer number;

  /**
   * 类型 TOTAL推送总数 SMS短信推送数 MAIL邮件消息推送数 SXXY三峡行云消息推送次数
   */
  @ApiModelProperty("型 TOTAL推送总数 SMS短信推送数 MAIL邮件消息推送数 SXXY三峡行云消息推送次数")
  private String type;

}
