/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.annotations.ApiParam;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendLogDTO.java
 * source table name: umm_msg_send_log
 * initial code lines: 75
 * initial generated date: 2021-06-03 22:51:27
 *
 * @date 2021-06-03 22:51:27
 */
@Data
public class UmmMsgSendLogDTO {

    /**
    * 主键id
    */
    @ApiParam("主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotBlank
    private Long id;

    /**
    * 消息发送数据id
    */
    @ApiParam("消息发送数据id")
    private Long msgSendId;

    /**
    * 消息原文
    */
    @ApiParam("消息原文")
    private String messageIn;

    /**
    * 返回的内容
    */
    @ApiParam("返回的内容")
    private String messageOut;

    /**
    * 发送时间
    */
    @ApiParam("发送时间")
    private Date sendTime;

    /**
    * 3.发送成功，4.发送失败
    */
    @ApiParam("3.发送成功，4.发送失败")
    private Integer status;

    /**
    * 创建时间
    */
    @ApiParam("创建时间")
    private Date gmtCreate;

    /**
    * 修改时间
    */
    @ApiParam("修改时间")
    private Date gmtModify;

    @ApiParam("租户id")
    private String tenantCode;

}