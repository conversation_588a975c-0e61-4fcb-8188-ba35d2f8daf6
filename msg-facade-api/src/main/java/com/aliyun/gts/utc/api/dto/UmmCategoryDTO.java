/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.annotations.ApiParam;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmCategoryDTO.java
 * source table name: umm_category
 * initial code lines: 123
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@Data
public class UmmCategoryDTO {

    /**
    * 栏目id
    */
    @ApiParam("栏目id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
    * 
    */
    @ApiParam("")
    private String guid;

    /**
    * 栏目名称
    */
    @Valid
    @Size(min = 1, max = 20, message = "栏目名称长度限制1-20")
    @ApiParam("栏目名称")
    private String categoryName;

    /**
    * 栏目编码
    */
    @Valid
    @Size(min = 1, max = 20, message = "栏目编码长度限制1-20")
    @ApiParam("栏目编码")
    private String categoryCode;

    /**
    * 上级栏目id
    */
    @ApiParam("上级栏目id")
    private Long parentId;

    /**
    * 状态 0禁用 1启用
    */
    @ApiParam("状态 0禁用 1启用")
    private Integer status;

    /**
    * 创建人guid
    */
    @ApiParam("创建人guid")
    private String createUserGuid;

    /**
    * 创建人姓名
    */
    @ApiParam("创建人姓名")
    private String createUserName;

    /**
    * 修改人guid
    */
    @ApiParam("修改人guid")
    private String updateUserGuid;

    /**
    * 修改人姓名
    */
    @ApiParam("修改人姓名")
    private String updateUserName;

    /**
    * 创建时间
    */
    @ApiParam("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    /**
    * 修改时间
    */
    @ApiParam("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModify;

    /**
    * 所有外部系统冗余字段，以json形式存储
    */
    @ApiParam("所有外部系统冗余字段，以json形式存储")
    private String features;

    /**
    * 逻辑删除
    */
    @ApiParam("逻辑删除")
    private Long isDeleted;

    /**
    * 版本号
    */
    @ApiParam("版本号")
    private Integer version;

    /**
    * 应用id
    */
    @ApiParam("应用id")
    private Long appId;

    /**
    * 租户编码
    */
    @ApiParam("租户编码")
    private String tenantCode;

}