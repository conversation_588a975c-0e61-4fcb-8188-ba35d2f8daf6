package com.aliyun.gts.utc.api.facade.request.message;

import com.aliyun.gts.utc.api.dto.CustomParamDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class MessageSendRequest {

    @ApiModelProperty(value = "模板编码")
    private String tplCode;

    @Length(min = 2, max = 30, message = "消息标题长度限制：2-30")
    private String msgTitle;

    @ApiModelProperty(value = "消息内容")
    private String msgContent;

    @ApiModelProperty(value = "栏目编码")
    private String categoryCode = "default";

    @ApiModelProperty(value = "发送类型，1-实时发送/2-定时发送", required = true)
    private Integer sendType;

    @ApiModelProperty("定时发送-发送时间")
    private Date sendTime;

    @ApiModelProperty(value = "发送范围类型，ALL/ORG/USR", required = true)
    private String receiverType;

    @ApiModelProperty(value = "渠道编码", required = true)
    private String channelCode;

    @ApiModelProperty(value = "发送范围guid列表", required = true)
    private List<String> receiverGuidList;

    @ApiModelProperty("保存：false/发送：true ,默认true")
    private Boolean isSend = true;

    @ApiModelProperty("自定义参数")
    private List<CustomParamDTO> customParamList;

    @ApiModelProperty("扩展字段")
    private Map<String, Object> extendFields;

    @ApiModelProperty("租户ID")
    private Long tenantId;

    @ApiModelProperty("租户Code")
    private String tenantCode;

    @ApiModelProperty("发送方姓名。长度不超过25个字符。")
    private String sendName;

    @ApiModelProperty("发送方单位（部门）长度不超过100个字符。")
    private String sendDept;
}
