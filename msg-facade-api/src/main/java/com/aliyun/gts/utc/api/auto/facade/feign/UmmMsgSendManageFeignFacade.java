/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.facade.feign;

import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsend.DeleteUmmMsgSendResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsend.QueryUmmMsgSendResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsend.PageQueryUmmMsgSendResponse;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsend.PageQueryUmmMsgSendRequest;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsend.UpdateUmmMsgSendRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsend.UpdateUmmMsgSendResponse;
import com.aliyun.gts.utc.api.auto.facade.request.ummmsgsend.CreateUmmMsgSendRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmsgsend.CreateUmmMsgSendResponse;
import org.springframework.web.bind.annotation.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMsgSendManageFeignFacade.java
 * source table name: umm_msg_send
 * initial code lines: 74
 * initial generated date: 2021-06-03 22:51:27
 *
 * @date 2021-06-03 22:51:27
 */
@RequestMapping("/umm-msg-sends")
public interface UmmMsgSendManageFeignFacade {

    /**
     * 查询UmmMsgSend
     *
     * @param id 查询UmmMsgSend请求
     * @return UmmMsgSend查询结果
     */
    @GetMapping("/{id}")
    QueryUmmMsgSendResponse queryUmmMsgSend(@PathVariable("id") Long id);

    /**
     * 分页查询UmmMsgSend
     *
     * @param request 查询UmmMsgSend请求
     * @return UmmMsgSend查询结果
     */
    @PostMapping("/queries")
    PageQueryUmmMsgSendResponse pageQueryUmmMsgSend(@RequestBody PageQueryUmmMsgSendRequest request);

    /**
     * 创建UmmMsgSend
     *
     * @param request 创建UmmMsgSend请求
     * @return 创建UmmMsgSend响应
     */
    @PostMapping()
    CreateUmmMsgSendResponse createUmmMsgSend(@RequestBody CreateUmmMsgSendRequest request);

    /**
     * 删除UmmMsgSend
     *
     * @param id 删除UmmMsgSend请求
     * @return UmmMsgSend删除结果
     */
    @DeleteMapping("/{id}")
    DeleteUmmMsgSendResponse deleteUmmMsgSend(@PathVariable("id") Long id);

    /**
     * 更新UmmMsgSend
     *
     * @param request 更新UmmMsgSend请求
     * @return UmmMsgSend更新结果
     */
    @PutMapping
    UpdateUmmMsgSendResponse updateUmmMsgSend(@RequestBody UpdateUmmMsgSendRequest request);

}