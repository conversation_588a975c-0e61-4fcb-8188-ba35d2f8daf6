package com.aliyun.gts.utc.api.facade.request.message;

import com.aliyun.gts.utc.api.facade.request.AbstractPageQueryRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 分页查询消息发送明细请求体
 *
 * <AUTHOR>
 * @date 2021/6/1 17:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageQueryMessageSendResultRequest extends AbstractPageQueryRequest {

    @ApiModelProperty("姓名查询")
    private String nameQuery;

    @ApiModelProperty("消息id")
    @NotNull(message = "消息id不可为空")
    private Long msgId;

    @ApiModelProperty("邮箱查询")
    private String emailQuery;

    @ApiModelProperty("手机号查询")
    private String phoneQuery;

    @ApiModelProperty("账号查询")
    private String accountQuery;

    @ApiModelProperty("机构查询")
    private String orgQuery;

    @ApiModelProperty("发送状态")
    private Integer sendStatus;

    @ApiModelProperty("阅读状态")
    private Integer readStatus;

    @ApiModelProperty("排序字段 account|name|phone|position|sendTime|sendStatus")
    @Pattern(regexp = "account|name|phone|email|position|sendTime|sendStatus")
    private String orderField;

    @ApiModelProperty("是否升序")
    private Boolean isAsc;

}
