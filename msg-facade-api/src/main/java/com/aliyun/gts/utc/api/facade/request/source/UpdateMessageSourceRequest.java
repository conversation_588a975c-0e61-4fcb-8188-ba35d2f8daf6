package com.aliyun.gts.utc.api.facade.request.source;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
@Data
public class UpdateMessageSourceRequest {

    @ApiModelProperty("主键")
    @NotNull(message = "主键不可为空")
    private Long id;

    @NotNull(message = "消息源名称不可为空")
    @ApiModelProperty("消息源名称")
    @Size(min = 2, max = 30, message = "消息源名称长度限制2-30个字符")
    private String sourceName;

    @NotNull(message = "消息源编码不能为空")
    @ApiModelProperty("消息源编码")
    private String sourceCode;

    @NotNull(message = "消息源类型")
    @ApiModelProperty("消息源类型，目前仅MAIL、SMS、SXXY")
    @Pattern(regexp = "MAIL|SMS|SXXY", message = "消息源类型错误")
    private String sourceType;


    @ApiModelProperty("邮箱参数")
    MailSourceRequest mail;

    @ApiModelProperty("三峡行云参数参数")
    SxxySourceRequest sxxy;

    @ApiModelProperty("短信参数")
    SmsSourceRequest sms;










    @ApiModelProperty("邮箱host")
    private String mailHost;

    @ApiModelProperty("邮箱用户名")
    private String mailUserName;

    @ApiModelProperty("邮箱密码")
    private String mailPassWord;

    @ApiModelProperty("邮箱端口号")
    private Integer mailPort;

    @ApiModelProperty("是否使用SSL协议")
    private Boolean mailSSL;

    @ApiModelProperty("发送人信息")
    private String submitter;
}
