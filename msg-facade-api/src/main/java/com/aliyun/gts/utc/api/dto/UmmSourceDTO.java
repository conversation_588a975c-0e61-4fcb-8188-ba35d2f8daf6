package com.aliyun.gts.utc.api.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */

@Data
public class UmmSourceDTO {

    /**
     * 主键id
     */
    @ApiParam("主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 消息源名称
     */
    @ApiParam("消息源名称")
    private String sourceName;

    /**
     * 消息源编码
     */
    @ApiParam("消息源编码")
    private String sourceCode;

    /**
     * 消息源类型
     */
    @ApiParam("消息源类型")
    private String sourceType;

    /**
     * 消息源参数json存储
     */
    @ApiParam("消息源参数json存储")
    private String sourceParam;

    /**
     * 创建人guid
     */
    @ApiParam("创建人guid")
    private String createUserGuid;

    /**
     * 创建人姓名
     */
    @ApiParam("创建人姓名")
    private String createUserName;

    /**
     * 修改人guid
     */
    @ApiParam("修改人guid")
    private String updateUserGuid;

    /**
     * 修改人姓名
     */
    @ApiParam("修改人姓名")
    private String updateUserName;

    /**
     * 创建时间
     */
    @ApiParam("创建时间")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @ApiParam("修改时间")
    private Date gmtModify;

    /**
     * 逻辑删除
     */
    @ApiParam("逻辑删除")
    private Long isDeleted;

    /**
     * 版本号
     */
    @ApiParam("版本号")
    private Integer version;

    /**
     * 租户编码
     */
    @ApiParam("租户编码")
    private String tenantCode;

    @Data
    public static class SourceParam {
        private String mailHost;
        private String mailUserName;
        private String mailPassWord;
        private Integer mailPort;
        private Boolean mailSSL;
        private String submitter;
    }
}
