package com.aliyun.gts.utc.api.enums;

public enum UserTypeEnum {

    IN_USER(1, "内部用户"),


    OUT_USER(2, "外部用户");

    private Integer userType;

    private String userTypeDesc;

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getUserTypeDesc() {
        return userTypeDesc;
    }

    public void setUserTypeDesc(String userTypeDesc) {
        this.userTypeDesc = userTypeDesc;
    }

    UserTypeEnum(Integer userType, String userTypeDesc) {
        this.userType = userType;
        this.userTypeDesc = userTypeDesc;
    }

    public static String getUserTypeDesc(Integer userType) {
        for (UserTypeEnum ele : values()) {
            if (ele.getUserType().equals(userType)) {
                return ele.getUserTypeDesc();
            }
        }
        return "";
    }
}
