package com.aliyun.gts.utc.api.facade.request.template;

import com.aliyun.gts.utc.api.facade.request.AbstractPageQueryRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 分页查询消息模板请求体
 *
 * <AUTHOR>
 * @date 2021/6/1 14:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageQueryMessageTemplateRequest extends AbstractPageQueryRequest {

    @ApiModelProperty("消息标题关键字")
    private String titleQuery;

    @ApiModelProperty("提交人关键字")
    private String submitNameQuery;

    @ApiModelProperty("栏目id")
    private Long categoryId;

    @ApiModelProperty("栏目树的栏目id列表")
    private List<Long> categoryIdList;

    @Pattern(regexp = "msgTitle|categoryName|gmtModify", message = "排序字段不正确")
    @ApiModelProperty("排序字段 msgTitle|categoryName|gmtModify")
    private String orderField;

    @ApiModelProperty("是否升序")
    private Boolean isAsc;
}
