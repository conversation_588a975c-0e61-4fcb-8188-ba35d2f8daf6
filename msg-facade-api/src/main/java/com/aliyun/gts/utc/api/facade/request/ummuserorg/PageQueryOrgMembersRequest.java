package com.aliyun.gts.utc.api.facade.request.ummuserorg;

import com.aliyun.gts.utc.api.facade.request.AbstractPageQueryRequest;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.List;

@Data
public class PageQueryOrgMembersRequest extends AbstractPageQueryRequest {
    @ApiParam("组织guid列表")
    private List<String> orgGuidList;
    @ApiParam("组织id列表")
    private List<Long> orgIdList;
    @ApiParam("用户名称")
    private String userName;
    @ApiParam("邮箱")
    private String email;
    @ApiParam("手机号")
    private String phone;
    @ApiParam("用户编码")
    private String userCode;
    @ApiParam("组织名称")
    private String orgName;
    @ApiParam("是否升序")
    private Boolean isAsc;
    @ApiParam("排序字段 title|category|gmtModify")
    private String orderField;
}
