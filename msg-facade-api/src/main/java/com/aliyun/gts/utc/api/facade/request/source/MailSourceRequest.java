package com.aliyun.gts.utc.api.facade.request.source;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 邮件参数
 *
 * @author: guoshengLi
 * @create: 2025-02-26 12:11
 * @Description:
 */
@Data
public class MailSourceRequest {
  @ApiModelProperty("邮箱host")
  private String mailHost;

  @ApiModelProperty("邮箱用户名")
  private String mailUserName;

  @ApiModelProperty("邮箱密码")
  private String mailPassWord;

  @ApiModelProperty("邮箱端口号")
  private Integer mailPort;

  @ApiModelProperty("是否使用SSL协议")
  private Boolean mailSSL;

  @ApiModelProperty("发送人信息")
  private String submitter;
}
