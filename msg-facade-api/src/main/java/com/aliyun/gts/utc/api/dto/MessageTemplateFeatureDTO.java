package com.aliyun.gts.utc.api.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;


import java.util.List;

/**
 * 消息模板冗余字段dto
 * <AUTHOR>
 * @date 2021/6/2 14:54
 */
@Data
public class MessageTemplateFeatureDTO {

    @ApiParam("发送类型，1-实时发送/2-定时发送")
    private Integer sendType;

    @ApiParam("系统参数列表")
    private List<SystemParamDTO> systemParamList;

    @ApiParam("发送范围类型，ALL/ORG/POS")
    private String receiverType;

    @ApiParam("渠道名称")
    private String channelName;

    @ApiParam("渠道id")
    private Long channelId;

    @ApiParam("发送范围guid列表")
    private List<String> receiverGuidList;

    @ApiModelProperty("发生人员信息列表")
    private List<ReceiverDTO> receiverList;
}
