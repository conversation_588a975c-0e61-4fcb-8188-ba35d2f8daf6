package com.aliyun.gts.utc.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Data
public class UmmMessageCreateDTO {


    @ApiModelProperty(value = "模板id",required = true)
    private Long tplId;

    @Length(min = 2, max = 30, message = "消息标题长度限制：2-30")
    @ApiModelProperty(value = "消息标题",required = true)
    private String msgTitle;

    @ApiModelProperty(value = "消息内容",required = true)
    private String msgContent;

    @ApiModelProperty("模板编码")
    private String tplCode;

    @ApiModelProperty(value = "栏目ID",required = true)
    private Long categoryId;

    @ApiModelProperty(value = "发送类型，1-实时发送/2-定时发送",required = true)
    private Integer sendType;

    @ApiModelProperty("定时发送-发送时间")
    private Date sendTime;

    @ApiModelProperty("系统参数")
    @Valid
    private ParamDTO paramDTO;

    @ApiModelProperty(value = "发送范围类型，ALL/ORG/POS/USR",required = true)
    private String receiverType;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty(value = "渠道id",required = true)
    private Long channelId;

    @ApiModelProperty(value = "发送范围guid列表")
    private List<String> receiverGuidList;

    @ApiModelProperty(value = "发送范围列表")
    private List<ReceiverDTO> receiverList;

    @ApiModelProperty("保存：false/发送：true ,默认false")
    private Boolean isSend = false;

    @ApiModelProperty("消息源信息")
    private SourceDTO source;
}
