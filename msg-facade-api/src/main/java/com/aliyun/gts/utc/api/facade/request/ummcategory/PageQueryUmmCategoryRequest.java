/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.facade.request.ummcategory;

import com.aliyun.gts.utc.api.dto.UmmCategoryDTO;
import com.aliyun.gts.utc.api.facade.request.AbstractPageQueryRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: PageQueryUmmCategoryRequest.java
 * source table name: umm_category
 * initial code lines: 29
 * initial generated date: 2021-06-01 19:50:12
 *
 * @date 2021-06-01 19:50:12
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PageQueryUmmCategoryRequest extends AbstractPageQueryRequest {

    /**
     * 新建umm_category数据  必填
     */
    private UmmCategoryDTO ummCategoryDTO;

}