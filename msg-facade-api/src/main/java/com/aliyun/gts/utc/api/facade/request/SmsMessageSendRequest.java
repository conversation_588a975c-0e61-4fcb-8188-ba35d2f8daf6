package com.aliyun.gts.utc.api.facade.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.util.LinkedHashMap;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsMessageSendRequest extends BaseRequest {

    @ApiModelProperty(value = "短信模板Code", required = true)
    @NotBlank(message = "templateCode is blank")
    private String templateCode;

    @ApiModelProperty(value = "短信模板扩展参数kv")
    private Map<String, String> extParams = new LinkedHashMap<>();

    @ApiModelProperty(value = "短信接收者电话", required = true)
    @NotBlank(message = "phoneNum is blank")
    private String phoneNum;


}
