package com.aliyun.gts.utc.api.facade.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode
public class OrgTreeResponse {
    private Long id;
    private String orgCode;
    private String guid;
    private String orgName;
    private String orgType;
    @JsonProperty("pId")
    private String pId;
    private String area;
    private List<OrgTreeResponse> childList;
    private Boolean isLeaf;
}
