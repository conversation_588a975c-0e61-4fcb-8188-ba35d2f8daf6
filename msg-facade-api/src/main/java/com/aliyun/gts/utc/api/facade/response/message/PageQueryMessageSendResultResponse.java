package com.aliyun.gts.utc.api.facade.response.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/2 10:06
 */
@Data
public class PageQueryMessageSendResultResponse {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("用户guid")
    private String userGuid;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("手机")
    private String phone;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("所在组织/机构")
    private String org;

    @ApiModelProperty("岗位")
    private String position;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("发送状态")
    private Integer sendStatus;

    @ApiModelProperty("发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    @ApiModelProperty("阅读状态")
    private Integer readStatus;

    @ApiModelProperty("阅读时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date readTime;


}
