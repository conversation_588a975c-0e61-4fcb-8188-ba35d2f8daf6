package com.aliyun.gts.utc.api.facade.response.ummmessage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 统计数据
 * @author: guoshengLi
 * @create: 2025-02-26 15:04
 * @Description:
 */
@Data
public class StatisticsMessageResponse {

  /**
   * 发送时间
   */
  @ApiModelProperty("发送时间")
  private Date sendTime;

  /**
   * 消息总数
   */
  @ApiModelProperty("消息总数")
  private Integer totalNum;

  /**
   * 发送消息成功总数
   */
  @ApiModelProperty("发送消息成功总数")
  private Integer successNum;

  /**
   * 消息源类型
   */
  @ApiModelProperty("消息源类型")
  private String sourceType;

}
