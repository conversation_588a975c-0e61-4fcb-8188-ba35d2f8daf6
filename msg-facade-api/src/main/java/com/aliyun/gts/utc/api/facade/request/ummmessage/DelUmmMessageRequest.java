package com.aliyun.gts.utc.api.facade.request.ummmessage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel("删除消息对象")
public class DelUmmMessageRequest {
    /**
     * 要删除的消息id列表
     */
    @ApiModelProperty("要删除的消息id列表")
    @NotEmpty(message = "无效消息id列表")
    private List<Long> ids;
}
