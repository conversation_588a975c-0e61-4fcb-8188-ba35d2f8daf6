package com.aliyun.gts.utc.spi;

import com.aliyun.gts.utc.api.base.Page;
import com.aliyun.gts.utc.spi.dto.User;
import com.aliyun.gts.utc.spi.request.UserQueryRequest;

import java.util.List;

/**
 * userService
 */
public interface UserServiceSpi {

    /**
     * 根据uid查询user
     *
     * @param uid
     * @return
     */
    User queryUser(String uid);

    /**
     * 查询用户
     *
     * @param userQueryRequest
     * @return
     */
    List<User> queryUser(UserQueryRequest userQueryRequest);


    /**
     * 查询组织下的所有用户
     * @param orgGuidList
     * @return
     */
    List<User> listMembersByOrgCode(List<String> orgGuidList);

    /**
     * uids
     *
     * @param uids
     * @return
     */
    List<User> queryUser(List<String> uids);
}
