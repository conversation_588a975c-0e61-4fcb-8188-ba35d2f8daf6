package com.aliyun.gts.utc.api.facade.request;

import com.aliyun.gts.utc.api.dto.MailHostPropsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import java.util.LinkedHashMap;
import java.util.Map;


@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MailMessageSendRequest extends BaseRequest {
    /**
     * 标题
     */
    @ApiModelProperty(value = "邮件标题", required = true)
    @NotBlank(message = "subject is blank")
    private String subject;
    /**
     * 内容
     */
    @NotBlank(message = "content is blank")
    @ApiModelProperty(value = "邮件正文", required = true)
    private String content;
    /**
     * 邮箱
     */
    @NotBlank(message = "email is blank")
    @Email(message = "illegal email address")
    @ApiModelProperty(value = "收件人邮箱地址", required = true)
    private String email;

    @ApiModelProperty(value = "html格式的内容")
    private Boolean htmlContent = true;

    private final Map<String, Object> tplVarsMap = new LinkedHashMap<>();
    private MailHostPropsDTO mailHostProps = new MailHostPropsDTO();

    public MailMessageSendRequest setTplVar(String name, Object value) {
        tplVarsMap.put(name, value);
        return this;
    }
}
