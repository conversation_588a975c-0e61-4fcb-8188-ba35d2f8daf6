/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.aliyun.gts.utc.api.auto.facade.feign;

import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.UpdateUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.DeleteUmmMessageResponse;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.CreateUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.QueryUmmMessageResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.CreateUmmMessageResponse;
import com.aliyun.gts.utc.api.auto.facade.request.ummmessage.PageQueryUmmMessageRequest;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.PageQueryUmmMessageResponse;
import com.aliyun.gts.utc.api.auto.facade.response.ummmessage.UpdateUmmMessageResponse;
import org.springframework.web.bind.annotation.*;

/**
 * Do not delete
 * Auto-Generated by aliyun-gts-code-generation 1.1.0-SNAPSHOT
 * filename: UmmMessageManageFeignFacade.java
 * source table name: umm_message
 * initial code lines: 74
 * initial generated date: 2021-06-05 14:10:07
 *
 * @date 2021-06-05 14:10:07
 */
@RequestMapping("/umm-messages")
public interface UmmMessageManageFeignFacade {

    /**
     * 查询UmmMessage
     *
     * @param id 查询UmmMessage请求
     * @return UmmMessage查询结果
     */
    @GetMapping("/{id}")
    QueryUmmMessageResponse queryUmmMessage(@PathVariable("id") Long id);

    /**
     * 分页查询UmmMessage
     *
     * @param request 查询UmmMessage请求
     * @return UmmMessage查询结果
     */
    @PostMapping("/queries")
    PageQueryUmmMessageResponse pageQueryUmmMessage(@RequestBody PageQueryUmmMessageRequest request);

    /**
     * 创建UmmMessage
     *
     * @param request 创建UmmMessage请求
     * @return 创建UmmMessage响应
     */
    @PostMapping()
    CreateUmmMessageResponse createUmmMessage(@RequestBody CreateUmmMessageRequest request);

    /**
     * 删除UmmMessage
     *
     * @param id 删除UmmMessage请求
     * @return UmmMessage删除结果
     */
    @DeleteMapping("/{id}")
    DeleteUmmMessageResponse deleteUmmMessage(@PathVariable("id") Long id);

    /**
     * 更新UmmMessage
     *
     * @param request 更新UmmMessage请求
     * @return UmmMessage更新结果
     */
    @PutMapping
    UpdateUmmMessageResponse updateUmmMessage(@RequestBody UpdateUmmMessageRequest request);

}