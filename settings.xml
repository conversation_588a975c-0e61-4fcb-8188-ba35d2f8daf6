<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <mirrors>
        <mirror>
            <id>mirror</id>
            <name>mirror</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public</url>
            <mirrorOf>
                *,!dayu-maven-snapshots,!dayu-maven-releases,!maven-dayu-common
            </mirrorOf>
        </mirror>

    </mirrors>
    <servers>
        <server>
            <id>maven-dayu-common</id>
            <username>maven-dayu-common-reader</username>
            <password>stVr5LAX4ZlOPMjf</password>
        </server>
        <server>
            <id>dayu-maven-releases</id>
            <username>bp46588062-deploy</username>
            <password>0v6qVF557krtYaep60Msey6Nn779TYUS</password>
        </server>
        <server>
            <id>dayu-maven-snapshots</id>
            <username>bp46588062-deploy</username>
            <password>0v6qVF557krtYaep60Msey6Nn779TYUS</password>
        </server>
    </servers>


    <profiles>
        <profile>
            <id>maven-dayu-common</id>
            <repositories>
                <repository>
                    <id>maven-dayu-common</id>
                    <url>https://nexus.dayu.work/repository/maven-dayu-common</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>maven-dayu-common</id>
                    <url>https://nexus.dayu.work/repository/maven-dayu-common</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <profile>
            <id>maven-dayu-project</id>
            <repositories>
                <repository>
                    <id>dayu-maven-releases</id>
                    <url>https://nexus.dayu.work/repository/bp46588062-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>dayu-maven-snapshots</id>
                    <url>https://nexus.dayu.work/repository/bp46588062-snapshots/</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>dayu-maven-releases</id>
                    <url>https://nexus.dayu.work/repository/bp46588062-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
                <pluginRepository>
                    <id>dayu-maven-snapshots</id>
                    <url>https://nexus.dayu.work/repository/bp46588062-snapshots/</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <activeProfiles>
        <activeProfile>maven-dayu-common</activeProfile>
        <activeProfile>maven-dayu-project</activeProfile>
    </activeProfiles>

</settings>
