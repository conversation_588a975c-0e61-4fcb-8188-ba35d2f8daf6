package com.aliyun.gts.utc.func;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 功能测试配置
 */
@MapperScan(basePackages = "com.aliyun.gts.utc.dal.dao")
@SpringBootApplication(scanBasePackages = "com.aliyun.gts.utc")
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.aliyun.gts.utc.integration")
public class FuncTestConfiguration {
}