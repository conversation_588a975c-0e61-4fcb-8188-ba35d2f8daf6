spring:
  datasource:
    username: ${<PERSON>Y<PERSON>V_DB_MYSQL_USERNAME:root}
    password: ${DYGEV_DB_MYSQL_PASSWORD:rootmysql}
    url: jdbc:mysql://${DYGEV_DB_MYSQL_HOST:127.0.0.1}:${DYGEV_DB_MYSQL_PORT:3306}/${DYGEV_DB_MYSQL_DATABASE:yunqiao_msg}?useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&useSSL=false&autoReconnect=true&failOverReadOnly=false&allowPublicKeyRetrieval=true
    driver-class-name: com.mysql.cj.jdbc.Driver
  #  cloud:
  #    nacos:
  #      server-addr: mse-7bb337b0-p.nacos-ans.mse.aliyuncs.com:8848
  #      discovery:
  #        server-addr: mse-7bb337b0-p.nacos-ans.mse.aliyuncs.com:8848
  #        username: nacos
  #        password: nacos
  #        namespace: basic
  #      config:
  #        file-extension: yml
  #        server-addr: mse-7bb337b0-p.nacos-ans.mse.aliyuncs.com:8848
  #        username: nacos
  #        password: nacos
  mail:
    host: smtp.alibaba-inc.com
    username: <EMAIL>
    password: admin123!
    protocol: smtp
    default-encoding: utf-8
    port: 465
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory


logging:
  level:
    com.aliyun.gts.utc.dal.dao: debug


gts:
  cache:
    type: redis
    host: r-bp1a474e7133deb4-p.redis.rds.aliyuncs.com
    port: 6379
    auth: Lh8UD91i
    database: 0

#ThreadPool
threadpool:
  corePoolSize: 20
  maximumPoolSize: 80
  keepAliveTime: 1
  workQueueSize: 1024

dingtalk:
  codeConverId: true
  domainName: openplatform.dg-work.cn
  accessKey: XMCZDYH-0zgT1zO43z6880T873MFnu
  secretKey: 42WMRTr1s3k6UsqsU1bCm74j3gDCr6a12iTl0011
  protocal: https

msg:
  client:
    sms:
      plat: ali
      akid:
      aksk:
      sign: 阿里云短信测试
  foura:
    plat: mock
    foura-service-url: http://basic-utc-user-daily.ingress.dayu.work

msg:
  foura:
    apigw-prefix: ${MSG_FOURA_APIGW_PREFIX:/uuc}

yunqiao:
  apigw:
    enable: false #是否启动api发现注册
    admin:
      url: http://yunqiao-base-apirepo-be-daily.ingress.dayu.work #apigw-admin地址，注册发现
    client:
      pub-check:
        enable: true # 是否启动api注册检测
        abort: true # api扫描过程如果出现问题是否中断
      pub:
        #api发现模式，0为普通模式；1位严格模式(推荐)，不允许出现同名api
        # 普通模式在多语言SDK生成时候，会产生大量的辅助代码，不美观也不方便使用
        mode: 0


