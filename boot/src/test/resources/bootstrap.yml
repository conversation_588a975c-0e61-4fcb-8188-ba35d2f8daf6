spring:
  profiles:
    active: test
  application:
    name: umm-distribute
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
  session:
    store-type: redis
  redis:
    database: ${DYGEV_REDIS_DATABASE:0}
    host: ${DYGEV_REDIS_HOST:127.0.0.1}
    username: ${DYGEV_REDIS_USERNAME:}
    password: ${DYGEV_REDIS_PASSWORD:}
    port: ${DYGEV_REDIS_PORT:6379}
  flyway:
    # 是否启用flyway
    enabled: false
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: classpath:db/migration
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
  main:
    allow-bean-definition-overriding: true

server:
  port: 8080
  servlet:
    context-path: /umm/distribute
    session:
      cookie:
        path: /

mybatis-plus:
  mapper-locations:
    - classpath*:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: AUTO
    banner: false

gts:
  common:
    swagger:
      enable: false

swagger:
  enable: false
