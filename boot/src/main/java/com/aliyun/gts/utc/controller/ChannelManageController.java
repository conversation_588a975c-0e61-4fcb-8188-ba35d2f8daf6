package com.aliyun.gts.utc.controller;

import com.aliyun.gts.utc.api.facade.response.channel.QueryAllChannelResponse;
import com.aliyun.gts.utc.integration.client.MessageClientUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/4 17:37
 */
@RestController
@RequestMapping("/channel")
@Api(value = "消息渠道管理", tags = "消息管理")
public class ChannelManageController {


    @ApiOperation("查询所有消息渠道")
    @GetMapping("/all")
    public List<QueryAllChannelResponse> getAll() {
        List<QueryAllChannelResponse> res =
                MessageClientUtils.getSupportClients().stream().map(i -> {
                    QueryAllChannelResponse r = new QueryAllChannelResponse();
                    r.setChannelCode(i.getClientType());
                    r.setId(i.getClientID());
                    r.setChannelName(i.getClientName());
                    return r;
                }).collect(Collectors.toList());
        return res;
    }


}
