package com.aliyun.gts.utc.controller;

import com.aliyun.gts.utc.api.facade.request.parameter.PageQueryMessageParameterRequest;
import com.aliyun.gts.utc.api.facade.response.parameter.PageQueryMessageParameterResponse;
import com.aliyun.gts.utc.core.service.MessageParameterManageService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息参数管理控制层
 */
@RestController
@Api(value = "消息参数管理",tags = "消息管理")
@RequestMapping("/message/parameter")
public class MessageParameterManageController {

    private final MessageParameterManageService messageParameterManageService;
    public MessageParameterManageController(MessageParameterManageService messageParameterManageService){
        this.messageParameterManageService = messageParameterManageService;
    }

    @PostMapping("/page")
    @ApiOperation("分页查询消息参数")
    public Page<PageQueryMessageParameterResponse> pageQuery(@RequestBody PageQueryMessageParameterRequest request){
        return messageParameterManageService.pageQuery(request);
    }
}
