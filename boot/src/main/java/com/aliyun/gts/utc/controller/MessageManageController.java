package com.aliyun.gts.utc.controller;

import com.aliyun.gts.utc.api.facade.request.message.MessageReplayRequest;
import com.aliyun.gts.utc.api.facade.request.message.PageQueryMessageSendResultRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.CreateUmmMessageRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.DelUmmMessageRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.PageQueryUmmMessageListRequest;
import com.aliyun.gts.utc.api.facade.request.ummmessage.UpdateUmmMessageRequest;
import com.aliyun.gts.utc.api.facade.response.message.MessageReplayResponse;
import com.aliyun.gts.utc.api.facade.response.message.PageQueryMessageSendResultResponse;
import com.aliyun.gts.utc.api.facade.response.message.QueryMessageResponse;
import com.aliyun.gts.utc.api.facade.response.ummmessage.CreateUmmMessageResponse;
import com.aliyun.gts.utc.api.facade.response.ummmessage.DeleteUmmMessageResponse;
import com.aliyun.gts.utc.api.facade.response.ummmessage.UpdateUmmMessageResponse;
import com.aliyun.gts.utc.application.UmmMessageManageAppService;
import com.aliyun.gts.utc.core.service.MessageManageService;
import com.aliyun.gts.utc.dal.dto.PageMessageDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqiao.foura.service.sdk.annotations.FouraAuditOperation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api(value = "消息管理", tags = "消息管理")
@RestController
@RequestMapping("/message")
public class MessageManageController {
    private final MessageManageService messageManageService;
    private final UmmMessageManageAppService ummMessageManageAppService;

    public MessageManageController(MessageManageService messageManageService, UmmMessageManageAppService ummMessageManageAppService) {
        this.messageManageService = messageManageService;
        this.ummMessageManageAppService = ummMessageManageAppService;
    }

    @ApiOperation(value = "消息推送列表分页查询")
    @PostMapping("/listByPage")
    public Page<PageMessageDTO> listByPage(@RequestBody PageQueryUmmMessageListRequest request) {
        // 查询并转换增加消息返回
        return ummMessageManageAppService.pageQueryUmmMessage(request);

    }


    @ApiOperation(value = "消息新增发送接口")
    @PostMapping("/createMsgSend")
    @FouraAuditOperation(action = "消息新增发送", object = "#request.ummMessageDTO.msgTitle")
    public CreateUmmMessageResponse createMsgSend(@RequestBody @Valid CreateUmmMessageRequest request) {

        return ummMessageManageAppService.createUmmMessage(request);
    }

    @ApiOperation(value = "推送消息发送接口")
    @GetMapping("/msgSend")
    public Boolean msgSend(@ApiParam(name = "消息id") @RequestParam("msgId") Long msgId) {
        Boolean b = ummMessageManageAppService.msgSend(msgId);
        if(!b){
            throw new RuntimeException("消息发送失败");
        }
        return  b;
    }

    @ApiOperation(value = "消息编辑发送接口")
    @PostMapping("/editMsgSend")
    public UpdateUmmMessageResponse editMsgSend(@RequestBody @Valid UpdateUmmMessageRequest request) {

        return ummMessageManageAppService.updateUmmMessage(request);
    }


    /**
     * 分页查询发送明细
     *
     * @param resultRequest 请求体
     * @return 发送明细结果
     */
    @PostMapping("/send/result/page")
    @ApiOperation(value = "分页查询发送明细")
    public Page<PageQueryMessageSendResultResponse> sendResultPage(@Valid @RequestBody PageQueryMessageSendResultRequest resultRequest) {
        return messageManageService.sendResultPage(resultRequest);
    }

    /**
     * 查询消息详情
     *
     * @param id 消息id
     * @return 消息详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询消息详情")
    public QueryMessageResponse detail(@RequestParam("id") Long id) {
        return messageManageService.getDetail(id);
    }

    /**
     * 消息重发
     *
     * @param request 消息id列表
     * @return 消息详情
     */
    @PostMapping("/replay")
    @ApiOperation(value = "消息重发")
    @FouraAuditOperation(action = "消息重发")
    public MessageReplayResponse replay(@RequestBody MessageReplayRequest request) {
        return messageManageService.replay(request);
    }

    @ApiOperation(value = "消息批量删除接口")
    @PostMapping("delMsgByIds")
    @FouraAuditOperation(action = "消息批量删除")
    public DeleteUmmMessageResponse delMsgByIds(@RequestBody @Valid DelUmmMessageRequest delUmmMessageRequest) {
        return ummMessageManageAppService.deleteUmmMessageOnBatch(delUmmMessageRequest);
    }
}
