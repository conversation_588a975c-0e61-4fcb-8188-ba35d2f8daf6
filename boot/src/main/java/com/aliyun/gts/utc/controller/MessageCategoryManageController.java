package com.aliyun.gts.utc.controller;

import com.aliyun.gts.utc.api.facade.request.ummcategory.CreateUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.request.ummcategory.UpdateUmmCategoryRequest;
import com.aliyun.gts.utc.api.facade.response.ummcategory.AllUmmCategoryResponse;
import com.aliyun.gts.utc.api.facade.response.ummcategory.CreateUmmCategoryResponse;
import com.aliyun.gts.utc.api.facade.response.ummcategory.DeleteUmmCategoryResponse;
import com.aliyun.gts.utc.api.facade.response.ummcategory.UpdateUmmCategoryResponse;
import com.aliyun.gts.utc.application.UmmCategoryManageAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api(value = "消息类型管理", tags = "消息类型管理")
@RestController
@RequestMapping("/message/category")
public class MessageCategoryManageController {

    private final UmmCategoryManageAppService ummCategoryManageAppService;

    public MessageCategoryManageController(UmmCategoryManageAppService ummCategoryManageAppService) {
        this.ummCategoryManageAppService = ummCategoryManageAppService;
    }

    @ApiOperation(value = "消息类型树查询")
    @PostMapping("/listTree")
    public AllUmmCategoryResponse listTree() {
        AllUmmCategoryResponse allUmmCategoryResponse = ummCategoryManageAppService.listAll();
        return allUmmCategoryResponse;
    }

    @ApiOperation(value = "消息类型树添加")
    @PostMapping("/add")
    public CreateUmmCategoryResponse add(@Valid @RequestBody CreateUmmCategoryRequest request) {
        CreateUmmCategoryResponse createUmmCategoryResponse = ummCategoryManageAppService.createUmmCategory(request);
        return createUmmCategoryResponse;
    }

    @ApiOperation(value = "消息类型树修改")
    @PostMapping("/update")
    public UpdateUmmCategoryResponse update(@Valid @RequestBody UpdateUmmCategoryRequest request) {
        return ummCategoryManageAppService.updateUmmCategory(request);
    }

    @ApiOperation(value = "消息类型树删除")
    @GetMapping("/del")
    public DeleteUmmCategoryResponse del(@RequestParam("id") Long id) {
        DeleteUmmCategoryResponse deleteUmmCategoryResponse = ummCategoryManageAppService.deleteUmmCategory(id);
        return deleteUmmCategoryResponse;
    }


}
