package com.aliyun.gts.utc.controller;

import com.aliyun.gts.utc.api.facade.request.template.CreateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.DeleteMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.PageQueryMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.request.template.UpdateMessageTemplateRequest;
import com.aliyun.gts.utc.api.facade.response.template.*;
import com.aliyun.gts.utc.api.facade.response.ummtemplate.PageQueryUmmTemplateResponse;
import com.aliyun.gts.utc.application.UmmTemplateManageAppService;
import com.aliyun.gts.utc.common.enums.ResponseCodeEnum;
import com.aliyun.gts.utc.core.service.MessageTemplateManageService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqiao.foura.service.sdk.annotations.FouraAuditOperation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "消息模板管理")
@RestController
@RequestMapping("/message/template")
public class MessageTemplateManageController {

    private final MessageTemplateManageService messageTemplateManageService;

    private final UmmTemplateManageAppService ummTemplateManageAppService;


    public MessageTemplateManageController(MessageTemplateManageService messageTemplateManageService, UmmTemplateManageAppService ummTemplateManageAppService) {
        this.messageTemplateManageService = messageTemplateManageService;
        this.ummTemplateManageAppService = ummTemplateManageAppService;
    }

    /**
     * 新增消息模板
     *
     * @param request 新增消息模板请求体
     * @return 新增消息模板返回体
     */
    @PostMapping("/add")
    @ApiOperation("新增消息模板")
    @FouraAuditOperation(action = "新增消息模板", object = "#request.msgTitle")
    public CreateMessageTemplateResponse addMessageTemplate(@Valid @RequestBody CreateMessageTemplateRequest request) {
        String tplCode = request.getTplCode();
        if (messageTemplateManageService.checkTplCode(tplCode)) {
            throw new RuntimeException(ResponseCodeEnum.TPLCODESAME.getMessage());
        }
        return messageTemplateManageService.add(request);
    }

    /**
     * 编辑消息模板
     *
     * @param request 请求体
     * @return 编辑结果
     */
    @PostMapping("/modify")
    @ApiOperation("编辑消息模板")
    @FouraAuditOperation(action = "编辑消息模板", object = "#request.msgTitle")
    public UpdateMessageTemplateResponse modifyMessageTemplate(@Valid @RequestBody UpdateMessageTemplateRequest request) {
        return messageTemplateManageService.modify(request);
    }

    /**
     * 查询消息模板详情
     *
     * @param id 消息模板id
     * @return 消息模板详情
     */
    @GetMapping("/detail")
    @ApiOperation("查询消息模板详情")
    public QueryMessageTemplateResponse queryMessageTemplate(@RequestParam("id") Long id) {
        return messageTemplateManageService.detail(id);
    }

    /**
     * 批量删除消息模板
     *
     * @param request 请求体，消息模板id列表
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("批量删除消息模板")
    @FouraAuditOperation(action = "批量删除消息模板")
    public DeleteMessageTemplateResponse deleteMessageTemplate(@Valid @RequestBody DeleteMessageTemplateRequest request) {
        return messageTemplateManageService.delete(request);
    }

    /**
     * 分页查询消息模板
     *
     * @param request 请求体
     * @return 分页后的消息模板列表
     */
    @PostMapping("/page")
    @ApiOperation("分页查询消息模板")
    public Page<PageQueryMessageTemplateResponse> pageMessageTemplate(@Valid @RequestBody PageQueryMessageTemplateRequest request) {
        return messageTemplateManageService.pageQuery(request);
    }

    @GetMapping("/warning")
    @ApiOperation("/获取告警类型模板")
    public List<GetWarningListResponse> getWarningList(@RequestParam("id") Long id) {
        return messageTemplateManageService.getWarningList(id);
    }


    @ApiOperation(value = "全部消息模板查询")
    @GetMapping("/listAll")
    public PageQueryUmmTemplateResponse listAll() {
        return ummTemplateManageAppService.queryAllUmmTemplate();
    }


}
