package com.aliyun.gts.utc.controller;

import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.aliyun.gts.utc.api.facade.request.ummuserorg.PageQueryOrgMembersRequest;
import com.aliyun.gts.utc.api.facade.response.OrgTreeResponse;
import com.aliyun.gts.utc.api.facade.response.ummuserorg.PageQueryOrgMembersResponse;
import com.aliyun.gts.utc.core.service.UserOrgService;
import com.aliyun.gts.utc.spi.dto.User;
import com.aliyun.gts.utc.spi.request.UserQueryRequest;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Strings;
import com.yunqiao.foura.service.model.UserInfoDTO;
import com.yunqiao.foura.service.sdk.service.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Api(value = "用户组织相关信息查询", tags = "用户组织")
@RestController
@RequestMapping("/user")
public class UserOrgController {

    @Autowired
    private UserOrgService userOrgService;

    @Autowired
    private UserContext userContext;

    @GetMapping("/greet")
    @ResponseBody
    public ResultResponse<UserInfoDTO> greet() {
        UserInfoDTO currentUser = this.userContext.currentUserInfo(false);
        return ResultResponse.succResult(currentUser);
    }

    @GetMapping(value = "/listOrgTree")
    @ApiOperation(value = "获取组织树")
    @ResponseBody
    public List<OrgTreeResponse> listOrgTree(@RequestParam(value = "type",
            required = false) String type) {
        return userOrgService.listOrgTree(type);
    }


    @ApiOperation(value = "根据组织机构分页查询人员信息接口")
    @PostMapping("/listOrgMembersByPage")
    @ResponseBody
    public Page<PageQueryOrgMembersResponse> listOrgMembersByPage
            (@RequestBody PageQueryOrgMembersRequest request) {
        return userOrgService.listOrgMembersByPage(request);
    }

    @ApiOperation(value = "获取下一级组织列表，参数为null返回根节点")
    @GetMapping("/listChildOrgList")
    @ResponseBody
    public List<OrgTreeResponse> listChildOrgList(String orgGuid) {
        if(Strings.isNullOrEmpty(orgGuid)) {
            return userOrgService.listChildOrgList(null);
        }
        return userOrgService.listChildOrgList(orgGuid);
    }

    @GetMapping(value = "/getUser")
    @ApiOperation(value = "获取单个用户")
    @ResponseBody
    public User getUser(@RequestParam String userCode) {
        return userOrgService.getUser(userCode);
    }

    @PostMapping(value = "/listUserByCodes")
    @ApiOperation(value = "获取多个用户")
    @ResponseBody
    public List<User> listUserByCodes(@RequestBody List<String> userCodes) {
        return userOrgService.listUserByCodes(userCodes);
    }

    @GetMapping(value = "/queryUser")
    @ApiOperation(value = "查询用户")
    @ResponseBody
    public List<User> queryUser(@RequestParam String keyword) {
        return userOrgService.queryUser(keyword);
    }
}
