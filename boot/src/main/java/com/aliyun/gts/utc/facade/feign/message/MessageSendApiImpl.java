package com.aliyun.gts.utc.facade.feign.message;

import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.aliyun.gts.utc.api.facade.feign.message.MessageSendApi;
import com.aliyun.gts.utc.api.facade.request.CommonMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.MailMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.SmsMessageSendRequest;
import com.aliyun.gts.utc.api.facade.request.message.MessageSendRequest;
import com.aliyun.gts.utc.application.MessageSendAppService;
import com.aliyun.gts.utc.application.UmmMessageManageAppService;
import com.aliyun.gts.utc.application.impl.MailMessageSenderService;
import com.aliyun.gts.utc.core.exceptions.BizException;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 消息发送对外服务实现
 **/
@ApiOperation("消息分发-消息发送")
@RestController
@RequestMapping("/external")
public class MessageSendApiImpl implements MessageSendApi {

    @Autowired
    private MessageSendAppService messageSendAppService;
    @Autowired
    private MailMessageSenderService messageSenderService;
    @Autowired
    private UmmMessageManageAppService ummMessageManageAppService;

    @Override
    public ResultResponse<String> sendMessage(MessageSendRequest request) {
        String msgId = ummMessageManageAppService.messageSend(request);
        return ResultResponse.succResult(msgId);
    }

    @Override
    @PostMapping("/message/send/mail")
    public ResultResponse<String> sendEmail(MailMessageSendRequest request) {
        if (request.getMailHostProps().getHost() == null || request.getMailHostProps().getHost().isEmpty()) {
            messageSendAppService.sendEmail(request);
        } else {
            boolean sent = messageSenderService.sendMail(request);
            if (!sent) {
                return ResultResponse.errorResult(null);
            }
        }
        return ResultResponse.succResult(null);
    }

    @Override
    @PostMapping("/message/send/sms")
    public ResultResponse<String> sendSms(@Valid SmsMessageSendRequest request) {
        messageSendAppService.sendSms(request);
        //todo return msgId
        return ResultResponse.succResult(null);
    }
}
