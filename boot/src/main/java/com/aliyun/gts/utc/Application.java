package com.aliyun.gts.utc;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.session.data.redis.config.annotation.web.server.EnableRedisWebSession;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * springboot 启动类
 */
@EnableSwagger2
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.aliyun.gts.utc.integration")
@MapperScan(basePackages = "com.aliyun.gts.utc.dal.dao")
@SpringBootApplication(scanBasePackages = {"com.aliyun.gts.utc", "com.yunqiao.foura", "com.aliyun.gts.yunqiao.message.util"})
@EnableRedisHttpSession
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
