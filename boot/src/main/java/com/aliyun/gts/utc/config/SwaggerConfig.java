package com.aliyun.gts.utc.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * swagger 配置
 */
@Configuration
public class SwaggerConfig {

    @Value("${swagger.enable}")
    private Boolean enable;

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.aliyun.gts.utc"))
                .paths(PathSelectors.any())
                .build().enable(enable);
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("接口管理1.0.0." + (new SimpleDateFormat("yyyy-MM-dd " +
                        "HH:mm:ss:SS"))
                        .format(Calendar.getInstance().getTime()))
                .description("接口管理*******")
                .version("1.0.0-SNAPSHOT")
                .build();
    }
}

