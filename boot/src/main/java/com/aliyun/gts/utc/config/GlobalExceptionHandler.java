package com.aliyun.gts.utc.config;

import com.aliyun.gts.bpaas.framework.api.rest.dto.ResultResponse;
import com.aliyun.gts.utc.api.error.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 全局异常处理
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    public @ResponseBody ResultResponse<Void> handleException(Exception e) {
        log.error("encounter unknown exception", e);
        return ResultResponse.errorResult(ErrorCodeEnum.UNKNOWN.toCode(), e.getMessage());
    }
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public @ResponseBody ResultResponse<Void> handleValidException(MethodArgumentNotValidException methodArgumentNotValidException){
        String message = methodArgumentNotValidException.getBindingResult().getFieldErrors().stream().findFirst().get().getDefaultMessage();
        log.error("valid exception:{}",message);
        return ResultResponse.errorResult(ErrorCodeEnum.PARAM_ILLEGAL.toCode(),message);
    }
}