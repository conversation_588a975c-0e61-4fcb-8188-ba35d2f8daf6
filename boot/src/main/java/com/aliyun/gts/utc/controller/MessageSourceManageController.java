package com.aliyun.gts.utc.controller;

import com.aliyun.gts.utc.api.facade.request.source.CreateSourceSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.DeleteMessageSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.PageQueryMessageSourceRequest;
import com.aliyun.gts.utc.api.facade.request.source.UpdateMessageSourceRequest;
import com.aliyun.gts.utc.api.facade.response.source.CreateMessageSourceResponse;
import com.aliyun.gts.utc.api.facade.response.source.DeleteMessageSourceResponse;
import com.aliyun.gts.utc.api.facade.response.source.QueryMessageSourceResponse;
import com.aliyun.gts.utc.api.facade.response.source.UpdateMessageSourceResponse;
import com.aliyun.gts.utc.common.enums.ResponseCodeEnum;
import com.aliyun.gts.utc.core.service.MessageSourceManageService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqiao.foura.service.sdk.annotations.FouraAuditOperation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
@Api(tags = "消息源管理")
@RestController
@RequestMapping("/message/source")
public class MessageSourceManageController {

    private final MessageSourceManageService messageSourceManageService;


    public MessageSourceManageController(MessageSourceManageService messageSourceManageService) {
        this.messageSourceManageService = messageSourceManageService;
    }

    /**
     * 新增消息源
     *
     * @param request 新增消息源请求体
     * @return 新增消息源返回体
     */
    @PostMapping("/add")
    @ApiOperation("新增消息源")
    @FouraAuditOperation(action = "新增消息源", object = "#request.sourceName")
    public CreateMessageSourceResponse addMessageSource(@Valid @RequestBody CreateSourceSourceRequest request) {
        String sourceCode = request.getSourceCode();
        if (messageSourceManageService.checkSourceCode(sourceCode)) {
            throw new RuntimeException(ResponseCodeEnum.SOURCECODESAME.getMessage());
        }
        return messageSourceManageService.add(request);
    }

    /**
     * 编辑消息源
     *
     * @param request 请求体
     * @return 编辑结果
     */
    @PostMapping("/modify")
    @ApiOperation("编辑消息源")
    @FouraAuditOperation(action = "编辑消息源", object = "#request.sourceName")
    public UpdateMessageSourceResponse modifyMessageSource(@Valid @RequestBody UpdateMessageSourceRequest request) {
        return messageSourceManageService.modify(request);
    }

    /**
     * 查询消息源详情
     *
     * @param id 消息源id
     * @return 消息源详情
     */
    @GetMapping("/detail")
    @ApiOperation("查询消息源详情")
    public QueryMessageSourceResponse queryMessageSource(@RequestParam("id") Long id) {
        return messageSourceManageService.detail(id);
    }

    /**
     * 批量删除消息源
     *
     * @param request 请求体，消息源id列表
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("批量删除消息源")
    @FouraAuditOperation(action = "批量删除消息源")
    public DeleteMessageSourceResponse deleteMessageSource(@Valid @RequestBody DeleteMessageSourceRequest request) {
        return messageSourceManageService.delete(request);
    }

    /**
     * 分页查询消息源
     *
     * @param request 请求体
     * @return 分页后的消息源列表
     */
    @PostMapping("/page")
    @ApiOperation("分页查询消息源")
    public Page<QueryMessageSourceResponse> pageMessageSource(@Valid @RequestBody PageQueryMessageSourceRequest request) {
        return messageSourceManageService.pageQuery(request);
    }
}
