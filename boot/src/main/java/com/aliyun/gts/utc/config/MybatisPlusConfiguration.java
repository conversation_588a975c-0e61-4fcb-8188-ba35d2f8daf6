package com.aliyun.gts.utc.config;

import com.aliyun.gts.utc.core.mybatis.CustomMetaObjectHandler;
import com.aliyun.gts.utc.core.mybatis.CustomSqlInjector;
import com.aliyun.gts.utc.core.utils.RuntimeContextUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Locale;

@Configuration
public class MybatisPlusConfiguration {
    @Value("#{'${mybatis-plus.plugin.tenant.ignore-tables:}'.split(',')}")
    private List<String> ignoreTables;

    public MybatisPlusConfiguration() {
    }

    @Bean
    public CustomSqlInjector sqlInjector() {
        return new CustomSqlInjector();
    }

    @Bean
    public MetaObjectHandler customMetaObjectHandler() {
        return new CustomMetaObjectHandler();
    }
}
