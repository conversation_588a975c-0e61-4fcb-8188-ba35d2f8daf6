package com.aliyun.gts.utc.controller;

import com.aliyun.gts.utc.api.facade.response.statistics.QueryStatisticsResponse;
import com.aliyun.gts.utc.core.service.MessageStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
/**
 * @author: guoshengLi
 * @create: 2025-02-25 13:18
 * @Description:
 */
@Api(tags = "消息统计管理")
@RestController
@RequestMapping("/message/statistics")
public class MessageStatisticsController {

  @Resource
  private  MessageStatisticsService messageStatisticsService;


  /**
   * 消息推送请求数
   *
   * @return 消息推送请求数返回实体类
   */
  @GetMapping("/queryStatisticsRequest")
  @ApiOperation("消息推送请求数")
  public QueryStatisticsResponse queryMessageRequest(@RequestParam("days")int days) {
    return messageStatisticsService.queryMessageRequest(days);
  }

  /**
   * 消息推送次数
   *
   * @return 消息推送次数返回实体类
   */
  @GetMapping("/queryStatisticsPush")
  @ApiOperation("消息推送次数")
  public QueryStatisticsResponse queryMessagePush(@RequestParam("days")int days) {
    return messageStatisticsService.queryMessagePush(days);
  }
}
