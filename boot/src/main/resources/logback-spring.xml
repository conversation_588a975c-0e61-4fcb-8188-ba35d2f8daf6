<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <property name="APP_NAME" value="utc-umm-distribute"/>
    <property name="LOG_PATH" value="${LOG_ROOT:-./logs}/${APP_NAME}"/>
    <property name="ENCODING" value="UTF-8"/>
    <property name="MAX_HISTORY" value="3"/>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 控制台 appender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>${ENCODING}</charset>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
