spring:
  datasource:
    username: ${<PERSON><PERSON><PERSON><PERSON>_DB_MYSQL_USERNAME:root}
    password: ${DYGEV_DB_MYSQL_PASSWORD:rootmysql}
    url: jdbc:mysql://${DYGEV_DB_MYSQL_HOST:127.0.0.1}:${DYGEV_DB_MYSQL_PORT:3306}/${DYGEV_DB_MYSQL_DATABASE:yunqiao_msg}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&useSSL=false&autoReconnect=true&failOverReadOnly=false&allowPublicKeyRetrieval=true
    driver-class-name: ${DB_DRIVER:com.mysql.cj.jdbc.Driver}
  mail:
    host: ${MAIL_SERVER_HOST:smtp.alibaba-inc.com}
    username: ${MAIL_SERVER_USER_NAME:msg_yun<PERSON><PERSON>@alibaba-inc.com}
    password: ${MAIL_SERVER_PASSWORD:admin123}
    protocol: ${MAIL_PROTOCOL:smtp}
    default-encoding: utf-8
    port: ${MAIL_SERVER_PORT:465}
    properties:
      mail:
        smtp:
          auth: ${MAIL_PROPERTIES_MAIL_SMTP_AUTH:true}
          ssl:
            enable: ${MAIL_PROPERTIES_MAIL_SMTP_SSL_ENABLE:true}
          socketFactory:
            class: ${MAIL_PROPERTIES_MAIL_SMTP_SOCKETFACTORY:javax.net.ssl.SSLSocketFactory}
          starttls:
            enable: ${MAIL_PROPERTIES_MAIL_SMTP_START_TLS_ENABLE:true}
            required: ${MAIL_PROPERTIES_MAIL_SMTP_START_TLS_REQUIRED:true}
          timeout: ${MAIL_PROPERTIES_MAIL_SMTP_TIMEOUT:3000}
          connectionTimeout: ${MAIL_PROPERTIES_MAIL_SMTP_CONNECTION_TIMEOUT:3000}
          writetimeout: ${MAIL_PROPERTIES_MAIL_SMTP_CONNECTION_WRITETIMEOUT:3000}
  client:
    sms:
      plat: ${MSG_CLIENT_SMS_PLAT:}
      akid: ${MSG_CLIENT_SMS_AKID:}
      aksk: ${MSG_CLIENT_SMS_AKSK:}
      sign: ${MSG_CLIENT_SMS_SIGN:}

mock:
  send-mail: ${MOCK_SEND_MAIL_ADDRESS:<EMAIL>}

ribbon:
  ReadTimeout: 3000
  ConnectTimeout: 3000

logging:
  level:
    com.aliyun.gts.utc.dal.dao: info

gts:
  cache:
    type: redis
    host: ${DYGEV_REDIS_HOST}
    port: ${DYGEV_REDIS_PORT}
    auth: ${DYGEV_REDIS_PASSWORD}
    database: 0

#ThreadPool
threadpool:
  corePoolSize: 20
  maximumPoolSize: 80
  keepAliveTime: 1
  workQueueSize: 1024


dingtalk:
  codeConvertId: true
  domainName: ${DINGTALK_HOST}
  accessKey: ${DINGTALK_AK}
  secretKey: ${DINGTALK_SK}
  protocol: ${DINGTALK_PROTOCAL:https}

xxl:
  job:
    userName: ${XXL_JOB_USERNAME:admin}
    password: ${XXL_JOB_PASSWORD:123456}
    admin:
      # 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册
      addresses: ${XXL_JOB_ADMIN_ADDRESSES:http://cypc-xxl-job-admin.app.ingress.dxsp.tech:58080/xxl-job-admin}
      # 执行器通讯TOKEN [选填]：非空时启用
    accessToken:  ${XXL_JOB_ACCESSTOKEN:}
    executor:
      # 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      appname: ${XXL_JOB_EXECUTOR_APPNAME:tmingMessageJobHandler}
      # 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      address: ${XXL_JOB_EXECUTOR_ADDRESS:}
      # 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      ip: ${XXL_JOB_EXECUTOR_IP:}
      # 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      port: ${XXL_JOB_EXECUTOR_PORT:9999}
      # 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: ${XXL_JOB_EXECUTOR_LOGPATH:/data/applogs/xxl-job/jobhandler}
      # 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      logretentiondays: ${XXL_JOB_EXECUTOR_LOGRETENTIONDAYS:30}
    client:
      jobGroupId: ${XXL_JOB_CLIENT_JOBGROUPID:1}
      addUrl: ${xxl.job.admin.addresses}/jobinfo/add
      removeUrl: ${xxl.job.admin.addresses}/jobinfo/remove
      startJobUrl: ${xxl.job.admin.addresses}/jobinfo/start
      stopJobUrl: ${xxl.job.admin.addresses}/jobinfo/stop
      pageListUrl: ${xxl.job.admin.addresses}/jobinfo/pageList
      loginUrl: ${xxl.job.admin.addresses}/login

third:
  smsUrl: ${THIRD_SMSURL:dysmsapi.aliyuncs.com}
  apiKey: ${THIRD_APIKEY:thirdapiKey}
  sxxyUrl: ${THIRD_SXXYURL:https://qywx.ctg.com.cn:50443/cgi-bin}


