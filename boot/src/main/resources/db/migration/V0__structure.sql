CREATE TABLE IF NOT EXISTS umm_category (
  id bigint(11) NOT NULL AUTO_INCREMENT COMMENT '栏目id',
  guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '外部id',
  category_code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '栏目编码',
  category_name varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '栏目名称',
  parent_id bigint(11) DEFAULT NULL COMMENT '上级栏目id',
  status int(1) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  create_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人id',
  create_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人名称',
  update_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人id',
  update_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人名称',
  gmt_create datetime DEFAULT NULL COMMENT '创建时间',
  gmt_modify datetime DEFAULT NULL COMMENT '修改时间',
  features json DEFAULT NULL COMMENT '所有外部系统冗余字段，以json形式存储',
  is_deleted bigint(11) DEFAULT '0' COMMENT '逻辑删除',
  version int(8) DEFAULT NULL COMMENT '版本号',
  app_id bigint(11) DEFAULT NULL COMMENT '应用id',
  tenant_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT 'default' COMMENT '租户编码',
  PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='消息栏目表';



CREATE TABLE IF NOT EXISTS  umm_message (
  id bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  tpl_id bigint(11) unsigned DEFAULT NULL COMMENT '模板id',
  guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '外部id',
  msg_title varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '消息标题',
  msg_content text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '消息内容',
  param json DEFAULT NULL COMMENT '发送参数,json存储',
  send_method int(1) DEFAULT NULL COMMENT '1.实时发送|2.定时发送',
  send_time datetime DEFAULT NULL COMMENT '分发时间，定时发送时的时间',
  actual_send_time datetime DEFAULT NULL COMMENT '实际发送时间',
  category_id bigint(11) DEFAULT NULL COMMENT '栏目ID',
  status int(1) NOT NULL DEFAULT '1' COMMENT '状态（0草稿；1发送中；2部分成功；3全部成功；4发送失败；5待审批；6审批未通过；7已撤销）',
  create_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人id',
  create_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人名称',
  update_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人id',
  update_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人名称',
  gmt_create datetime DEFAULT NULL COMMENT '创建时间',
  gmt_modify datetime DEFAULT NULL COMMENT '修改时间',
  features json DEFAULT NULL COMMENT '所有外部系统冗余字段，以json形式存储',
  is_deleted bigint(11) DEFAULT '0' COMMENT '逻辑删除',
  version int(8) DEFAULT NULL COMMENT '版本号',
  app_id bigint(11) DEFAULT NULL COMMENT '应用id',
  tenant_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT 'default' COMMENT '租户编码',
  total_num int(8) DEFAULT NULL COMMENT '消息发送总数',
  success_num int(8) DEFAULT NULL COMMENT '消息发送成功数',
  PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='消息记录表';


CREATE TABLE IF NOT EXISTS  umm_msg_channel (
  id bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  msg_id bigint(11) unsigned NOT NULL COMMENT 'msg主键id',
  channel_id bigint(11) unsigned NOT NULL COMMENT '消息渠道ID',
  start_date date DEFAULT NULL COMMENT '生效日期',
  end_date date DEFAULT NULL COMMENT '失效日期',
  tenant_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT 'default' COMMENT '租户编码',
  app_id bigint(11) DEFAULT NULL COMMENT '应用id',
  status int(1) NOT NULL DEFAULT '1' COMMENT '1启用 2禁用',
  PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='消息发送渠道表';


CREATE TABLE IF NOT EXISTS  umm_msg_parameter (
  id bigint(11) NOT NULL AUTO_INCREMENT COMMENT '参数id',
  guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '外部id',
  param_name varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '系统参数名称',
  param_exp varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '系统参数标识',
  status int(1) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  create_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人id',
  create_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人名称',
  update_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人id',
  update_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人名称',
  gmt_create datetime DEFAULT NULL COMMENT '创建时间',
  gmt_modify datetime DEFAULT NULL COMMENT '修改时间',
  features json DEFAULT NULL COMMENT '所有外部系统冗余字段，以json形式存储',
  is_deleted bigint(11) DEFAULT '0' COMMENT '逻辑删除',
  version int(8) DEFAULT NULL COMMENT '版本号',
  app_id bigint(11) DEFAULT NULL COMMENT '应用id',
  tenant_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT 'default' COMMENT '租户编码',
  PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='消息栏目参数表';

CREATE TABLE IF NOT EXISTS  umm_msg_receiver (
  id bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  msg_id bigint(11) unsigned NOT NULL COMMENT 'msg主键id',
  receiver_type char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '接收类型 USR:用户  ORG:组织  POS:单位',
  receiver_guid varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '接收标识',
  start_date date DEFAULT NULL COMMENT '生效',
  end_date date DEFAULT NULL COMMENT '失效日期',
  tenant_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT 'default' COMMENT '租户编码',
  app_id bigint(11) DEFAULT NULL COMMENT '应用id',
  status int(1) NOT NULL DEFAULT '1' COMMENT '1启用 2禁用',
  PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='消息接收对象表';


CREATE TABLE IF NOT EXISTS  umm_msg_send (
  id bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '外部id',
  user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '用户guid',
  user_code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '用户编码',
  user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '用户名称',
  user_org text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs COMMENT '用户归属组织',
  user_postion text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs COMMENT '用户归属岗位',
  email varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '邮件',
  phone varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '电话',
  msg_id varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '消息id',
  msg_title varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '消息标题',
  msg_content text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '消息内容',
  channel_id bigint(11) unsigned DEFAULT NULL COMMENT '消息渠道ID',
  send_time datetime DEFAULT NULL COMMENT '发送时间',
  status int(1) NOT NULL DEFAULT '1' COMMENT '1.未发送,2.发送中，3.发送成功，4.发送失败',
  read_status int(1) NOT NULL DEFAULT '0' COMMENT '阅读状态 0：未读 1：已读',
  read_time datetime DEFAULT NULL COMMENT '阅读时间',
  create_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人id',
  create_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人名称',
  update_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人id',
  update_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人名称',
  gmt_create datetime DEFAULT NULL COMMENT '创建时间',
  gmt_modify datetime DEFAULT NULL COMMENT '修改时间',
  features json DEFAULT NULL COMMENT '所有外部系统冗余字段，以json形式存储',
  is_deleted bigint(11) DEFAULT '0' COMMENT '逻辑删除',
  version int(8) DEFAULT NULL COMMENT '版本号',
  app_id bigint(11) DEFAULT NULL COMMENT '应用id',
  tenant_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT 'default' COMMENT '租户编码',
  retry_rule varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '重发规则',
  source_id varchar(64) COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '业务ID',
  PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='消息发送记录表';


CREATE TABLE IF NOT EXISTS  umm_msg_send_log (
  id bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  msg_send_id bigint(11) unsigned NOT NULL COMMENT '消息发送数据id',
  message_in mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs COMMENT '消息请求request',
  message_out mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs COMMENT '消息响应response',
  send_time datetime DEFAULT NULL COMMENT '发送时间',
  status int(1) NOT NULL DEFAULT '3' COMMENT '3.发送成功，4.发送失败',
  gmt_create datetime DEFAULT NULL COMMENT '创建时间',
  gmt_modify datetime DEFAULT NULL COMMENT '修改时间',
  tenant_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT 'default' COMMENT '租户编码',
  PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='消息发送日志表';

CREATE TABLE IF NOT EXISTS  umm_template (
  id bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '外部id',
  tpl_code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '模板编码',
  tpl_title varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '模板标题',
  tpl_content text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '模板内容',
  param json DEFAULT NULL COMMENT '发送参数json存储，存储模板中涉及到的参数，存储数组',
  category_id bigint(11) DEFAULT NULL COMMENT '栏目ID',
  support_channels json DEFAULT NULL COMMENT '支持的channels, 存储channel id的数组',
  status int(1) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  create_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人id',
  create_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人名称',
  update_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人id',
  update_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人名称',
  gmt_create datetime DEFAULT NULL COMMENT '创建时间',
  gmt_modify datetime DEFAULT NULL COMMENT '修改时间',
  features json DEFAULT NULL COMMENT '所有外部系统冗余字段，以json形式存储',
  is_deleted bigint(11) DEFAULT '0' COMMENT '逻辑删除',
  version int(8) DEFAULT NULL COMMENT '版本号',
  app_id bigint(11) DEFAULT NULL COMMENT '应用id',
  tenant_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT 'default' COMMENT '租户编码',
  PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='消息模板';

alter table umm_template add KEY `idx_code_deleted` (`tpl_code`,`is_deleted`);
alter table umm_category add KEY `idx_category_code_deleted` (`category_code`,`is_deleted`);
alter table umm_msg_receiver add KEY `idx_msg_id` (`msg_id`);
alter table umm_msg_channel add KEY `idx_msg_id` (`msg_id`);
alter table umm_message add KEY `idx_modify_deleted` (`is_deleted`, `tenant_code`, `gmt_modify`);
