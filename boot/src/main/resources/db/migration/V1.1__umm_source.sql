CREATE TABLE IF NOT EXISTS  umm_source (
    id bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs NOT NULL COMMENT '外部id',
    source_code varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '消息源编码',
    source_name varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '消息源名称',
    source_type varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '消息源类型',
    source_param json DEFAULT NULL COMMENT '消息源参数json存储',
    create_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人id',
    create_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '创建人名称',
    update_user_guid varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人id',
    update_user_name varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT NULL COMMENT '修改人名称',
    gmt_create datetime DEFAULT NULL COMMENT '创建时间',
    gmt_modify datetime DEFAULT NULL COMMENT '修改时间',
    features json DEFAULT NULL COMMENT '所有外部系统冗余字段，以json形式存储',
    is_deleted bigint(11) DEFAULT '0' COMMENT '逻辑删除',
    version int(8) DEFAULT NULL COMMENT '版本号',
    app_id bigint(11) DEFAULT NULL COMMENT '应用id',
    tenant_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_as_cs DEFAULT 'default' COMMENT '租户编码',
    PRIMARY KEY (id) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_as_cs COMMENT='消息源';

alter table umm_template
    add column source_id bigint(11) COMMENT '消息源id';