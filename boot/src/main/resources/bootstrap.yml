spring:
  profiles:
    active: ${DAYUBOOT_ENV:dev}
  application:
    name: yunqiao-msg
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
  session:
    store-type: redis
  redis:
    database: ${DYGEV_REDIS_DATABASE:0}
    host: ${DYGEV_REDIS_HOST:127.0.0.1}
    username: ${DYGEV_REDIS_USERNAME:}
    password: ${DYGEV_REDIS_PASSWORD:}
    port: ${DYGEV_REDIS_PORT:6379}
  flyway:
    # 是否启用flyway
    enabled: true
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: classpath:db/migration
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: false
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    #是否要替换Sql中的变量
    placeholderReplacement: false
  cloud:
    nacos:
      discovery:
        enabled: ${NACOS_DISCOVERY_ENABLED:false}
        namespace: ${NACOS_DISCOVERY_NAMESPACE:}
      config:
        enabled: false
      server-addr: ${NACOS_SERVER_ADDR:localhost:8848}
  web:
    cors:
      allowed-origin-patterns:
        - ${CORS_ALLOWED_PATTERNS:http://*.dxsp.tech}
gts:
  common:
    swagger:
      enable: false
server:
  port: 8080
  servlet:
    context-path: /umm/distribute
    session:
      cookie:
        path: /
  time-zone: Asia/Shanghai

mybatis-plus:
  mapper-locations:
    - classpath*:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: AUTO
    banner: false

swagger:
  enable: ${SWAGGER_ENABLED:false}

management:
  endpoints:
    enabled-by-default: false #不建议设置为true, 可能有安全漏洞风险
  endpoint:
    #info:
    #  enabled: true
    health:
      enabled: true #健康检查
      #show-details: always

yunqiao:
  foura:
    enable: ${YUNQIAO_FOURA_ENABLE:true}
    mode: ${YUNQIAO_FOURA_MODE:gateway}
    foura-service-url: ${YUNQIAO_FOURA_SERVICE_URL:http://127.0.0.1:8088}
    sso-page-url: ${YUNQIAO_FOURA_SSO_PAGE_URL:http://localhost:8200}
    sso-server-url: ${YUNQIAO_FOURA_SSO_SERVER_URL:http://127.0.0.1:8088}
    sso-callback-key: callbackUrl
    sso-token-key: ticket
    sso-logout-path: /logout
    sso-login-type: ${YUNQIAO_FOURA_LOGIN_TYPE:4A}
    sso-exclude-paths: ${YUNQIAO_EXCLUDE_PATHS:}
    sso-client-url: ${YUNQIAO_FOURA_SERVICE_URL:}
    sso-login-path: /login
    foura-app-id: ${YUNQIAO_FOURA_APP_ID:}
    audit-mode: ${YUNQIAO_FOURA_AUDIT_MODE:} #custom 开启审计，其他值则不起作用

foura:
  app:
    id: ${YUNQIAO_FOURA_APP_ID:}
  service:
    endpoint: ${YUNQIAO_FOURA_SERVICE_URL:}
  audit:
    remote:
      enable: ${YUNQIAO_FOURA_AUDIT_ENABLE:true}
global:
  system:
    admin-user-id: ${ADMIN_USER_ID:52c1dfde391f4f63b5f84e0654c06137}


