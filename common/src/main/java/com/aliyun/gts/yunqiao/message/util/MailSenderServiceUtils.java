package com.aliyun.gts.yunqiao.message.util;

import com.aliyun.gts.yunqiao.message.boot.MailProperties;
import com.aliyun.gts.yunqiao.message.request.MailMessageRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.internet.MimeMessage;
import java.util.Date;
import java.util.Optional;
import java.util.Properties;

/**
 * Created by 鸿度 on 2023-02-20 19:18:07
 */
@Component
public class MailSenderServiceUtils implements EnvironmentAware {

    private static Environment environment;

    public static MimeMessage buildMimeMessage(MailMessageRequest request, JavaMailSender javaMailSender) throws Exception {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
        mimeMessageHelper.setSubject(request.getSubject());
        mimeMessageHelper.setText(request.getContent(), request.isHtml());
        mimeMessageHelper.setSentDate(new Date());
        if (StringUtils.isNotBlank(request.getSubmitter())) {
            mimeMessageHelper.setFrom(request.getFrom(), request.getSubmitter());
        } else {
            mimeMessageHelper.setFrom(request.getFrom());
        }
        mimeMessageHelper.setTo(request.getTo());
        return mimeMessage;
    }


    public static void applyProperties(JavaMailSender mailSender, MailProperties mailProperties) {
        JavaMailSenderImpl sender = (JavaMailSenderImpl) mailSender;
        sender.setHost(mailProperties.getHost());
        if (mailProperties.getPort() != null) {
            sender.setPort(mailProperties.getPort());
        }
        sender.setUsername(mailProperties.getUsername());
        sender.setPassword(mailProperties.getPassword());
        if (mailProperties.getProtocol() != null && !mailProperties.getProtocol().isEmpty()) {
            sender.setProtocol(mailProperties.getProtocol());
        } else {
            sender.setProtocol(JavaMailSenderImpl.DEFAULT_PROTOCOL);
        }
        sender.setDefaultEncoding("UTF-8");
        Properties javaMailProperties = new Properties();
        fillFromSpringConfig(javaMailProperties, "mail.smtp.auth", "true");
        if (mailProperties.getSslEnable() != null) {
            String value = mailProperties.getSslEnable().toString();
            javaMailProperties.setProperty("mail.smtp.ssl.enable", value);
        }
        fillFromSpringConfig(javaMailProperties, "mail.smtp.starttls.enable", "false");
        fillFromSpringConfig(javaMailProperties, "mail.smtp.starttls.required", "false");
        // 设置发送超时时间
        javaMailProperties.setProperty("mail.smtp.timeout", mailProperties.getTimeout());
        // 设置连接超时时间
        javaMailProperties.setProperty("mail.smtp.connectionTimeout", mailProperties.getConnectionTimeout());

        fillFromSpringConfig(javaMailProperties, "mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        sender.setJavaMailProperties(javaMailProperties);
    }

    private static void fillFromSpringConfig(Properties javaMailProperties, String key, String defaultValue) {
        javaMailProperties.setProperty(key,
                environment.getProperty("spring.mail.properties." + key, defaultValue)
        );
    }

    public void setEnvironment(Environment environment) {
        MailSenderServiceUtils.environment = environment;
    }
}
