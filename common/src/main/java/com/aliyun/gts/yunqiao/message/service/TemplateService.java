package com.aliyun.gts.yunqiao.message.service;

import com.aliyun.gts.yunqiao.message.request.MailMessageRequest;
import freemarker.cache.StringTemplateLoader;
import freemarker.core.TemplateClassResolver;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.commons.text.StringEscapeUtils;

import java.io.StringWriter;
import java.util.Map;
import java.util.UUID;

/**
 * Created by 鸿度 on 2023-01-31 11:08:10
 */
public class TemplateService {

    public void renderTplContent(MailMessageRequest req) throws Exception {
        Map<String, Object> tplVarsMap = req.getTplVarsMap();
        if (tplVarsMap != null && tplVarsMap.size() > 0) {
            String subject = render(req.getSubject(), tplVarsMap);
            req.setSubject(subject);
            String content = render(req.getContent(), tplVarsMap);
            req.setContent(content);
        }
    }

    String render(String tplContent, Map<String, Object> variables) throws Exception {
        tplContent = StringEscapeUtils.escapeHtml4(tplContent);
        for (String varKey : variables.keySet()) {
            tplContent = tplContent.replaceAll(varKey, varKey + "?html");
        }
        String uuid = UUID.randomUUID().toString().replace("-", "");
        Template template = new Template(uuid, tplContent, configuration());
        StringWriter stringWriter = new StringWriter();
        template.process(variables, stringWriter);
        return stringWriter.toString();
    }

    private Configuration configuration() {
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
        StringTemplateLoader templateLoader = new StringTemplateLoader();
        configuration.setTemplateLoader(templateLoader);
        configuration.setDefaultEncoding("UTF-8");
        configuration.setNewBuiltinClassResolver(TemplateClassResolver.SAFER_RESOLVER);
        return configuration;
    }
}
