package com.aliyun.gts.yunqiao.message.boot;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * Created by 鸿度 on 2023-01-30 17:05.
 */
@ConfigurationProperties(prefix = "yunqiao.message", ignoreInvalidFields = true)
@Data
public class MessageProperties {

    private boolean enabled = true;

    @NestedConfigurationProperty
    private MailProperties mail = new MailProperties();

    @NestedConfigurationProperty
    private SMSProperties sms = new SMSProperties();
}
